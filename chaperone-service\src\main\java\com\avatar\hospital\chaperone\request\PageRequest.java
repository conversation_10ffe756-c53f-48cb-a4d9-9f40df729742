package com.avatar.hospital.chaperone.request;

import com.avatar.hospital.chaperone.utils.ExportParamDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class PageRequest implements Serializable {

    private static final long serialVersionUID = -1537431942438821886L;

    /**
     * 页码
     * @mock 1
     */
    @NotNull
    private Integer pageIndex;

    /**
     * 页长
     * @mock 10
     */
    @NotNull
    private Integer pageSize;


    public <T> Page<T> ofPage() {
        Page<T> page = PageDTO.of(pageIndex, pageSize);
        return page;
    }

    public Integer offset() {
        return (pageIndex - 1) * pageSize;
    }

    public Long getPageIndexLong() {
        if (Objects.isNull(pageIndex)) {
            return 1L;
        }
        return pageIndex.longValue();
    }

    public Long getPageSizeLong() {
        if (Objects.isNull(pageSize)) {
            return 10L;
        }
        return pageSize.longValue();
    }

}
