package com.avatar.hospital.chaperone.request.part;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 13:15
 */
@Data
public class PartBatchRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = -5481496257106547961L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 批次编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 可用数量
     */
    private Integer balance;

    /**
     * 单价
     */
    private Integer price;

    /**
     * 数量单位
     */
    private String unit;

    /**
     * 状态（1-待入库，2-已入库）
     */
    private Integer status;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
