package com.avatar.hospital.chaperone.job.order;

import com.avatar.hospital.chaperone.TestApplication;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderNursingDO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-06 11:54
 **/
@SpringBootTest(classes = TestApplication.class)
public class NursingOrderDayExecTest {

    @Autowired
    private NursingOrderDayExec nursingOrderDayExec;

    /**
     * 給护工添加排班信息
     */
    @Test
    public void testAddOne() {
        nursingOrderDayExec.addOrderBind(358090115169869824L,"护工2",358129422710562816L,
                20231216,20231218);
    }

    /**
     * 給护工添加排班信息,不添加绑定关系
     */
    @Test
    public void testAddOneNoOrder() {
        nursingOrderDayExec.addOrderBind(358090115169869824L,"护工2",null,
                20231227,20231228);
    }


    /**
     * 給护工添加排班信息,不添加绑定关系
     */
    @Test
    public void testAddOneNoOrder2() {
        OrderNursingDO nursingDO = new OrderNursingDO();
        nursingDO.setNursingId(358090115169869824L);
        nursingDO.setNursingName("护工2");

        List<OrderNursingDO> list = new ArrayList<>();
        list.add(nursingDO);
        nursingOrderDayExec.addList(Boolean.TRUE,list,null,
                20231227,20231228);
    }


    /**
     * 給护工添加排班信息,不添加绑定关系
     */
    @Test
    public void testDelNursingOrder() {
        nursingOrderDayExec.delNursingOrder(441489137289011200L,
                20231227);
    }

    @Test
    public void testAddAll() {
        nursingOrderDayExec.addNursingBindOrder(358090115169869824L,"护工2",
                20240101,20240131);
    }
}
