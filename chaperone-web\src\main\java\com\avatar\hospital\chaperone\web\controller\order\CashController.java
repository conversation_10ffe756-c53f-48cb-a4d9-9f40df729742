package com.avatar.hospital.chaperone.web.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.job.JobTaskRegister;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.CashBalanceResponse;
import com.avatar.hospital.chaperone.response.order.CashIdResponse;
import com.avatar.hospital.chaperone.response.order.CashResponse;
import com.avatar.hospital.chaperone.service.order.CashService;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.web.validator.order.OrderValidator;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

/**
 * B端现金归档
 * @program: hospital-chaperone
 * @description: 套餐
 * @author: sp0372
 * @create: 2023-10-13 10:07
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/cashlog")
public class CashController {
    private final CashService cashService;
    private final JobTaskRegister jobTaskRegister;

    /**
     * 查询
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<CashResponse>> paging(@RequestBody CashPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return cashService.paging(request);
        });
    }

    /**
     * 修改-提取
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.CASH_EXTRACT_PREFIX + " + #request.operatorUser.id")
    @PostMapping("extract")
    public SingleResponse<CashIdResponse> extract(@RequestBody CashExtractRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.extractByCash(request);
            return cashService.extract(request);
        });
    }

    /**
     * 修改-存入
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.CASH_DEPOSIT_PREFIX + " + #request.operatorUser.id")
    @PostMapping("deposit")
    public SingleResponse<CashIdResponse> deposit(@RequestBody CashDepositRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.extractByCash(request);
            return cashService.deposit(request);
        });
    }

    /**
     * 查询余额
     * @param request
     * @return
     */
    @PostMapping("balance")
    public SingleResponse<CashBalanceResponse> balance(@RequestBody CashBalanceRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return cashService.balance(request);
        });
    }

    /**
     * 补偿-人工触发定时任务
     * @return 防止定时任务执行失败,人工补偿线上数据
     */
    @GetMapping("compensate/job/invoke")
    public SingleResponse<Boolean> compensateJobInvoke(@RequestParam(value = "sign",required = false) String sign,
                                                       @RequestParam(value = "code") String code,
                                                       @RequestParam(value = "objects",required = false) Object[] objects) {
        return TemplateProcess.doProcess(log, () -> {
            if (!Objects.equals("sdafas0z2343 0daf",sign)) {
                return Boolean.FALSE;
            }
            return jobTaskRegister.invoke(code,objects);
        });
    }

    /**
     * 补偿-所有任务返回
     * @return
     */
    @GetMapping("compensate/job/all")
    public SingleResponse<Map<String,String>> compensateJobAll(@RequestParam(value = "sign",required = false) String sign) {
        return TemplateProcess.doProcess(log, () -> {
            if (!Objects.equals("sdafas0z2343 0daf",sign)) {
                return Maps.newHashMap();
            }
            return jobTaskRegister.all();
        });
    }
}
