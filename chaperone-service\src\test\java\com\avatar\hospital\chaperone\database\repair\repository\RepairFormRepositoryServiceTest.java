package com.avatar.hospital.chaperone.database.repair.repository;

import com.avatar.hospital.chaperone.TestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 11:18
 **/
@SpringBootTest(classes = TestApplication.class)
public class RepairFormRepositoryServiceTest {
    @Autowired
    private RepairFormRepositoryService repairFormRepositoryService;

    @Test
    public void test() {
        /*Map<Integer, Integer> integerIntegerMap = repairFormRepositoryService.statisticsRepairStatusFinish(2023);
        System.out.printf("integerIntegerMap," + integerIntegerMap);*/
    }

}
