package com.avatar.hospital.chaperone.database.nursing.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum NursingLeaveStatus {
    NONE(-1, "未知"),
    NORMAL(0, "正常"),
    LEAVE(1, "请假"),
    ;

    private final Integer status;

    private final String describe;


    NursingLeaveStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static NursingLeaveStatus of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (NursingLeaveStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
