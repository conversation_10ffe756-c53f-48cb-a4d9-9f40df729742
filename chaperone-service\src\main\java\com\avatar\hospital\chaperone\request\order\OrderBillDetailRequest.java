package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillDetailRequest implements OperatorReq {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 账单ID
     */
    private Long billId;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
