package com.avatar.hospital.chaperone.consumer.configurer;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
@Slf4j
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SaInterceptor(handler -> {
            // 匿名可访问
            SaRouter.match("/**")
                    .notMatch("/api/v1/consumer/login")
                    .notMatch("/api/v1/consumer/login/test")
                    .notMatch("/api/v1/consumer/notify/wx")
                    .check(r -> StpUtil.checkLogin());

        })).addPathPatterns("/**");
    }

}
