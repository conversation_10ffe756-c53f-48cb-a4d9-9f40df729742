package com.avatar.hospital.chaperone.utils;


import com.avatar.hospital.chaperone.enums.IEnumConvert;

import java.lang.annotation.*;

/**
 * @program: hospital-chaperone
 * @description: 别名
 * @author: sp0372
 * @create: 2023-10-12 13:11
 **/
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@Documented
public @interface CompareAlias {
    String value() default "";

    /**
     * 枚举值
     *   参数转换 转换可以显示
     * @return
     */
    Class<? extends IEnumConvert> clazz() default IEnumConvert.class;

    /**
     * 改字段对比是否忽略
     * @return
     */
    boolean ignore() default false;

    /**
     * 强制记录2遍差异，一样也记
     * @return
     */
    boolean force() default false;

}
