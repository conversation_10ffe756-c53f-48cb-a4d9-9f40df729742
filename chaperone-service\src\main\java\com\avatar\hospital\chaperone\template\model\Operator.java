package com.avatar.hospital.chaperone.template.model;

import lombok.Data;

import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 操作人
 * @author: sp0372
 * @create: 2023-10-23 15:00
 **/
@Data
public class Operator {

    /**
     * 操作人ID
     */
    private Long id;

    /**
     * 操作人手机号
     */
    private String mobile;

    /**
     * 操作人姓名
     */
    private String name;

    /**
     * 操作人来源 1 c端 2 B端 3 定时任务
     */
    private Integer source;

    /**
     * 是否是c端用户
     * @return
     */
    public Boolean isSourceC() {
        return Objects.equals(1,source);
    }

    /**
     * 是否是B端用户
     * @return
     */
    public Boolean isSourceB() {
        return Objects.equals(2,source);
    }

    public final static Operator of(Long id,String mobile,String name,Integer source) {
        Operator operator = new Operator();
        operator.setId(id);
        operator.setMobile(mobile);
        operator.setName(name);
        operator.setSource(source);
        return operator;
    }
}
