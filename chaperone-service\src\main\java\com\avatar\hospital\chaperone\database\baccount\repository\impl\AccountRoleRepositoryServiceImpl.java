package com.avatar.hospital.chaperone.database.baccount.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountRoleDO;
import com.avatar.hospital.chaperone.database.baccount.mapper.AccountRoleMapper;
import com.avatar.hospital.chaperone.database.baccount.repository.AccountRoleRepositoryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户角色关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Service
public class AccountRoleRepositoryServiceImpl extends ServiceImpl<AccountRoleMapper, AccountRoleDO> implements AccountRoleRepositoryService {

    @Override
    public List<AccountRoleDO> findByRoleIds(Set<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AccountRoleDO> queryWrapper = queryWrapper();
        if (roleIds.size() == 1) {
            queryWrapper.eq(AccountRoleDO::getRoleId, roleIds.toArray()[0]);
        }
        else {
            queryWrapper.in(AccountRoleDO::getRoleId, roleIds);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<AccountRoleDO> findByAccountIds(Set<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AccountRoleDO> queryWrapper = queryWrapper();
        if (accountIds.size() == 1) {
            queryWrapper.eq(AccountRoleDO::getAccountId, accountIds.toArray()[0]);
        }
        else {
            queryWrapper.in(AccountRoleDO::getAccountId, accountIds);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public Boolean deleteByRoleIds(Set<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        LambdaUpdateWrapper<AccountRoleDO> updateWrapper = updateWrapper();
        if (roleIds.size() == 1) {
            updateWrapper.eq(AccountRoleDO::getRoleId, roleIds.toArray()[0]);
        }
        else {
            updateWrapper.in(AccountRoleDO::getRoleId, roleIds);
        }
        return remove(updateWrapper);
    }

    @Override
    public Boolean deleteByAccountIds(Set<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return false;
        }
        LambdaUpdateWrapper<AccountRoleDO> updateWrapper = updateWrapper();
        if (accountIds.size() == 1) {
            updateWrapper.eq(AccountRoleDO::getAccountId, accountIds.toArray()[0]);
        }
        else {
            updateWrapper.in(AccountRoleDO::getAccountId, accountIds);
        }
        return remove(updateWrapper);
    }


    private LambdaQueryWrapper<AccountRoleDO> queryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    private LambdaUpdateWrapper<AccountRoleDO> updateWrapper() {
        return new LambdaUpdateWrapper<>();
    }
}
