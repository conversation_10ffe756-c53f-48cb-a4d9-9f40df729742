package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderRefundDO;
import com.avatar.hospital.chaperone.database.order.mapper.OrderRefundMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderRefundRepositoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 订单-退款记录; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class OrderRefundRepositoryServiceImpl extends ServiceImpl<OrderRefundMapper, OrderRefundDO> implements OrderRefundRepositoryService {

}
