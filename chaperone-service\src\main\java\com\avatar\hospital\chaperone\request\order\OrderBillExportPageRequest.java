package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.request.ExportPageRequest;
import com.avatar.hospital.chaperone.service.order.dto.OrderBillExportDTO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillExportPageRequest extends ExportPageRequest implements OperatorReq {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 支付方式
     */
    private Integer tradeType;

    /**
     * 开始时间 结束时间    yyyy-MM-dd HH:mm:ss
     */
    private List<String> createTime;

    /**
     * 是否筛选退款账单 null 0 否 1 是
     */
    private Integer filterRefundBill;


    /* *//**
     * 开始时间 yyyy-MM-dd
     *//*
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    *//**
     * 结束时间 yyyy-MM-dd
     *//*
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;
*/
    /**
     * 操作用户
     */
    private Operator operatorUser;

    @Override
    public Class getClazz() {
        return OrderBillExportDTO.class;
    }

    @Override
    public String getFileName() {
        return "账单";
    }
}
