package com.avatar.hospital.chaperone.database.plan.repository;

import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanRefOrgDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.repository.base.PlanRefRepositoryService;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Set;

/**
 * <p>
 * 维保计划关联部门 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface MaintenancePlanRefOrgRepositoryService extends IService<MaintenancePlanRefOrgDO> , PlanRefRepositoryService {

}
