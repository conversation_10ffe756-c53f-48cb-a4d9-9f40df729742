package com.avatar.hospital.chaperone.service.part;

import com.avatar.hospital.chaperone.request.part.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartApplyIdResponse;
import com.avatar.hospital.chaperone.response.part.PartApplyResponse;

/**
 * @program: hospital-chaperone
 * @description: 备件审核
 * @author: sp0372
 * @create: 2023-10-27 10:37
 **/
public interface PartApplyService {
    // B端

    /**
     * 备件使用审核列表
     */
    PageResponse<PartApplyResponse> paging(PartApplyPageRequest request);

    /**
     * 备件使用审核通过
     *      * 更新申请单状态
     *      * 选择N个备件,设置状态(按id正序排序)
     *      * 更新批次余额
     */
    PartApplyIdResponse pass(PartApplyPassRequest request);

    /**
     * 备件使用审核拒绝
     */
    PartApplyIdResponse refuse(PartApplyRefuseRequest request);

}
