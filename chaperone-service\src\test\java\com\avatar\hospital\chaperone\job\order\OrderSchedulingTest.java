package com.avatar.hospital.chaperone.job.order;

import com.avatar.hospital.chaperone.TestApplication;
import com.avatar.hospital.chaperone.utils.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-06 09:31
 **/
@SpringBootTest(classes = TestApplication.class)
public class OrderSchedulingTest {

    @Autowired
    private OrderScheduling orderScheduling;

    /**
     * 测试消费记录生成
     */
    @Test
    public void testConsumerLog() {
        Integer hour = DateUtils.hour();
        Integer date = DateUtils.dateInt();
        orderScheduling.consumerLog(20231228,0);
    }

    @Test
    public void testResetOrderNursingByPre() {
        orderScheduling.resetOrderNursingByPre(2023110611);
    }

    @Test
    public void testOrderPayMessageByEveryDay() {
        orderScheduling.orderPayMessageByEveryDay(528079560627167232L);
    }

}
