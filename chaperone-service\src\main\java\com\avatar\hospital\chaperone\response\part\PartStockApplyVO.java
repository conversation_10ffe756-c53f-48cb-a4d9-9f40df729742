package com.avatar.hospital.chaperone.response.part;

import com.avatar.hospital.chaperone.annotation.serializer.LocalDateTimeDeserializer;
import com.avatar.hospital.chaperone.annotation.serializer.LocalDateTimeSerializer;
import com.avatar.hospital.chaperone.common.Attachment;
import com.avatar.hospital.chaperone.request.part.PartStockApplyRequest;
import com.avatar.hospital.chaperone.response.BaseVO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 备品备件入库审批单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
public class PartStockApplyVO extends BaseVO implements Serializable {


    private static final long serialVersionUID = -8339747083155485353L;
    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 审批单编号
     */
    private String code;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态（1-通过，2-驳回，0-待审批）
     *
     * @see com.avatar.hospital.chaperone.database.part.enums.PartStockApplyStatusType
     */
    private Integer status;

    /**
     * 名称（物品名称+数量）
     */
    private String name;

    /**
     * 出资方类型（1-物业，2-医院）
     */
    private Integer investorType;

    /**
     * 出资方机构ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;

    /**
     * 出资方机构名称
     */
    private String investor;
    /**
     * 入库时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime entryTime;
    /**
     * 附件URL,数组
     */
    private List<Attachment> attachments;

    /**
     * 关联的批次
     */
    private List<PartStockApplyRequest.PartBatch> PartBatchs;

    /**
     * 操作人
     */
    private String operator;
}
