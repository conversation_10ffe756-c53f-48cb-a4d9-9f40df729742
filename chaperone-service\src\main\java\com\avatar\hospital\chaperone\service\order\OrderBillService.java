package com.avatar.hospital.chaperone.service.order;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderPayDO;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.dto.OrderBillExportDTO;

import java.util.List;

/**
 * @program: hospital-chaperone
 * @description: 订单账单明细
 * @author: sp0372
 * @create: 2023-10-11 16:57
 **/
public interface OrderBillService {

    /**
     * C端 账单列表查询
     * @param request
     * @return
     */
    PageResponse<OrderBillResponse> pagingForC(OrderBillCPageRequest request);

    /**
     * B端 账单列表查询
     * @param request
     * @return
     */
    PageResponse<OrderBillBResponse> paging(OrderBillPageRequest request);

    /**
     * B端 账单列表导出
     * @param request
     * @return
     */
    List<OrderBillExportDTO> exportPaging(OrderBillExportPageRequest request);


    /**
     * 创建一条账单记录
     * @param request
     * @return
     */
    OrderBillIdResponse create(OrderBillCreateRequest request);

    /**
     * 查询指定范围内所有账单
     *   排除总账单
     * @param request
     * @return
     */
    List<OrderBillResponse> findAll(OrderBillFindAllRequest request);

    /**
     * 修改账单类型
     *     预付单
     *        生产一条预付单
     *     周期账单
     *        只修改信息
     * @param request
     * @return
     */
    OrderBillIdResponse modifyBillType(OrderBillModifyTradeRequest request);

    /**
     * 生成支付单
     * @param request
     * @return
     */
    OrderBillPayResponse pay(OrderBillPayRequest request);


    /**
     * 生成支付单
     * @param request
     * @return
     */
    OrderBillPayResponse payInfo(OrderBillPayRequest request);

    /**
     * 现金支付
     * @param request
     * @return
     */
    OrderBillIdResponse cashPay(OrderBillCashPayRequest request);

    /**
     * 订单主查更新
     * @param payDO
     */
    Boolean payUpdateByQuery(OrderPayDO payDO);

    /**
     * 订单回调处理
     * @param data
     */
    void payUpdateByNotifyWx(String data);

    /**
     * 详情
     * @param request
     * @return
     */
    OrderBillDetailResponse detail(OrderBillDetailRequest request);

    /**
     * 微信退款
     *
     * @param request
     * @return
     */
    OrderBillIdResponse wxRefund(OrderBillCashPayRequest request);

    /**
     * 统计
     * @param request
     * @return
     */
    OrderBillStatisticsBResponse statisticsforB(OrderBillPageRequest request);
}
