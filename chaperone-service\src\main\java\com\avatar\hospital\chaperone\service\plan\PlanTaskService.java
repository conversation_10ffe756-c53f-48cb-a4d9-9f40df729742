package com.avatar.hospital.chaperone.service.plan;

import com.avatar.hospital.chaperone.database.plan.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.QueryRequest;
import com.avatar.hospital.chaperone.request.plan.TaskExecuteRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.plan.PlanTaskVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 13:32
 */
public interface PlanTaskService {
    Boolean create(PlanType planType, PlanDO planDO, List<Long> deviceIds);

    PageResponse<PlanTaskVO> paging(QueryRequest request);

    boolean update2delete(PlanRequest request);

    List<PlanTaskVO> getPlanBeforeTodayTask(PlanType planType, Long planId);

    Boolean execute(TaskExecuteRequest request);

    <PERSON><PERSON><PERSON> expired(PlanType planType, List<Long> taskIds);
}
