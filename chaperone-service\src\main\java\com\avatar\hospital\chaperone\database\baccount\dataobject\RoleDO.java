package com.avatar.hospital.chaperone.database.baccount.dataobject;

import com.avatar.hospital.chaperone.database.baccount.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.baccount.enums.RoleStatus;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_role")
public class RoleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色权限字符串
     */
    private String roleKey;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.RoleStatus
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 判断状态是否启用
     *
     * @return -
     */
    public boolean enable() {
        return RoleStatus.ENABLE.getStatus().equals(status);
    }

}
