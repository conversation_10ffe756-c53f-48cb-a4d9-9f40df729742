package com.avatar.hospital.chaperone.response.order;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderConsumeSumPriceResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 消费金额
     */
    private Integer price;

    public static final OrderConsumeSumPriceResponse build(Integer price) {
        OrderConsumeSumPriceResponse obj = new OrderConsumeSumPriceResponse();
        obj.setPrice(price);
        return obj;
    }

}
