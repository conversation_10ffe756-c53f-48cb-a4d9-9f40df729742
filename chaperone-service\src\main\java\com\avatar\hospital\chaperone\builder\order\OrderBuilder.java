package com.avatar.hospital.chaperone.builder.order;

import com.avatar.hospital.chaperone.database.order.dataobject.*;
import com.avatar.hospital.chaperone.database.order.dto.OrderConsumerlogExtraNursingInfoDTO;
import com.avatar.hospital.chaperone.database.order.enums.*;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.baccount.OrganizationControlResponse;
import com.avatar.hospital.chaperone.response.item.ItemResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingSimpleResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.service.order.dto.OrderBillExportDTO;
import com.avatar.hospital.chaperone.service.order.dto.OrderConsumerlogExportDTO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.avatar.hospital.chaperone.utils.*;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.avatar.hospital.chaperone.service.order.consts.OrderConst.CONSUMER_LOG_VERSION;
import static com.avatar.hospital.chaperone.service.order.consts.OrderConst.NO_DELETED;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-13 11:39
 **/
public class OrderBuilder {

    public static final OrderResponse toOrderResponseByOrderDO(OrderDO orderDO) {
        OrderResponse orderResponse = new OrderResponse();
        orderResponse.setId(orderDO.getId());
        orderResponse.setAccountId(orderDO.getAccountId());
        orderResponse.setServerType(orderDO.getServerType());
        orderResponse.setPatientName(orderDO.getPatientName());
        orderResponse.setPatientGender(orderDO.getPatientGender());
        orderResponse.setPatientBirthday(orderDO.getPatientBirthday());
        orderResponse.setPhone(orderDO.getPhone());
        orderResponse.setPatientStateDesc(orderDO.getPatientStateDesc());
        orderResponse.setSpecificDesc(orderDO.getSpecificDesc());
        orderResponse.setOrgId(orderDO.getOrgId());
        orderResponse.setDepartments(orderDO.getDepartments());
        orderResponse.setBedNo(orderDO.getBedNo());
        orderResponse.setAddress(orderDO.getAddress());
        orderResponse.setOrderStatus(orderDO.getOrderStatus());
        orderResponse.setStartTime(orderDO.getStartTime());
        orderResponse.setEndTime(orderDO.getEndTime());
        orderResponse.setRealStartTime(orderDO.getRealStartTime());
        orderResponse.setRealEndTime(orderDO.getRealEndTime());
        orderResponse.setParentBillId(orderDO.getParentBillId());
        orderResponse.setDiscount(orderDO.getDiscount());
        orderResponse.setDiscountType(orderDO.getDiscountType());
        orderResponse.setRateCertifiedProperty(orderDO.getRateCertifiedProperty());
        orderResponse.setRateHospital(orderDO.getRateHospital());
        orderResponse.setRateNursing(orderDO.getRateNursing());
        orderResponse.setRemark(orderDO.getRemark());
        orderResponse.setAccountPhone(orderDO.getAccountPhone());
        return orderResponse;
    }

    public static OrderResponse totoOrderResponseByOrderDOForB(OrderDO orderDO,
                                                               Map<Long, List<OrderItemDO>> orderMap) {
        List<OrderItemDO> orderItem = orderMap.get(orderDO.getId());
        List<Long> itemIdLis = new ArrayList<>();
        List<String> itenNameList = new ArrayList<>();
        if (Objects.nonNull(orderItem) && !orderItem.isEmpty()) {
            itemIdLis = CollUtils.toListLongDistinct(orderItem, OrderItemDO::getItemId);
            itenNameList = CollUtils.toListLongDistinct(orderItem, OrderItemDO::getItemName);
        }

        OrderResponse orderResponse = new OrderResponse();
        orderResponse.setId(orderDO.getId());
        orderResponse.setAccountId(orderDO.getAccountId());
        orderResponse.setAccountPhone(orderDO.getAccountPhone());
        orderResponse.setServerType(orderDO.getServerType());
        orderResponse.setPatientName(orderDO.getPatientName());
        orderResponse.setPatientGender(orderDO.getPatientGender());
        orderResponse.setPatientBirthday(orderDO.getPatientBirthday());
        orderResponse.setPhone(orderDO.getPhone());
        orderResponse.setPatientStateDesc(orderDO.getPatientStateDesc());
        orderResponse.setSpecificDesc(orderDO.getSpecificDesc());
        orderResponse.setOrgId(orderDO.getOrgId());
        orderResponse.setDepartments(orderDO.getDepartments());
        orderResponse.setBedNo(orderDO.getBedNo());
        orderResponse.setAddress(orderDO.getAddress());
        orderResponse.setOrderStatus(orderDO.getOrderStatus());
        orderResponse.setStartTime(orderDO.getStartTime());
        orderResponse.setEndTime(orderDO.getEndTime());
        orderResponse.setRealStartTime(orderDO.getRealStartTime());
        orderResponse.setRealEndTime(orderDO.getRealEndTime());
        orderResponse.setParentBillId(orderDO.getParentBillId());
        orderResponse.setDiscount(orderDO.getDiscount());
        orderResponse.setDiscountType(orderDO.getDiscountType());
        orderResponse.setRateCertifiedProperty(orderDO.getRateCertifiedProperty());
        orderResponse.setRateHospital(orderDO.getRateHospital());
        orderResponse.setRateNursing(orderDO.getRateNursing());
        orderResponse.setRemark(orderDO.getRemark());
        orderResponse.setItemIdList(itemIdLis);
        orderResponse.setItemNameList(itenNameList);
        orderResponse.setAccountPhone(orderDO.getAccountPhone());
        return orderResponse;
    }

    public static OrderTotalBillResponse toOrderTotalBillResponse(OrderBillDO orderBillDO,
                                                                  List<OrderItemDO> orderItemDOList,
                                                                  List<ItemResponse> itemResponses,
                                                                  OrderBillDO repayBill) {
        Integer price = 0;
        if (Objects.nonNull(repayBill)) {
            price = repayBill.getPriceReceivable();
        }

        OrderTotalBillResponse orderTotalBillResponse = new OrderTotalBillResponse();
        orderTotalBillResponse.setId(orderBillDO.getId());
        orderTotalBillResponse.setOrderId(orderBillDO.getOrderId());
        orderTotalBillResponse.setBillType(orderBillDO.getBillType());
        orderTotalBillResponse.setBillSubType(orderBillDO.getBillSubType());
        orderTotalBillResponse.setPriceReceivable(orderBillDO.getPriceReceivable());
        orderTotalBillResponse.setPriceReceived(orderBillDO.getPriceReceived());
        orderTotalBillResponse.setRemark(orderBillDO.getRemark());
        orderTotalBillResponse.setPrice(price);
        orderTotalBillResponse.setItemList(toOrderBillItemResponse(orderItemDOList, itemResponses));
        return orderTotalBillResponse;
    }

    public static OrderDO createByModifyDiscount(OrderModifyDiscountRequest request) {
        OrderDO orderDO = new OrderDO();
        orderDO.setId(request.getOrderId());
        orderDO.setDiscount(request.getDiscount());
        orderDO.setDiscountType(request.getType());
        return orderDO;
    }

    public static OrderDO createByModifyRate(OrderModifyRateRequest request) {
        OrderDO orderDO = new OrderDO();
        orderDO.setId(request.getOrderId());
        orderDO.setRateCertifiedProperty(request.getRateCertifiedProperty());
        orderDO.setRateHospital(request.getRateHospital());
        orderDO.setRateNursing(request.getRateNursing());
        return orderDO;
    }

    public static OrderDO createOrderDOByCreate(OrderCreateRequest request,
                                                OrganizationControlResponse organizationControl) {
        Long accountId = request.getAccountId();
        accountId = Objects.isNull(accountId) ? request.getOperator() : accountId;
        OrderStatus orderStatus = OrderStatus.initStatusByOrgControl(organizationControl);

        OrderDO orderDO = new OrderDO();
        orderDO.setAccountId(accountId);
        orderDO.setAccountPhone(request.getAccountPhone());
        orderDO.setServerType(request.getServerType());
        orderDO.setPatientName(request.getPatientName());
        orderDO.setPatientGender(request.getPatientGender());
        orderDO.setPatientBirthday(request.getPatientBirthday());
        orderDO.setPhone(request.getPhone());
        orderDO.setPatientStateDesc(request.getPatientStateDesc());
        orderDO.setSpecificDesc(request.getSpecificDesc());
        orderDO.setOrgId(request.getOrgId());
        orderDO.setDepartments(request.getDepartments());
        orderDO.setBedNo(request.getBedNo());
        orderDO.setAddress(request.getAddress());
        orderDO.setOrderStatus(orderStatus.getStatus());
        orderDO.setStartTime(request.getStartTime());
        orderDO.setEndTime(request.getEndTime());
        orderDO.setRealStartTime(request.getStartTime());
        orderDO.setRealEndTime(request.getEndTime());
        orderDO.setDiscount(OrderConst.NO_DISCOUNT);
        orderDO.setDiscountType(OrderDiscountType.NO_ASCEND.getStatus());
        orderDO.setRateCertifiedProperty(OrderConst.DEFAULT_RATE);
        orderDO.setRateHospital(OrderConst.DEFAULT_RATE);
        orderDO.setRateNursing(OrderConst.DEFAULT_RATE);
        orderDO.setRemark(request.getRemark());
        orderDO.setCreateBy(request.getOperator());
        orderDO.setUpdateBy(request.getOperator());
        orderDO.setDeleted(OrderConst.NO_DELETED);
        return orderDO;
    }

    /**
     * 创建总账单
     *
     * @param request
     * @param calculationResponse
     * @return
     */
    public static OrderBillDO createTotalOrderBillDOByCreate(OrderCreateRequest request,
                                                             OrderPriceCalculationResponse calculationResponse) {
        String itemNames = calculationResponse.getItemPriceList().stream()
                .map(item -> item.getName())
                .collect(Collectors.joining(","));

        OrderBillDO orderBillDO = new OrderBillDO();
        orderBillDO.setParentBillId(OrderConst.TOP_BILL);
        orderBillDO.setBizId(OrderConst.ZERO.toString());
        orderBillDO.setBillType(OrderBillType.NONE.getStatus());
        orderBillDO.setBillSubType(OrderBillSubType.NORMAL.getStatus());
        orderBillDO.setPriceReceivable(calculationResponse.getTotalPrice());
        orderBillDO.setPriceReceived(OrderConst.ZERO);
        orderBillDO.setPriceRefundable(OrderConst.NO_REFUND_PRICE);
        orderBillDO.setPriceRefunded(OrderConst.NO_REFUND_PRICE);
        orderBillDO.setItemNames(itemNames);
        orderBillDO.setDisplay(OrderDisplay.NO_DISPLAY.getStatus());
        orderBillDO.setRemark(OrderConst.DEFAULT_REMARK);
        orderBillDO.setDiscount(OrderConst.NO_DISCOUNT);
        orderBillDO.setCreateBy(request.getOperator());
        orderBillDO.setUpdateBy(request.getOperator());
        orderBillDO.setDeleted(OrderConst.NO_DELETED);
        return orderBillDO;
    }

    public static OrderBillDO createOrderBillPrepayByModify(Long totalBillId,
                                                            OrderBillModifyTradeRequest request,
                                                            String patientName) {

        OrderBillDO orderBillDO = new OrderBillDO();
        orderBillDO.setOrderId(request.getOrderId());
        orderBillDO.setBizId(request.getOrderId().toString());
        orderBillDO.setParentBillId(totalBillId);
        orderBillDO.setBillType(OrderBillType.PREPAY.getStatus());
        orderBillDO.setBillSubType(OrderBillSubType.NORMAL.getStatus());
        orderBillDO.setPriceReceivable(request.getPrice());
        orderBillDO.setPriceReceived(OrderConst.ZERO);
        orderBillDO.setPriceRefundable(OrderConst.ZERO);
        orderBillDO.setPriceRefunded(OrderConst.ZERO);
        orderBillDO.setItemNames(OrderConst.DEFAULT_REMARK);
        orderBillDO.setDisplay(OrderDisplay.DISPLAY.getStatus());
        orderBillDO.setRemark(OrderConst.DEFAULT_REMARK);
        orderBillDO.setDiscount(OrderUtils.DISCOUNT_RATE);
        orderBillDO.setCreateBy(request.getOperator());
        orderBillDO.setUpdateBy(request.getOperator());
        orderBillDO.setDeleted(NO_DELETED);
        orderBillDO.setPatientName(patientName);
        orderBillDO.setNursingJson(StrUtils.EMPTY);
        return orderBillDO;
    }

    /**
     * 创建套餐价格明细
     *
     * @param request
     * @param calculationResponse
     * @return
     */
    public static List<OrderItemDO> createOrderItemDOByCreate(OrderCreateRequest request,
                                                              OrderPriceCalculationResponse calculationResponse) {
        List<OrderItemDO> list = calculationResponse.getItemPriceList().stream()
                .map(item -> buildOrderItemDO(item,request.getOperator()))
                .collect(Collectors.toList());
        return list;
    }

    public static OrderItemDO buildOrderItemDO(OrderPriceCalculationResponse.PriceCalculationItem item,
                                                Long operator) {
        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setItemId(item.getItemId());
        orderItemDO.setItemName(item.getName());
        orderItemDO.setNumber(item.getNumber());
        orderItemDO.setPrice(item.getPrice());
        orderItemDO.setTotalPrice(item.getTotalPrice());
        orderItemDO.setCreateBy(operator);
        orderItemDO.setUpdateBy(operator);
        orderItemDO.setDeleted(OrderConst.NO_DELETED);
        return orderItemDO;
    }

    /**
     * 取消订单
     * @param orderDO
     * @return
     */
    public static OrderDO createUpdateEntityByCancel(OrderDO orderDO) {
        OrderDO entity = new OrderDO();
        entity.setId(orderDO.getId());
        entity.setRealEndTime(DateUtils.dateHourInt());
        // B端审核还未通过
        if (orderDO.getOrderStatus() < OrderStatus.WAIT_CONFIRM.getStatus()) {
            entity.setOrderStatus(OrderStatus.CANCEL.getStatus());
        } else {
            entity.setOrderStatus(OrderStatus.CLOSE.getStatus());
        }
        return entity;
    }

    /**
     * 订单状态转换
     * @param orderId 订单
     * @param orderStatus 订正状态
     * @return
     */
    public static OrderDO createUpdateEntityByStatus(Long orderId,OrderStatus orderStatus) {
        OrderDO entity = new OrderDO();
        entity.setId(orderId);
        entity.setOrderStatus(orderStatus.getStatus());
        entity.setUpdatedAt(LocalDateTime.now());
        return entity;
    }

    /**
     * 顶底确认
     * @param orderDO
     * @param operator
     * @return
     */
    public static OrderDO createUpdateEntityByConfirm(OrderDO orderDO,Long operator) {
        OrderDO entity = new OrderDO();
        entity.setId(orderDO.getId());
        entity.setOrderStatus(OrderStatus.WORK.getStatus());
        entity.setRealStartTime(DateUtils.dateHourInt());
        entity.setUpdateBy(operator);
        return entity;
    }

    public static OrderDO createUpdateEntityBySettle(OrderDO orderDO,Long operator) {
        OrderDO entity = new OrderDO();
        entity.setId(orderDO.getId());
        entity.setOrderStatus(OrderStatus.APPLY_SETTLE.getStatus());
        entity.setUpdateBy(operator);
        return entity;
    }

    public static OrderDetailResponse createOrderDetailResponseByGetById(OrderDO orderDO,
                                                                         OrderBillDO orderBillDO,
                                                                         List<OrderItemDO> orderItemDOList,
                                                                         List<ItemResponse> itemList,
                                                                         OrderBillDO repayBill,
                                                                         List<NursingSimpleResponse> curNursingResponseList,
                                                                         List<NursingSimpleResponse> preNursingResponseList) {
        OrderDetailResponse result = new OrderDetailResponse();
        result.setBase(toOrderResponseByOrderDO(orderDO));
        result.setBill(toOrderTotalBillResponse(orderBillDO,orderItemDOList,itemList,repayBill));
        result.setCurNursing(CollectionUtils.isEmpty(curNursingResponseList) ? null : curNursingResponseList.get(0));
        result.setPreNursing(CollectionUtils.isEmpty(preNursingResponseList) ? null : preNursingResponseList.get(0));
        result.setCurNursingList(curNursingResponseList);
        result.setPreNursingList(preNursingResponseList);
        return result;
    }

    private static List<OrderBillItemResponse> toOrderBillItemResponseList(List<OrderItemDO> orderItemDOList,
                                                                           List<ItemResponse> itemResponses) {
        Map<Long, ItemResponse> itemIdRef = itemResponses.stream()
                .collect(Collectors.toMap(ItemResponse::getId, Function.identity()));

        List<OrderBillItemResponse> list = new ArrayList<>();
        for (OrderItemDO orderItemDO : orderItemDOList) {
            ItemResponse item = itemIdRef.get(orderItemDO.getItemId());
            OrderBillItemResponse orderBillItemResponse = new OrderBillItemResponse();
            orderBillItemResponse.setItemId(orderItemDO.getItemId());
            orderBillItemResponse.setNumber(orderItemDO.getNumber());
            orderBillItemResponse.setPrice(orderItemDO.getPrice());
            orderBillItemResponse.setTotalPrice(orderItemDO.getTotalPrice());
            if (Objects.nonNull(item)) {
                orderBillItemResponse.setServerType(item.getServerType());
                orderBillItemResponse.setName(item.getName());
                orderBillItemResponse.setStatus(item.getStatus());
                orderBillItemResponse.setNursingStar(item.getNursingStar());
                orderBillItemResponse.setRemark(item.getRemark());
                orderBillItemResponse.setCoverPicture(item.getCoverPicture());
            }
            list.add(orderBillItemResponse);
        }
        return list;
    }

    public static List<OrderBillItemResponse> toOrderBillItemResponse(List<OrderItemDO> orderItemDOList,
                                                                List<ItemResponse> itemResponses) {
        Map<Long, ItemResponse> itemMap = itemResponses.stream()
                .collect(Collectors.toMap(ItemResponse::getId, Function.identity(), (v1, v2) -> v1));
        List<OrderBillItemResponse> list = new ArrayList<>(orderItemDOList.size());
        for (OrderItemDO orderItemDO : orderItemDOList) {
            ItemResponse itemInfo = itemMap.get(orderItemDO.getItemId());
            if (Objects.nonNull(itemInfo)) {
                list.add(toOrderBillItemResponse(orderItemDO,itemInfo));
            }

        }
        return list;
    }

    private static OrderBillItemResponse toOrderBillItemResponse(OrderItemDO orderItemDO,ItemResponse item) {
        OrderBillItemResponse orderBillItemResponse = new OrderBillItemResponse();
        orderBillItemResponse.setItemId(orderItemDO.getItemId());
        orderBillItemResponse.setNumber(orderItemDO.getNumber());
        orderBillItemResponse.setPrice(orderItemDO.getPrice());
        orderBillItemResponse.setTotalPrice(orderItemDO.getTotalPrice());
        orderBillItemResponse.setServerType(item.getServerType());
        orderBillItemResponse.setName(item.getName());
        orderBillItemResponse.setStatus(item.getStatus());
        orderBillItemResponse.setNursingStar(item.getNursingStar());
        orderBillItemResponse.setRemark(item.getRemark());
        orderBillItemResponse.setCoverPicture(item.getCoverPicture());
        orderBillItemResponse.setChargingTime(item.getChargingTime());
        return orderBillItemResponse;
    }


    public static  OrderResponse createOrderResponseByPagingForC(OrderDO orderDO) {
        OrderResponse orderResponse = new OrderResponse();
        orderResponse.setId(orderDO.getId());
        orderResponse.setAccountId(orderDO.getAccountId());
        orderResponse.setServerType(orderDO.getServerType());
        orderResponse.setPatientName(orderDO.getPatientName());
        orderResponse.setPatientGender(orderDO.getPatientGender());
        orderResponse.setPatientBirthday(orderDO.getPatientBirthday());
        orderResponse.setPhone(orderDO.getPhone());
        orderResponse.setPatientStateDesc(orderDO.getPatientStateDesc());
        orderResponse.setSpecificDesc(orderDO.getSpecificDesc());
        orderResponse.setOrgId(orderDO.getOrgId());
        orderResponse.setDepartments(orderDO.getDepartments());
        orderResponse.setBedNo(orderDO.getBedNo());
        orderResponse.setAddress(orderDO.getAddress());
        orderResponse.setOrderStatus(orderDO.getOrderStatus());
        orderResponse.setStartTime(orderDO.getStartTime());
        orderResponse.setEndTime(orderDO.getEndTime());
        orderResponse.setRealStartTime(orderDO.getRealStartTime());
        orderResponse.setRealEndTime(orderDO.getRealEndTime());
        orderResponse.setParentBillId(orderDO.getParentBillId());
        orderResponse.setDiscount(orderDO.getDiscount());
        orderResponse.setDiscountType(orderDO.getDiscountType());
        orderResponse.setRateCertifiedProperty(orderDO.getRateCertifiedProperty());
        orderResponse.setRateHospital(orderDO.getRateHospital());
        orderResponse.setRateNursing(orderDO.getRateNursing());
        orderResponse.setRemark(orderDO.getRemark());
        orderResponse.setAccountPhone(orderDO.getAccountPhone());
        return orderResponse;
    }

    public static OrderChangelogResponse toOrderChangelogResponse(OrderChangelogDO data) {
        OrderChangelogResponse orderChangelogResponse = new OrderChangelogResponse();
        orderChangelogResponse.setId(data.getId());
        orderChangelogResponse.setOrderId(data.getOrderId());
        orderChangelogResponse.setTime(data.getTime());
        orderChangelogResponse.setEvent(data.getEventCode());
        orderChangelogResponse.setEventName(OrderLogEvent.of(data.getEventCode()).getDescribe());
        orderChangelogResponse.setBefore(data.getBeforeValue());
        orderChangelogResponse.setAfter(data.getAfterValue());
        orderChangelogResponse.setOperatorId(data.getCreateBy());
        orderChangelogResponse.setOperatorName(data.getOperatorName());
        orderChangelogResponse.setOperatorMobile(data.getOperatorMobile());
        orderChangelogResponse.setOperatorSource(data.getOperatorSource());
        return orderChangelogResponse;
    }

    public static OrderConsumerLogCResponse toOrderConsumerLogCResponse(OrderConsumerlogDO consumerlogDO) {
        OrderConsumerLogCResponse orderConsumerLogCResponse = new OrderConsumerLogCResponse();
        orderConsumerLogCResponse.setId(consumerlogDO.getId());
        orderConsumerLogCResponse.setDate(consumerlogDO.getDate());
        orderConsumerLogCResponse.setOrderId(consumerlogDO.getOrderId());
        orderConsumerLogCResponse.setItemId(consumerlogDO.getItemId());
        orderConsumerLogCResponse.setItemName(consumerlogDO.getItemName());
        orderConsumerLogCResponse.setNursingId(consumerlogDO.getNursingId());
        orderConsumerLogCResponse.setNursingName(consumerlogDO.getNursingName());
        orderConsumerLogCResponse.setTotalPrice(consumerlogDO.getTotalPrice());
        return orderConsumerLogCResponse;
    }

    public static OrderConsumerLogResponse toOrderConsumerLogResponse(OrderConsumerlogDO consumerlogDO) {
        OrderConsumerLogResponse orderConsumerLogResponse = new OrderConsumerLogResponse();
        orderConsumerLogResponse.setId(consumerlogDO.getId());
        orderConsumerLogResponse.setDate(consumerlogDO.getDate());
        orderConsumerLogResponse.setOrderId(consumerlogDO.getOrderId());
        orderConsumerLogResponse.setItemId(consumerlogDO.getItemId());
        orderConsumerLogResponse.setTotalPrice(consumerlogDO.getTotalPrice());
        orderConsumerLogResponse.setRateCertifiedProperty(consumerlogDO.getRateCertifiedProperty());
        orderConsumerLogResponse.setPriceCertifiedProperty(consumerlogDO.getPriceCertifiedProperty());
        orderConsumerLogResponse.setRateHospital(consumerlogDO.getRateHospital());
        orderConsumerLogResponse.setPriceHospital(consumerlogDO.getPriceHospital());
        orderConsumerLogResponse.setRateNursing(consumerlogDO.getRateNursing());
        orderConsumerLogResponse.setPriceNursing(consumerlogDO.getPriceNursing());
        orderConsumerLogResponse.setVersion(consumerlogDO.getVersion());
        orderConsumerLogResponse.setNursingId(consumerlogDO.getNursingId());
        orderConsumerLogResponse.setNursingList(OrderConsumerlogExtraNursingInfoDTO.build(consumerlogDO.getExtraNursingInfo()).getList());
        if (CollectionUtils.isEmpty(orderConsumerLogResponse.getNursingList())) {
            // 老数据做兼容处理
            orderConsumerLogResponse.setNursingList(OrderConsumerlogExtraNursingInfoDTO.build(consumerlogDO.getNursingId(),consumerlogDO.getNursingName(),
                                                                                                consumerlogDO.getPriceNursing(),consumerlogDO.getRateNursing()).getList());
        }
        return orderConsumerLogResponse;
    }

    public static OrderChangelogDO toOrderChangelogDO(OrderChangelogSaveRequest request) {
        Operator operatorUser = request.getOperatorUser();
        Long operatorId = operatorUser.getId();

        OrderChangelogDO orderChangelogDO = new OrderChangelogDO();
        orderChangelogDO.setOrderId(request.getOrderId());
        orderChangelogDO.setTime(request.getTime());
        orderChangelogDO.setEventCode(request.getEvent().getStatus());
        orderChangelogDO.setBeforeValue(request.getBefore());
        orderChangelogDO.setAfterValue(request.getAfter());
        orderChangelogDO.setOperatorMobile(operatorUser.getMobile());
        orderChangelogDO.setOperatorName(operatorUser.getName());
        orderChangelogDO.setOperatorSource(operatorUser.getSource());
        orderChangelogDO.setCreateBy(operatorId);
        orderChangelogDO.setUpdateBy(operatorId);
        orderChangelogDO.setDeleted(OrderConst.NO_DELETED);
        return orderChangelogDO;
    }


    public static OrderBillResponse toOrderBillResponse(OrderBillDO data) {
        OrderPayStatus orderPayStatus = OrderPayStatus.UNPAY;
        if (data.getPriceReceivable() > 0 && Objects.equals(data.getPriceReceivable(),data.getPriceReceived())) {
            orderPayStatus = OrderPayStatus.SUCCESS;
        }
        if (data.getPriceRefundable() > 0 && Objects.equals(data.getPriceRefundable(),data.getPriceRefunded())) {
            orderPayStatus = OrderPayStatus.SUCCESS;
        }

        OrderBillResponse orderBillResponse = new OrderBillResponse();
        orderBillResponse.setId(data.getId());
        orderBillResponse.setOrderId(data.getOrderId());
        orderBillResponse.setBillType(data.getBillType());
        orderBillResponse.setBillSubType(data.getBillSubType());
        orderBillResponse.setPriceReceivable(data.getPriceReceivable());
        orderBillResponse.setPriceReceived(data.getPriceReceived());
        orderBillResponse.setPriceRefundable(data.getPriceRefundable());
        orderBillResponse.setPriceRefunded(data.getPriceRefunded());
        orderBillResponse.setItemNames(data.getItemNames());
        orderBillResponse.setDisplay(data.getDisplay());
        orderBillResponse.setRemark(data.getRemark());
        orderBillResponse.setDiscount(data.getDiscount());
        orderBillResponse.setPayStatus(orderPayStatus.getStatus());
        orderBillResponse.setStartTime(data.getStartTime());
        orderBillResponse.setEndTime(data.getEndTime());
        orderBillResponse.setNursingList(OrderNursingSimpleResponse.of(data.getNursingJson()));
        orderBillResponse.setPatientName(data.getPatientName());
        return orderBillResponse;
    }

    public static OrderBillDO createOrderBill(OrderBillCreateRequest request) {
        OrderBillDO orderBillDO = new OrderBillDO();
        orderBillDO.setOrderId(request.getOrderId());
        orderBillDO.setBizId(request.getBizId());
        orderBillDO.setParentBillId(request.getParentBillId());
        orderBillDO.setBillType(request.getBillType());
        orderBillDO.setBillSubType(request.getBillSubType());
        orderBillDO.setPriceReceivable(request.getPriceReceivable());
        orderBillDO.setPriceReceived(0);
        orderBillDO.setPriceRefundable(request.getPriceRefundable());
        orderBillDO.setPriceRefunded(0);
        orderBillDO.setItemNames(StrUtils.EMPTY);
        orderBillDO.setDisplay(OrderDisplay.DISPLAY.getStatus());
        orderBillDO.setRemark(request.getRemark());
        orderBillDO.setDiscount(request.getDiscount());
        orderBillDO.setCreateBy(request.getOperator());
        orderBillDO.setUpdateBy(request.getOperator());
        orderBillDO.setDeleted(NO_DELETED);
        orderBillDO.setTradeType(OrderTradeType.INIT.getStatus());
        orderBillDO.setStartTime(request.getStartTime());
        orderBillDO.setEndTime(request.getEndTime());
        orderBillDO.setPatientName(request.getPatientName());
        orderBillDO.setNursingJson(request.getNursingJson());
        return orderBillDO;
    }

    public static List<OrderConsumerlogDO> toOrderConsumerlogDOListCreate(OrderDO order,
                                                                          List<OrderNursingDO> nursingDOList,
                                                                          List<Long> itemIdList,
                                                                          Map<Long, ItemResponse> itemMap,
                                                                          Integer date,
                                                                          Long operator) {
        Boolean orderNoSettingRate = order.noSettingRate();
        List<OrderConsumerlogDO> list = new ArrayList<>(itemIdList.size());
        for (Long itemId : itemIdList) {
            ItemResponse item = itemMap.get(itemId);
            if (Objects.isNull(item)) {
                continue;
            }
            // 费率更新
            Integer rateCertifiedProperty = order.getRateCertifiedProperty();
            Integer rateHospital = order.getRateHospital();
            Integer rateNursing = order.getRateNursing();
            if (orderNoSettingRate) {
                rateCertifiedProperty = item.getRateCertifiedProperty();
                rateHospital = item.getRateHospital();
                rateNursing = item.getRateNursing();
            }
            Integer newTotalPrice = OrderUtils.calculationDiscount(item.getPrice(), order.getDiscount());

            OrderUtils.CalculationRateResult calculationRateResult
                    = OrderUtils.calculationRate(OrderUtils.CalculationRateParam.build(newTotalPrice, rateCertifiedProperty, rateHospital, rateNursing));
            // 护工平分计算
            List<OrderUtils.CalculationNursingRateResult> calculationNursingRateResultList
                    = OrderUtils.calculationNursingRate(calculationRateResult.getPriceNursing(),rateNursing,nursingDOList);

            OrderConsumerlogDO orderConsumerlogDO = new OrderConsumerlogDO();
            orderConsumerlogDO.setDate(date);
            orderConsumerlogDO.setOrderId(order.getId());
            orderConsumerlogDO.setItemId(itemId);
            orderConsumerlogDO.setItemName(item.getName());
            orderConsumerlogDO.setTotalPriceSource(item.getPrice());
            orderConsumerlogDO.setTotalPrice(newTotalPrice);
            orderConsumerlogDO.setRateCertifiedProperty(calculationRateResult.getRateCertifiedProperty());
            orderConsumerlogDO.setPriceCertifiedProperty(calculationRateResult.getPriceCertifiedProperty());
            orderConsumerlogDO.setRateHospital(calculationRateResult.getRateHospital());
            orderConsumerlogDO.setPriceHospital(calculationRateResult.getPriceHospital());
            orderConsumerlogDO.setRateNursing(calculationRateResult.getRateNursing());
            orderConsumerlogDO.setPriceNursing(calculationRateResult.getPriceNursing());
            orderConsumerlogDO.setVersion(CONSUMER_LOG_VERSION);
            orderConsumerlogDO.setCreateBy(operator);
            orderConsumerlogDO.setUpdateBy(operator);
            orderConsumerlogDO.setDeleted(NO_DELETED);
            orderConsumerlogDO.setExtraNursingInfo(OrderConsumerlogExtraNursingInfoDTO.build(calculationNursingRateResultList).toArrayJson());
            /*orderConsumerlogDO.setNursingId(nursing.getId());
            orderConsumerlogDO.setNursingName(nursing.getName());*/
            list.add(orderConsumerlogDO);
        }
        return list;
    }

    public static List<OrderConsumerlogDO> toOrderConsumerlogDOListCreateDateMap(OrderDO order,
                                                                                 List<OrderNursingDO> nursingDOList,
                                                                          List<Long> itemIdList,
                                                                          Map<Long, ItemResponse> itemMap,
                                                                          Map<Long,Integer> itemDateRef,
                                                                          Long operator) {
        Boolean orderNoSettingRate = order.noSettingRate();
        List<OrderConsumerlogDO> list = new ArrayList<>(itemIdList.size());
        for (Long itemId : itemIdList) {
            ItemResponse item = itemMap.get(itemId);
            if (Objects.isNull(item)) {
                continue;
            }
            // 费率更新
            Integer rateCertifiedProperty = order.getRateCertifiedProperty();
            Integer rateHospital = order.getRateHospital();
            Integer rateNursing = order.getRateNursing();
            if (orderNoSettingRate) {
                rateCertifiedProperty = item.getRateCertifiedProperty();
                rateHospital = item.getRateHospital();
                rateNursing = item.getRateNursing();
            }
            Integer newTotalPrice = OrderUtils.calculationDiscount(item.getPrice(), order.getDiscount());

            OrderUtils.CalculationRateResult calculationRateResult
                    = OrderUtils.calculationRate(OrderUtils.CalculationRateParam.build(newTotalPrice, rateCertifiedProperty, rateHospital, rateNursing));
            // 护工平分计算
            List<OrderUtils.CalculationNursingRateResult> calculationNursingRateResultList
                    = OrderUtils.calculationNursingRate(calculationRateResult.getPriceNursing(),rateNursing,nursingDOList);

            Integer date = itemDateRef.get(itemId);

            OrderConsumerlogDO orderConsumerlogDO = new OrderConsumerlogDO();
            orderConsumerlogDO.setDate(date);
            orderConsumerlogDO.setOrderId(order.getId());
            orderConsumerlogDO.setItemId(itemId);
            orderConsumerlogDO.setItemName(item.getName());
            orderConsumerlogDO.setTotalPriceSource(item.getPrice());
            orderConsumerlogDO.setTotalPrice(newTotalPrice);
            orderConsumerlogDO.setRateCertifiedProperty(calculationRateResult.getRateCertifiedProperty());
            orderConsumerlogDO.setPriceCertifiedProperty(calculationRateResult.getPriceCertifiedProperty());
            orderConsumerlogDO.setRateHospital(calculationRateResult.getRateHospital());
            orderConsumerlogDO.setPriceHospital(calculationRateResult.getPriceHospital());
            orderConsumerlogDO.setRateNursing(calculationRateResult.getRateNursing());
            orderConsumerlogDO.setPriceNursing(calculationRateResult.getPriceNursing());
            orderConsumerlogDO.setVersion(CONSUMER_LOG_VERSION);
            orderConsumerlogDO.setCreateBy(operator);
            orderConsumerlogDO.setUpdateBy(operator);
            orderConsumerlogDO.setDeleted(NO_DELETED);
            orderConsumerlogDO.setExtraNursingInfo(OrderConsumerlogExtraNursingInfoDTO.build(calculationNursingRateResultList).toArrayJson());
            /*orderConsumerlogDO.setNursingId(nursing.getId());
            orderConsumerlogDO.setNursingName(nursing.getName());*/
            list.add(orderConsumerlogDO);
        }
        return list;
    }

    public static List<OrderConsumerlogDO> toOrderConsumerlogDOListReCreate(List<OrderConsumerlogDO> oldDataList,
                                                                            OrderDO orderDO,
                                                                            Map<Long, ItemResponse> itemMap,
                                                                            Long operator) {
        Boolean orderNoSettingRate = orderDO.noSettingRate();
        List<OrderConsumerlogDO> list = new ArrayList<>(oldDataList.size());
        for (OrderConsumerlogDO oldConsumerLog : oldDataList) {
            // 费率更新
            Integer rateCertifiedProperty = orderDO.getRateCertifiedProperty();
            Integer rateHospital = orderDO.getRateHospital();
            Integer rateNursing = orderDO.getRateNursing();
            if (orderNoSettingRate) {
                ItemResponse itemResponse = itemMap.get(oldConsumerLog.getItemId());
                rateCertifiedProperty = itemResponse.getRateCertifiedProperty();
                rateHospital = itemResponse.getRateHospital();
                rateNursing = itemResponse.getRateNursing();
            }
            Integer newTotalPrice = OrderUtils.calculationDiscount(oldConsumerLog.getTotalPriceSource(), orderDO.getDiscount());

            OrderUtils.CalculationRateResult calculationRateResult
                    = OrderUtils.calculationRate(OrderUtils.CalculationRateParam.build(newTotalPrice, rateCertifiedProperty, rateHospital, rateNursing));
            // 护工平分计算
            OrderConsumerlogExtraNursingInfoDTO extraNursingInfoDTO = OrderConsumerlogExtraNursingInfoDTO.build(oldConsumerLog.getExtraNursingInfo());
            List<OrderUtils.CalculationNursingRateResult> calculationNursingRateResultList
                                = OrderUtils.calculationNursingRate(calculationRateResult.getPriceNursing(),rateNursing,extraNursingInfoDTO.toOrderNursingDOList());

            OrderConsumerlogDO orderConsumerlogDO = new OrderConsumerlogDO();
            orderConsumerlogDO.setDate(oldConsumerLog.getDate());
            orderConsumerlogDO.setOrderId(oldConsumerLog.getOrderId());
            orderConsumerlogDO.setItemId(oldConsumerLog.getItemId());
            orderConsumerlogDO.setItemName(oldConsumerLog.getItemName());
            orderConsumerlogDO.setTotalPriceSource(oldConsumerLog.getTotalPriceSource());
            orderConsumerlogDO.setTotalPrice(newTotalPrice);
            orderConsumerlogDO.setRateCertifiedProperty(calculationRateResult.getRateCertifiedProperty());
            orderConsumerlogDO.setPriceCertifiedProperty(calculationRateResult.getPriceCertifiedProperty());
            orderConsumerlogDO.setRateHospital(calculationRateResult.getRateHospital());
            orderConsumerlogDO.setPriceHospital(calculationRateResult.getPriceHospital());
            orderConsumerlogDO.setRateNursing(calculationRateResult.getRateNursing());
            orderConsumerlogDO.setPriceNursing(calculationRateResult.getPriceNursing());
            orderConsumerlogDO.setVersion(CONSUMER_LOG_VERSION);
            orderConsumerlogDO.setCreateBy(operator);
            orderConsumerlogDO.setUpdateBy(operator);
            orderConsumerlogDO.setDeleted(NO_DELETED);
            orderConsumerlogDO.setExtraNursingInfo(OrderConsumerlogExtraNursingInfoDTO.build(calculationNursingRateResultList).toArrayJson());
           /* orderConsumerlogDO.setNursingId(oldConsumerLog.getNursingId());
            orderConsumerlogDO.setNursingName(oldConsumerLog.getNursingName());*/
            list.add(orderConsumerlogDO);
        }
        return list;
    }

    public static OrderPayDO createOrderPayDOByPay(OrderBillPayRequest request,
                                                   Integer price,
                                                   Long orderId) {
        Long operator = request.getOperator();
        long payId = IdUtils.getId();

        OrderPayDO pay = new OrderPayDO();
        pay.setId(payId);
        pay.setOrderId(orderId);
        pay.setBillId(request.getBillId());
        pay.setOutTradeNo(OrderConst.EMPTY);
        pay.setTradeType(OrderTradeType.WECHAT.getStatus());
        pay.setPrice(price);
        pay.setPayTime(OrderConst.EMPTY);
        pay.setPayOutTime(OrderUtils.getPayOutTime());
        pay.setRefundAmount(0);
        pay.setRemark(OrderConst.EMPTY);
        pay.setPayStatus(OrderPayStatus.PAYING.getStatus());
        pay.setPayResult(OrderConst.EMPTY);
        pay.setPayInfo(OrderConst.EMPTY);
        pay.setCreateBy(operator);
        pay.setUpdateBy(operator);
        pay.setDeleted(DelUtils.NO_DELETED);
        return pay;
    }

    public static List<OrderNursingDO> createOrderNursingDOBymodifyNursing(OrderModifyNursingRequest request
                                                                     ,Map<Long,NursingSimpleResponse> nursingRef) {
        List<OrderNursingDO> list = new ArrayList<>(nursingRef.size());
        nursingRef.values().forEach(nursing -> {
            OrderNursingDO nursingDO = new OrderNursingDO();
            nursingDO.setOrderId(request.getOrderId());
            nursingDO.setNursingId(nursing.getId());
            nursingDO.setNursingName(nursing.getName());
            nursingDO.setStatus(OrderNursingStatus.NORMAL.getStatus());
            nursingDO.setCreateBy(request.getOperator());
            nursingDO.setUpdateBy(request.getOperator());
            nursingDO.setDeleted(NO_DELETED);
            list.add(nursingDO);
        });

        return list;
    }

    public static OrderDO createOrderDOByModify(OrderModifyRequest request) {
        OrderDO orderDO = new OrderDO();
        orderDO.setId(request.getId());
        orderDO.setServerType(request.getServerType());
        orderDO.setPatientName(request.getPatientName());
        orderDO.setPatientGender(request.getPatientGender());
        orderDO.setPatientBirthday(request.getPatientBirthday());
        orderDO.setPhone(request.getPhone());
        orderDO.setPatientStateDesc(request.getPatientStateDesc());
        orderDO.setSpecificDesc(request.getSpecificDesc());
        orderDO.setOrgId(request.getOrgId());
        orderDO.setDepartments(request.getDepartments());
        orderDO.setBedNo(request.getBedNo());
        orderDO.setAddress(request.getAddress());
        orderDO.setStartTime(request.getStartTime());
        orderDO.setEndTime(request.getEndTime());
        orderDO.setRealStartTime(request.getStartTime());
        orderDO.setRealEndTime(request.getEndTime());
        orderDO.setRemark(request.getRemark());
        orderDO.setUpdateBy(request.getOperator());
        return orderDO;
    }

    public static OrderEstimateDO createOrderEstimateDOByCreate(OrderEstimateSaveRequest request) {
        OrderEstimateDO orderEstimateDO = new OrderEstimateDO();
        orderEstimateDO.setOrderId(request.getOrderId());
        orderEstimateDO.setLevel(request.getLevel());
        orderEstimateDO.setRemark(request.getRemark());
        orderEstimateDO.setSource(request.getSource());
        orderEstimateDO.setCreateBy(request.getOrderId());
        orderEstimateDO.setUpdateBy(request.getOperator());
        orderEstimateDO.setDeleted(DelUtils.NO_DELETED);
        return orderEstimateDO;
    }

    public static List<OrderEstimateResponse> toOrderEstimateResponseList(List<OrderEstimateDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream()
                .map(item -> toOrderEstimateResponse(item))
                .collect(Collectors.toList());
    }

    public static OrderEstimateResponse toOrderEstimateResponse(OrderEstimateDO list) {
        OrderEstimateResponse orderEstimateResponse = new OrderEstimateResponse();
        orderEstimateResponse.setId(list.getId());
        orderEstimateResponse.setOrderId(list.getOrderId());
        orderEstimateResponse.setLevel(list.getLevel());
        orderEstimateResponse.setRemark(list.getRemark());
        orderEstimateResponse.setSource(list.getSource());
        return orderEstimateResponse;
    }

    public static OrderBillDO createOrderBillDOByModifyBillType(Long billId,OrderBillModifyTradeRequest request) {
        OrderBillDO orderBillDO = new OrderBillDO();
        orderBillDO.setId(billId);
        OrderBillType billType = OrderBillType.of(request.getBillType());
        if (billType.isPrepay()) {
            // 预付单
            orderBillDO.setBillType(request.getBillType());
            orderBillDO.setBillSubType(OrderBillSubType.NORMAL.getStatus());
        } else {
            // 周期
            orderBillDO.setBillType(request.getBillType());
            orderBillDO.setBillSubType(request.getBillSubType());
        }

        orderBillDO.setUpdateBy(request.getOperator());
        return orderBillDO;
    }

    public static List<OrderItemDO> createOrderItemListByModifyItem(OrderModifyItemRequest request,
                                                                    OrderPriceCalculationResponse calculationResponse) {
        Long operator = request.getOperator();
        Map<Long, OrderPriceCalculationResponse.PriceCalculationItem> itemMap = calculationResponse.getItemPriceList().stream()
                .collect(Collectors.toMap(OrderPriceCalculationResponse.PriceCalculationItem::getItemId, Function.identity()));

        List<Long> itemIdList = request.getItemIdList();
        List<OrderItemDO> list = new ArrayList<>(itemIdList.size());
        for (Long itemId : itemIdList) {
            OrderPriceCalculationResponse.PriceCalculationItem item = itemMap.get(itemId);

            OrderItemDO orderItemDO = new OrderItemDO();
            orderItemDO.setOrderId(request.getOrderId());
            orderItemDO.setItemId(itemId);
            orderItemDO.setItemName(item.getName());
            orderItemDO.setNumber(item.getNumber());
            orderItemDO.setPrice(item.getPrice());
            orderItemDO.setTotalPrice(item.getTotalPrice());
            orderItemDO.setCreateBy(operator);
            orderItemDO.setUpdateBy(operator);
            orderItemDO.setDeleted(DelUtils.NO_DELETED);
            list.add(orderItemDO);
        }
        return list;
    }

    public static OrderInvoiceDO createOrderInvoiceByCreate(OrderInvoiceCreateRequest request) {
        OrderInvoiceDO orderInvoiceDO = new OrderInvoiceDO();
        orderInvoiceDO.setBillId(request.getBillId());
        orderInvoiceDO.setNeedCertificate(OrderInvoiceNeedCertificate.NEED.getStatus());
        orderInvoiceDO.setInvoiceType(request.getInvoiceType());
        orderInvoiceDO.setEmail(request.getEmail());
        orderInvoiceDO.setTaxpayerName(request.getTaxpayerName());
        orderInvoiceDO.setTaxpayerNo(request.getTaxpayerNo());
        orderInvoiceDO.setCreateBy(request.getOperator());
        orderInvoiceDO.setUpdateBy(request.getOperator());
        return orderInvoiceDO;
    }

    public static List toOrderBillBResponseList(List<OrderBillDO> list,
                                                Map<Long, OrderPayDO> orderPayMap,
                                                Map<Long, String> orderItemRef,
                                                Map<Long, OrderInvoiceResponse> invoiceMap) {
        List<OrderBillBResponse> result = new ArrayList<>(list.size());
        for (OrderBillDO billDO : list) {
            Long billId = billDO.getId();
            OrderPayDO orderPayDO = orderPayMap.get(billId);
            String items = orderItemRef.get(billDO.getOrderId());

            OrderBillBResponse bill = new OrderBillBResponse();
            bill.setId(billDO.getId());
            bill.setOrderId(billDO.getOrderId());
            bill.setBillType(billDO.getBillType());
            bill.setBillSubType(billDO.getBillSubType());
            bill.setPriceReceivable(billDO.getPriceReceivable());
            bill.setPriceReceived(billDO.getPriceReceived());
            bill.setPriceRefundable(billDO.getPriceRefundable());
            bill.setPriceRefunded(billDO.getPriceRefunded());
            bill.setItemNames(items);
            bill.setRemark(billDO.getRemark());
            bill.setDiscount(billDO.getDiscount());
            bill.setTradeType(billDO.getTradeType());
            bill.setPayStatus(OrderPayStatus.UNPAY.getStatus());
            bill.setCreateTime(DateUtils.dateTimeStr(billDO.getCreatedAt()));
            bill.setStartTime(billDO.getStartTime());
            bill.setEndTime(billDO.getEndTime());
            bill.setInvoice(invoiceMap.get(billId));
            bill.setNursingList(OrderNursingSimpleResponse.of(billDO.getNursingJson()));
            bill.setPatientName(billDO.getPatientName());
            if (Objects.nonNull(orderPayDO)) {
                bill.setPayStatus(orderPayDO.getPayStatus());
                bill.setPayTime(orderPayDO.getPayTime());
            }
            result.add(bill);
        }
        return result;
    }

    public static List<OrderBillExportDTO> toOrderBillExportDTO(List<OrderBillDO> list,
                                                Map<Long, OrderPayDO> orderPayMap,
                                                Map<Long, String> orderItemRef,
                                                Map<Long, OrderInvoiceResponse> invoiceMap) {
        List<OrderBillExportDTO> result = new ArrayList<>(list.size());
        for (OrderBillDO billDO : list) {
            Long billId = billDO.getId();
            OrderPayDO orderPayDO = orderPayMap.get(billId);
            String items = orderItemRef.get(billDO.getOrderId());

            OrderBillExportDTO bill = new OrderBillExportDTO();
            bill.setId(billDO.getId());
            bill.setOrderId(billDO.getOrderId());
            bill.setBillType(billDO.getBillType());
            bill.setBillSubType(billDO.getBillSubType());
            bill.setItemNames(items);
            bill.setTradeType(billDO.getTradeType());
            bill.setPayStatus(OrderPayStatus.UNPAY.getStatus());
            bill.setCreateTime(DateUtils.dateTimeStr(billDO.getCreatedAt()));
            if (Objects.nonNull(orderPayDO)) {
                bill.setPayStatus(orderPayDO.getPayStatus());
                bill.setPayTime(orderPayDO.getPayTime());
            }
            OrderInvoiceResponse invoice = invoiceMap.get(billId);
            if (Objects.nonNull(invoice)) {
                bill.setNeedCertificate(invoice.getNeedCertificate());
                bill.setInvoiceType(invoice.getInvoiceType());
                bill.setEmail(invoice.getEmail());
                bill.setTaxpayerName(invoice.getTaxpayerName());
                bill.setTaxpayerNo(invoice.getTaxpayerNo());
            }
            result.add(bill);
        }
        return result;
    }

    public static OrderInvoiceResponse toOrderInvoiceResponse(OrderInvoiceDO orderInvoiceDO) {
        if (Objects.isNull(orderInvoiceDO)) {
            return null;
        }
        OrderInvoiceResponse orderInvoiceResponse = new OrderInvoiceResponse();
        orderInvoiceResponse.setId(orderInvoiceDO.getId());
        orderInvoiceResponse.setOrderId(orderInvoiceDO.getOrderId());
        orderInvoiceResponse.setBillId(orderInvoiceDO.getBillId());
        orderInvoiceResponse.setNeedCertificate(orderInvoiceDO.getNeedCertificate());
        orderInvoiceResponse.setInvoiceType(orderInvoiceDO.getInvoiceType());
        orderInvoiceResponse.setEmail(orderInvoiceDO.getEmail());
        orderInvoiceResponse.setTaxpayerName(orderInvoiceDO.getTaxpayerName());
        orderInvoiceResponse.setTaxpayerNo(orderInvoiceDO.getTaxpayerNo());
        return orderInvoiceResponse;
    }

    /**
     * 创建一个现金支付单
     * @param bill
     * @return
     */
    public static OrderPayDO createOrderPayByCash(OrderBillDO bill,Long operator) {
        Integer price = bill.getPriceReceivable() - bill.getPriceReceived();
        Integer priceRefund = bill.getPriceRefundable() - bill.getPriceReceived();
        Integer realPrice = price > 0 ? price : (priceRefund * -1);

        OrderPayDO orderPayDO = new OrderPayDO();
        orderPayDO.setOrderId(bill.getOrderId());
        orderPayDO.setBillId(bill.getId());
        orderPayDO.setOutTradeNo(OrderConst.EMPTY);
        orderPayDO.setTradeType(OrderTradeType.CASH.getStatus());
        orderPayDO.setPrice(realPrice);
        orderPayDO.setPayTime(DateUtils.dateTimeStr20());
        orderPayDO.setPayOutTime(DateUtils.dateTimeStr20());
        orderPayDO.setRefundAmount(OrderConst.ZERO);
        orderPayDO.setRemark(OrderConst.DEFAULT_REMARK);
        orderPayDO.setPayStatus(OrderPayStatus.SUCCESS.getStatus());
        orderPayDO.setPayResult(OrderPayStatus.SUCCESS.getDescribe());
        orderPayDO.setPayInfo("");
        orderPayDO.setCreateBy(operator);
        orderPayDO.setUpdateBy(operator);
        orderPayDO.setDeleted(DelUtils.NO_DELETED);
        return orderPayDO;
    }

    public static CashResponse toCashResponse(CashLogDO cashLogDO) {
        CashResponse cashResponse = new CashResponse();
        cashResponse.setId(cashLogDO.getId());
        cashResponse.setType(cashLogDO.getType());
        cashResponse.setPrice(cashLogDO.getPrice());
        cashResponse.setBizType(cashLogDO.getBizType());
        cashResponse.setBizId(cashLogDO.getBizId());
        cashResponse.setPersonId(cashLogDO.getPersonId());
        cashResponse.setPersonName(cashLogDO.getPersonName());
        cashResponse.setCreateAt(DateUtils.dateTimeStr(cashLogDO.getCreatedAt()));
        cashResponse.setCashSource(cashLogDO.getCashSource());
        return cashResponse;
    }

    public static CashLogDO createCashLogByExtract(CashExtractRequest request) {
        Long operator = request.getOperator();
        Operator operatorUser = request.getOperatorUser();

        CashLogDO cashLogDO = new CashLogDO();
        cashLogDO.setType(CashType.EXTRACT.getStatus());
        cashLogDO.setPrice(request.getPrice());
        cashLogDO.setCashSource(request.getCashSource());
        cashLogDO.setBizType(CashBizType.PERSON_EXTRACT.getStatus());
        if (Objects.equals(CashSource.ALIPAY.getStatus(),request.getCashSource())) {
            cashLogDO.setBizType(CashBizType.ALIPAY_EXTRACT.getStatus());
        }
        cashLogDO.setBizId(0L);
        cashLogDO.setPersonId(operatorUser.getId());
        cashLogDO.setPersonName(operatorUser.getName());
        cashLogDO.setCreateBy(operator);
        cashLogDO.setUpdateBy(operator);
        cashLogDO.setDeleted(DelUtils.NO_DELETED);
        return cashLogDO;
    }

    public static CashLogDO createCashLogByDeposit(CashDepositRequest request) {
        Long operator = request.getOperator();
        Operator operatorUser = request.getOperatorUser();

        CashLogDO cashLogDO = new CashLogDO();
        cashLogDO.setType(CashType.ADD.getStatus());
        cashLogDO.setPrice(request.getPrice());
        cashLogDO.setCashSource(request.getCashSource());
        cashLogDO.setBizType(CashBizType.PERSON_DEPOSIT.getStatus());
        if (Objects.equals(CashSource.ALIPAY.getStatus(),request.getCashSource())) {
            cashLogDO.setBizType(CashBizType.ALIPAY_DEPOSIT.getStatus());
        }
        cashLogDO.setBizId(0L);
        cashLogDO.setPersonId(operatorUser.getId());
        cashLogDO.setPersonName(operatorUser.getName());
        cashLogDO.setCreateBy(operator);
        cashLogDO.setUpdateBy(operator);
        cashLogDO.setDeleted(DelUtils.NO_DELETED);
        return cashLogDO;
    }

    public static CashLogDO createCashLogByBillCashPay(OrderBillDO orderBill,Operator operator,Integer cashSource) {
        Long operatorId = operator.getId();
        String operatorName = operator.getName();

        Integer pricePay = orderBill.getPriceReceivable();
        Integer priceRefund = orderBill.getPriceRefundable();

        Integer price = pricePay;
        Integer cashType = CashType.ADD.getStatus();
        if (priceRefund > 0) {
            cashType = CashType.EXTRACT.getStatus();
            price = priceRefund;
        }

        CashLogDO cashLog = new CashLogDO();
        cashLog.setType(cashType);
        cashLog.setPrice(price);
        cashLog.setBizType(CashBizType.BILL.getStatus());
        cashLog.setBizId(orderBill.getId());
        cashLog.setPersonId(operatorId);
        cashLog.setPersonName(operatorName);
        cashLog.setCreateBy(operatorId);
        cashLog.setUpdateBy(operatorId);
        cashLog.setDeleted(DelUtils.NO_DELETED);
        cashLog.setCashSource(cashSource);
        return cashLog;
    }

    public static OrderBillCreateRequest createOrderBillCreateRequestBySettle(OrderDO orderDO,
                                                                              OrderBillDO totalBill,
                                                                              Integer consumerPrice,
                                                                              Integer billPrice,
                                                                              Operator operatorUser) {
        Long orderId = orderDO.getId();

        OrderBillCreateRequest settleBillReq = new OrderBillCreateRequest();
        settleBillReq.setOrderId(orderId);
        settleBillReq.setBizId(orderId.toString());
        settleBillReq.setParentBillId(totalBill.getId());
        settleBillReq.setBillType(OrderBillType.SETTLE.getStatus());
        settleBillReq.setBillSubType(OrderBillSubType.NORMAL.getStatus());
        settleBillReq.setDiscount(orderDO.getDiscount());
        settleBillReq.setOperatorUser(operatorUser);
        settleBillReq.setRemark(OrderConst.EMPTY);
        if (consumerPrice < billPrice) {
            // 消耗小于账单給的金额,需要退款
            Integer priceRefundable = billPrice - consumerPrice;
            settleBillReq.setPriceReceivable(0);
            settleBillReq.setPriceRefundable(priceRefundable);
        }
        if (consumerPrice >= billPrice) {
            // 消耗大于账单給的金额,需要支付
            Integer priceRefundable = consumerPrice - billPrice;
            settleBillReq.setPriceReceivable(priceRefundable);
            settleBillReq.setPriceRefundable(0);
        }
        settleBillReq.setStartTime(DateUtils.toyyyyMMddHHmmssStr(orderDO.getRealStartTime()));
        settleBillReq.setEndTime(DateUtils.toyyyyMMddHHmmssStr(orderDO.getRealEndTime()));
        settleBillReq.setPatientName(orderDO.getPatientName());
        settleBillReq.setNursingJson(StrUtils.EMPTY);
        return settleBillReq;
    }

    public static OrderBillPayResponse createOrderBillPayResponseByCreate(OrderPayDO payDO,
                                                                          OrderBillPayInfoResponse payInfo) {
        OrderBillPayResponse orderBillPayResponse = new OrderBillPayResponse();
        orderBillPayResponse.setId(payDO.getId());
        orderBillPayResponse.setOrderId(payDO.getOrderId());
        orderBillPayResponse.setBillId(payDO.getBillId());
        orderBillPayResponse.setOutTradeNo(payDO.getOutTradeNo());
        orderBillPayResponse.setTradeType(payDO.getTradeType());
        orderBillPayResponse.setPrice(payDO.getPrice());
        orderBillPayResponse.setPayTime(payDO.getPayTime());
        orderBillPayResponse.setPayOutTime(payDO.getPayOutTime());
        orderBillPayResponse.setRefundAmount(payDO.getRefundAmount());
        orderBillPayResponse.setRemark(payDO.getRemark());
        orderBillPayResponse.setPayStatus(payDO.getPayStatus());
        orderBillPayResponse.setPayInfo(payInfo);
        return orderBillPayResponse;
    }

    public static OrderBillPayInfoResponse toOrderBillPayInfoResponse(String appId,String  mchid, String mchKey,
                                                                      WxPayUnifiedOrderResult jsapiResult) {
        Long timestamp = System.currentTimeMillis() / 1000;
        OrderBillPayInfoResponse orderBillPayInfoResponse = new OrderBillPayInfoResponse();
        orderBillPayInfoResponse.setAppId(appId);
        orderBillPayInfoResponse.setNonceStr(jsapiResult.getNonceStr());
        orderBillPayInfoResponse.setPackageValue("prepay_id=" + jsapiResult.getPrepayId());
        orderBillPayInfoResponse.setSignType("MD5");
        orderBillPayInfoResponse.setPrepayid(jsapiResult.getPrepayId());
        orderBillPayInfoResponse.setPartnerid(mchid);
        orderBillPayInfoResponse.setTimeStamp(timestamp.toString());

        String sign = WechatUtil.paySign(orderBillPayInfoResponse, mchKey);
        orderBillPayInfoResponse.setPaySign(sign);
        return orderBillPayInfoResponse;
    }

    public static OrderPresetValueDO createOrderPresetValue(OrderModifyNursingRequest request) {
        Long operator = request.getOperator();
        OrderPresetValueDO orderPresetValueDO = new OrderPresetValueDO();
        orderPresetValueDO.setOrderId(request.getOrderId());
        orderPresetValueDO.setTime(request.getPresetTime());
        orderPresetValueDO.setType(OrderPresetValueType.NURSING.getStatus());
        orderPresetValueDO.setVal(StrUtils.longListToStr(request.getNursingIdList()));
        orderPresetValueDO.setStatus(OrderPresetValueStatus.NO.getStatus());
        orderPresetValueDO.setCreateBy(operator);
        orderPresetValueDO.setUpdateBy(operator);
        orderPresetValueDO.setDeleted(DelUtils.NO_DELETED);
        return orderPresetValueDO;
    }

    public static List<OrderBillExportDTO> toOrderBillExportDTOList(List<OrderBillBResponse> records) {
        if (CollUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        List<OrderBillExportDTO> list = records.stream()
                .map(bill -> OrderBuilder.toOrderBillExport(bill))
                .collect(Collectors.toList());
        return list;
    }

    public static OrderBillExportDTO toOrderBillExport(OrderBillBResponse bill) {
        OrderBillExportDTO orderBillExportDTO = new OrderBillExportDTO();
        orderBillExportDTO.setOrderId(bill.getOrderId());
        orderBillExportDTO.setId(bill.getId());
        orderBillExportDTO.setBillType(bill.getBillType());
        orderBillExportDTO.setBillSubType(bill.getBillSubType());
        orderBillExportDTO.setPayStatus(bill.getPayStatus());
        orderBillExportDTO.setPrice(bill.getPrice());
        orderBillExportDTO.setCreateTime(bill.getCreateTime());
        orderBillExportDTO.setTradeType(bill.getTradeType());
        orderBillExportDTO.setPayTime(bill.getPayTime());
        orderBillExportDTO.setItemNames(bill.getItemNames());
        if (Objects.nonNull(bill.getInvoice())) {
            OrderInvoiceResponse invoice = bill.getInvoice();
            orderBillExportDTO.setNeedCertificate(invoice.getNeedCertificate());
            orderBillExportDTO.setInvoiceType(invoice.getInvoiceType());
            orderBillExportDTO.setEmail(invoice.getEmail());
            orderBillExportDTO.setTaxpayerName(invoice.getTaxpayerName());
            orderBillExportDTO.setTaxpayerNo(invoice.getTaxpayerNo());
        }
        orderBillExportDTO.setPatientName(bill.getPatientName());
        orderBillExportDTO.setStartTime(DateUtils.convertStr(bill.getStartTime()));
        orderBillExportDTO.setEndTime(DateUtils.convertStr(bill.getEndTime()));
        orderBillExportDTO.setNursingInfo(toNursingInfo(bill.getNursingList()));
        return orderBillExportDTO;
    }

    public static String toNursingInfo(List<OrderNursingSimpleResponse> nursingList) {
        if (CollUtils.isEmpty(nursingList)) {
            return StrUtils.EMPTY;
        }
        return nursingList.stream().map(nursing -> nursing.getName())
               .collect(Collectors.joining(","));
    }

    public static OrderConsumerPageRequest toOrderConsumerPageRequest(OrderConsumerExportPageRequest request) {
        OrderConsumerPageRequest orderConsumerPageRequest = new OrderConsumerPageRequest();
        orderConsumerPageRequest.setOrderId(request.getOrderId());
        orderConsumerPageRequest.setStartTime(request.getStartTime());
        orderConsumerPageRequest.setEndTime(request.getEndTime());
        orderConsumerPageRequest.setVersion(request.getVersion());

        Operator operatorUser = new Operator();
        operatorUser.setId(request.getOperatorUser().getId());
        operatorUser.setMobile(request.getOperatorUser().getMobile());
        operatorUser.setName(request.getOperatorUser().getName());
        operatorUser.setSource(request.getOperatorUser().getSource());
        orderConsumerPageRequest.setOperatorUser(operatorUser);
        orderConsumerPageRequest.setPageIndex(request.getPageIndex());
        orderConsumerPageRequest.setPageSize(request.getPageSize());
        orderConsumerPageRequest.setDate(request.getDate());
        return orderConsumerPageRequest;
    }

    public static List<OrderConsumerlogExportDTO> toOrderConsumerlogExportDTOList(List<OrderConsumerLogResponse> records) {
        if (CollUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        List<OrderConsumerlogExportDTO> list = records.stream()
               .map(orderConsumerLogResponse -> OrderBuilder.toOrderConsumerlogExport(orderConsumerLogResponse))
               .collect(Collectors.toList());
        return list;
    }

    private static OrderConsumerlogExportDTO toOrderConsumerlogExport(OrderConsumerLogResponse orderConsumerLogResponse) {
        OrderConsumerlogExtraNursingInfoDTO extraNursingInfoDTO = OrderConsumerlogExtraNursingInfoDTO.buildByList(orderConsumerLogResponse.getNursingList());

        OrderConsumerlogExportDTO orderConsumerlogExportDTO = new OrderConsumerlogExportDTO();
        orderConsumerlogExportDTO.setOrderId(orderConsumerLogResponse.getOrderId());
        orderConsumerlogExportDTO.setPatientName(orderConsumerLogResponse.getPatientName());
        orderConsumerlogExportDTO.setItemName(orderConsumerLogResponse.getItemName());
        orderConsumerlogExportDTO.setDate(orderConsumerLogResponse.getDate());
        orderConsumerlogExportDTO.setTotalPrice(orderConsumerLogResponse.getTotalPrice());
        orderConsumerlogExportDTO.setPriceCertifiedProperty(orderConsumerLogResponse.getPriceCertifiedProperty());
        orderConsumerlogExportDTO.setRateCertifiedProperty(orderConsumerLogResponse.getRateCertifiedProperty());
        orderConsumerlogExportDTO.setPriceNursing(orderConsumerLogResponse.getPriceNursing());
        orderConsumerlogExportDTO.setRateNursing(orderConsumerLogResponse.getRateNursing());
        orderConsumerlogExportDTO.setPriceHospital(orderConsumerLogResponse.getPriceHospital());
        orderConsumerlogExportDTO.setRateHospital(orderConsumerLogResponse.getRateHospital());
        orderConsumerlogExportDTO.setVersion(orderConsumerLogResponse.getVersion());
        orderConsumerlogExportDTO.setItemId(orderConsumerLogResponse.getItemId());
        orderConsumerlogExportDTO.setNursingOrder(extraNursingInfoDTO.exportInfo());
        return orderConsumerlogExportDTO;
    }
}
