package com.avatar.hospital.chaperone.service.order.dto;

import lombok.Data;

import javax.annotation.Nullable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-02 15:55
 **/
@Data
public class OrderPriceCalculationParam {

    /**
     * 订单ID
     */
    @Nullable
    private Long orderId;

    /**
     * 操作人
     */
    @Nullable
    private Long operator;

    /**
     * 开始时间
     */
    private Integer statTime = null;
    /**
     * 结束时间
     */
    private Integer endTime = null;
    /**
     * 套餐ID
     */
    private List<Long> itemIdList = null;
    /**
     * 则扣
     */
    private Integer discount = null;

    public static OrderPriceCalculationParam buildByModifyDiscount(Long orderId, Long operator, Integer discount) {
        OrderPriceCalculationParam param = new OrderPriceCalculationParam();
        param.setOrderId(orderId);
        param.setOperator(operator);
        param.setDiscount(discount);
        return param;
    }

    public static OrderPriceCalculationParam buildByModifyItem(Long orderId, Long operator, List<Long> itemIdList) {
        OrderPriceCalculationParam param = new OrderPriceCalculationParam();
        param.setOrderId(orderId);
        param.setOperator(operator);
        param.setItemIdList(itemIdList);
        return param;
    }

    public static OrderPriceCalculationParam buildByCommitCompleteInfo(Long orderId, Long operator) {
        OrderPriceCalculationParam param = new OrderPriceCalculationParam();
        param.setOrderId(orderId);
        param.setOperator(operator);
        return param;
    }
}
