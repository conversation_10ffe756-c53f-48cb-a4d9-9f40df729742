package com.avatar.hospital.chaperone.builder.nursing;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDayDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingOrderDO;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus;
import com.avatar.hospital.chaperone.request.nursing.NursingDayPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingSubstituteRequest;
import com.avatar.hospital.chaperone.response.nursing.NursingDayOrderResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse;
import com.avatar.hospital.chaperone.service.nursing.dto.NursingDayExportDTO;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author:sp0420
 * @Description:
 */
public class NursingDayBuilder {

    public static NursingDayDO buildNursingDayDO(NursingSubstituteRequest request,Integer date) {
        if (request == null) {
            return null;
        }
        NursingDayDO nursingDayDO = new NursingDayDO();
//        nursingDayDO.setId(IdUtils.getId());
        nursingDayDO.setDate(date);
        nursingDayDO.setNursingId(request.getNursingId());
        nursingDayDO.setNursingName(request.getNursingName());
        nursingDayDO.setStatus(NursingDayStatus.BUSY.getStatus());
        nursingDayDO.setDeleted(DelUtils.NO_DELETED);
        nursingDayDO.setCreateBy(request.getUpdateBy());
        nursingDayDO.setCreatedAt(LocalDateTime.now());
        nursingDayDO.setUpdateBy(request.getUpdateBy());
        nursingDayDO.setUpdatedAt(LocalDateTime.now());
        return nursingDayDO;
    }

    public static NursingDayDO buildSubstituteNursingDayDO(NursingDayDO request,Integer afterStatus) {
        if (request == null) {
            return null;
        }
        NursingDayDO nursingDayDO = new NursingDayDO();
        nursingDayDO.setId(request.getId());
        nursingDayDO.setStatus(afterStatus);
        nursingDayDO.setUpdateBy(request.getUpdateBy());
        nursingDayDO.setUpdatedAt(LocalDateTime.now());
        return nursingDayDO;
    }

    public static NursingDayDO buildLeaveNursingDayDO(NursingDayDO request) {
        if (request == null) {
            return null;
        }
        NursingDayDO nursingDayDO = new NursingDayDO();
        nursingDayDO.setId(request.getId());
        nursingDayDO.setStatus(NursingDayStatus.LEAVE.getStatus());
        nursingDayDO.setUpdateBy(request.getUpdateBy());
        nursingDayDO.setUpdatedAt(LocalDateTime.now());
        return nursingDayDO;
    }

    public static NursingDayDO buildCancelLeaveNursingDayDO(NursingDayDO request) {
        if (request == null) {
            return null;
        }
        NursingDayDO nursingDayDO = new NursingDayDO();
        nursingDayDO.setId(request.getId());
        nursingDayDO.setStatus(NursingDayStatus.FREE.getStatus());
        nursingDayDO.setUpdateBy(request.getUpdateBy());
        nursingDayDO.setUpdatedAt(LocalDateTime.now());
        return nursingDayDO;
    }

    public static List<NursingDayDO> buildNursingDayDO(List<String> dateList, Long nursingId, String name, Long createBy) {
        if (CollectionUtils.isEmpty(dateList) || nursingId == null || name == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        LocalDateTime now = LocalDateTime.now();
        List<NursingDayDO> doList = new ArrayList<>();
        for (String date : dateList) {
            NursingDayDO nursingDayDO = new NursingDayDO();
            nursingDayDO.setId(IdUtils.getId());
            nursingDayDO.setDate(Integer.valueOf(date));
            nursingDayDO.setNursingId(nursingId);
            nursingDayDO.setNursingName(name);
            nursingDayDO.setStatus(NursingDayStatus.FREE.getStatus());
            nursingDayDO.setDeleted(DelUtils.NO_DELETED);
            nursingDayDO.setCreateBy(createBy);
            nursingDayDO.setCreatedAt(LocalDateTime.now());
            nursingDayDO.setUpdateBy(createBy);
            nursingDayDO.setUpdatedAt(LocalDateTime.now());
            doList.add(nursingDayDO);
        }
        return doList;
    }

    public static NursingOrderDO createNursingOrderDO(Integer date, Long orderId, Long nursingId) {
        NursingOrderDO nursingOrderDO = new NursingOrderDO();
        nursingOrderDO.setDate(date);
        nursingOrderDO.setNursingId(nursingId);
        nursingOrderDO.setOrderId(orderId);
        nursingOrderDO.setCreateBy(OrderConst.JOB_OPERATOR.getId());
        nursingOrderDO.setUpdateBy(OrderConst.JOB_OPERATOR.getId());
        nursingOrderDO.setDeleted(DelUtils.NO_DELETED);
        return nursingOrderDO;
    }

    public static NursingDayDO createNursingDayDO(Integer date, Long nursingId, String name) {
        NursingDayDO nursingDayDO = new NursingDayDO();
        nursingDayDO.setDate(date);
        nursingDayDO.setNursingId(nursingId);
        nursingDayDO.setNursingName(name);
        nursingDayDO.setStatus(NursingDayStatus.FREE.getStatus());
        nursingDayDO.setCreateBy(OrderConst.JOB_OPERATOR.getId());
        nursingDayDO.setUpdateBy(OrderConst.JOB_OPERATOR.getId());
        nursingDayDO.setDeleted(DelUtils.NO_DELETED);
        return nursingDayDO;
    }

    public static NursingDayExportDTO buildNursingDayExportDTO(NursingDayPagingResponse response) {
        NursingDayExportDTO nursingDayExportDTO = new NursingDayExportDTO();
        nursingDayExportDTO.setId(response.getId());
        nursingDayExportDTO.setNursingId(response.getNursingId());
        nursingDayExportDTO.setNursingName(Objects.isNull(response.getNursingName()) ? null : response.getNursingName());
        nursingDayExportDTO.setDate(Objects.isNull(response.getDate()) ? null : response.getDate());
        nursingDayExportDTO.setStatusDescribe(Objects.isNull(response.getStatus()) ? null : NursingDayStatus.of(response.getStatus()).getDescribe());
        if (!CollectionUtils.isEmpty(response.getOrderList())){
            String collect = response.getOrderList().stream().map(NursingDayOrderResponse::getOrderId).map(String::valueOf).limit(1000).collect(Collectors.joining("/"));
            nursingDayExportDTO.setOrderListToString(collect);
        }
        return nursingDayExportDTO;
    }
}
