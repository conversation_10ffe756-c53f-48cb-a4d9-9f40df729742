package com.avatar.hospital.chaperone.template.serialize;

import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 时间戳 -> 时间戳序列化 yyyyMMddHH
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 16:36
 **/
@Slf4j
public class ToDateForStringDeserializer extends JsonDeserializer<String> {
    /**
     *
     * @throws IOException
     */
    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
        String value = jsonParser.getText();
        try {
            if (!StrUtils.hasText(value)) {
                return StrUtils.EMPTY;
            }
            String date = DateUtils.toDateHForMilliStr(value);
            return date;
        } catch (NumberFormatException e) {
            log.error("ToDateForStringDeserializer long parse error", e);
            return null;
        }
    }
}
