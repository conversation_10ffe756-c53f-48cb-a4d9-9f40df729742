package com.avatar.hospital.chaperone.service.baccount;

import com.avatar.hospital.chaperone.request.baccount.RoleAddRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleAllocationMenuRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleMenuRequest;
import com.avatar.hospital.chaperone.request.baccount.RolePagingRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleAddResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleAllocationMenuResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleMenuResponse;
import com.avatar.hospital.chaperone.response.baccount.RolePagingResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleUpdateResponse;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
public interface RoleService {

    /**
     * 添加角色
     *
     * @param request -
     * @return -
     */
    RoleAddResponse add(RoleAddRequest request);

    /**
     * 更新角色
     *
     * @param request -
     * @return -
     */
    RoleUpdateResponse update(RoleUpdateRequest request);

    /**
     * 删除角色
     *
     * @param request -
     * @return -
     */
    RoleDeleteResponse delete(RoleDeleteRequest request);

    /**
     * 角色分配菜单权限
     *
     * @param request -
     * @return -
     */
    RoleAllocationMenuResponse allocationMenu(RoleAllocationMenuRequest request);

    /**
     * 角色列表
     *
     * @param request -
     * @return -
     */
    PageResponse<RolePagingResponse> paging(RolePagingRequest request);

    /**
     * 角色详情
     *
     * @param roleId -
     * @return -
     */
    RoleDetailResponse detail(Long roleId);

    /**
     * 查询角色关联的权限
     *
     * @param request -
     * @return -
     */
    RoleMenuResponse menu(RoleMenuRequest request);

}
