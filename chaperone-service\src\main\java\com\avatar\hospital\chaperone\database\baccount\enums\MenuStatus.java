package com.avatar.hospital.chaperone.database.baccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum MenuStatus {

    /**
     * 禁用
     */
    DISABLE(0, "禁用"),


    /**
     * 启用
     */
    ENABLE(1, "启用"),


    ;

    private final Integer status;

    private final String describe;


    MenuStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static MenuStatus of(Integer status) {
        if (status == null) {
            return null;
        }
        for (MenuStatus menuStatus : values()) {
            if (status.equals(menuStatus.getStatus())) {
                return menuStatus;
            }
        }
        return null;
    }
}
