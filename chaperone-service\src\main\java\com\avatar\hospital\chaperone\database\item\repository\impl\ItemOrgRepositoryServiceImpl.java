package com.avatar.hospital.chaperone.database.item.repository.impl;

import com.avatar.hospital.chaperone.database.item.dataobject.ItemOrgDO;
import com.avatar.hospital.chaperone.database.item.mapper.ItemOrgMapper;
import com.avatar.hospital.chaperone.database.item.repository.ItemOrgRepositoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 套餐-机构; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class ItemOrgRepositoryServiceImpl extends ServiceImpl<ItemOrgMapper, ItemOrgDO> implements ItemOrgRepositoryService {

}
