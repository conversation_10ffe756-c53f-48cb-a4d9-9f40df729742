package com.avatar.hospital.chaperone.consumer.controller.repair;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.repair.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.repair.*;
import com.avatar.hospital.chaperone.service.repair.RepairFormFeedbackService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  C端工程-报修单反馈
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-30 14:29
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/repair/feedback")
@Deprecated
public class RepairFormFeedbackController {
    private final RepairFormFeedbackService repairFormFeedbackService;

    /**
     * 查询-分页
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<RepairFormFeedbackResponse>> paging(@Validated @RequestBody RepairFormFeedbackPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormFeedbackService.pagingForC(request);
        });
    }

    /**
     * 创建
     */
    @PostMapping("create")
    public SingleResponse<RepairFormFeedbackIdResponse> create(@RequestBody RepairFormFeedbackCreateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormFeedbackService.create(request);
        });
    }

}
