package com.avatar.hospital.chaperone.response.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 16:59
 **/
@Data
public class OrderConsumerLogCResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 日期,格式yyyyMMdd
     */
    private Integer date;

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 关联套餐
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemId;


    /**
     * 关联套餐名称
     */
    private String itemName;


    /**
     * 总金额：分
     */
    private Integer totalPrice;

    /**
     * 护工ID
     */
    private Long nursingId;

    /**
     * 护工名称
     */
    private String nursingName;
}
