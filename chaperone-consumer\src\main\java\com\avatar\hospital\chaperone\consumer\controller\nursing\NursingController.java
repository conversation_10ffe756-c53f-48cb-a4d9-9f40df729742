package com.avatar.hospital.chaperone.consumer.controller.nursing;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.response.nursing.NursingDetailResponse;
import com.avatar.hospital.chaperone.service.nursing.NursingService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/nursing")
public class NursingController {
    private final NursingService nursingService;

    @GetMapping("/{id}")
    public SingleResponse<NursingDetailResponse> detail(@PathVariable("id") Long nursingId) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("NursingController detail request:{}", nursingId);
            AssertUtils.isNotNull(nursingId, ErrorCode.PARAMETER_ERROR);
            return nursingService.detail(nursingId);
        });
    }


}
