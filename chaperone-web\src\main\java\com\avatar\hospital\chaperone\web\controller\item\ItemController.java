package com.avatar.hospital.chaperone.web.controller.item;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.database.item.enums.ItemServerType;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.item.ItemCreateRequest;
import com.avatar.hospital.chaperone.request.item.ItemModifyRequest;
import com.avatar.hospital.chaperone.request.item.ItemPageRequest;
import com.avatar.hospital.chaperone.request.item.ItemRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.item.ItemIdResponse;
import com.avatar.hospital.chaperone.response.item.ItemResponse;
import com.avatar.hospital.chaperone.service.item.ItemService;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import com.avatar.hospital.chaperone.web.validator.item.ItemValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * B端套餐管理
 * @program: hospital-chaperone
 * @description: 套餐
 * @author: sp0372
 * @create: 2023-10-13 10:07
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/item")
public class ItemController {
    private final ItemService itemService;

    /**
     * 创建套餐
     */
    @PostMapping("create")
    public SingleResponse<ItemIdResponse> create(@RequestBody ItemCreateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            ItemValidator.createValidate(request);
            return itemService.create(request);
        });
    }

    /**
     * 修改套餐
     */
    @Idempotent(value = OrderKey.ITEM_MODIFY_PREFIX + " + #request.id")
    @PostMapping("modify")
    public SingleResponse<ItemIdResponse> modify(@Validated @RequestBody ItemModifyRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            ItemValidator.modifyValidate(request);
            return itemService.modify(request);
        });
    }

    /**
     * 查询分页
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<ItemResponse>> paging(@RequestBody ItemPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return itemService.paging(request);
        });
    }

    /**
     * 查询详情
     */
    @PostMapping("get")
    public SingleResponse<ItemResponse> getById(@RequestBody ItemRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return itemService.detail(request);
        });
    }

    /**
     * 上架
     */
    @Idempotent(value = OrderKey.ITEM_MODIFY_PREFIX + " + #request.itemId")
    @PostMapping("no")
    public SingleResponse<ItemResponse> no(@RequestBody ItemRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            ItemValidator.noValidate(request);
            return itemService.on(request);
        });
    }

    /**
     * 下架
     */
    @Idempotent(value = OrderKey.ITEM_MODIFY_PREFIX + " + #request.itemId")
    @PostMapping("off")
    public SingleResponse<ItemResponse> off(@RequestBody ItemRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            ItemValidator.offValidate(request);
            return itemService.off(request);
        });
    }

    /**
     * 开启外显
     */
    @Idempotent(value = OrderKey.ITEM_MODIFY_PREFIX + " + #request.itemId")
    @PostMapping("no-display")
    public SingleResponse<ItemResponse> noDisplay(@RequestBody ItemRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            ItemValidator.noValidate(request);
            return itemService.onDisplay(request);
        });
    }

    /**
     * 关闭外显
     */
    @Idempotent(value = OrderKey.ITEM_MODIFY_PREFIX + " + #request.itemId")
    @PostMapping("off-display")
    public SingleResponse<ItemResponse> offDisplay(@RequestBody ItemRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            ItemValidator.offValidate(request);
            return itemService.offDisplay(request);
        });
    }

    /**
     * 枚举-获取服务支持类型(serveType)
     */
    @PostMapping("enum/serveType")
    public SingleResponse<Map<Integer,String>> enumServeType() {
        return TemplateProcess.doProcess(log, () -> {
            return ItemServerType.toMap();
        });
    }

    /**
     * 枚举-错误代码(errorCode)
     */
    @PostMapping("enum/errorCode")
    public SingleResponse<Map<String,String>> enumErrorCode() {
        return TemplateProcess.doProcess(log, () -> {
            return ErrorCode.toMap();
        });
    }
}
