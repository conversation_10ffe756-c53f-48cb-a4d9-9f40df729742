package com.avatar.hospital.chaperone.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.builder.order.OrderBuilder;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingRepositoryService;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderConsumerlogDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderNursingDO;
import com.avatar.hospital.chaperone.database.order.dto.OrderConsumerlogExtraNursingInfoDTO;
import com.avatar.hospital.chaperone.database.order.repository.OrderConsumerlogRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderItemRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderNursingRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.item.ItemResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.item.ItemService;
import com.avatar.hospital.chaperone.service.order.OrderConsumerlogService;
import com.avatar.hospital.chaperone.service.order.OrderService;
import com.avatar.hospital.chaperone.service.order.dto.OrderConsumerlogExportDTO;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.OrderUtils;
import com.avatar.hospital.chaperone.utils.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.avatar.hospital.chaperone.service.order.consts.OrderConst.CONSUMER_LOG_VERSION;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-16 14:22
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderConsumerlogServiceImpl implements OrderConsumerlogService {
    private final OrderConsumerlogRepositoryService orderConsumerlogRepositoryService;
    private final OrderRepositoryService orderRepositoryService;
    private final OrderItemRepositoryService orderItemRepositoryService;
    private final ItemService itemService;
    private final OrderNursingRepositoryService orderNursingRepositoryService;
    private final NursingRepositoryService nursingRepositoryService;
    @Override
    public PageResponse<OrderConsumerLogCResponse> pagingForC(OrderConsumerlogCPageRequest request) {
        OrderDO orderDO = orderRepositoryService.getByIdAndAccountId(request.getOrderId(),request.getOperator());
        AssertUtils.isNotNull(orderDO,ErrorCode.ORDER_NOT_EXIST);

        Page<OrderConsumerlogDO> page = request.ofPage();
        LambdaQueryWrapper<OrderConsumerlogDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderConsumerlogDO::getVersion,CONSUMER_LOG_VERSION);
        queryWrapper.eq(OrderConsumerlogDO::getOrderId,request.getOrderId());
        queryWrapper.ge(Objects.nonNull(request.getStartTime()),OrderConsumerlogDO::getDate,request.getStartTime());
        queryWrapper.le(Objects.nonNull(request.getEndTime()),OrderConsumerlogDO::getDate,request.getEndTime());
        queryWrapper.orderByDesc(OrderConsumerlogDO::getDate,OrderConsumerlogDO::getId);
        page = orderConsumerlogRepositoryService.page(page, queryWrapper);
        return PageResponse.build(page, OrderBuilder::toOrderConsumerLogCResponse);
    }

    @Override
    public PageResponse<OrderConsumerLogResponse> paging(OrderConsumerPageRequest request) {
        if (!CollUtils.isEmpty(request.getDate())) {
            List<String> date = request.getDate();
            request.setStartTime(date.get(0));
            request.setEndTime(date.get(1));
        }
        Page<OrderConsumerlogDO> page = request.ofPage();
        LambdaQueryWrapper<OrderConsumerlogDO> queryWrapper = queryWrapper();
        queryWrapper.eq(Objects.nonNull(request.getOrderId()),OrderConsumerlogDO::getOrderId,request.getOrderId());
        queryWrapper.ge(Objects.nonNull(request.getStartTime()),OrderConsumerlogDO::getDate, DateUtils.dateInt(request.getStartTime()));
        queryWrapper.le(Objects.nonNull(request.getEndTime()),OrderConsumerlogDO::getDate,DateUtils.dateInt(request.getEndTime()));
        if (Objects.equals(request.getVersion(),CONSUMER_LOG_VERSION)) {
            queryWrapper.eq(Objects.nonNull(request.getVersion()),OrderConsumerlogDO::getVersion,CONSUMER_LOG_VERSION);
        } else {
            queryWrapper.ne(Objects.nonNull(request.getVersion()),OrderConsumerlogDO::getVersion,CONSUMER_LOG_VERSION);
        }
        queryWrapper.orderByDesc(OrderConsumerlogDO::getDate)
                .orderByAsc(OrderConsumerlogDO::getVersion)
                .orderByDesc(OrderConsumerlogDO::getOrderId)
                .orderByDesc(OrderConsumerlogDO::getItemId);
        page = orderConsumerlogRepositoryService.page(page, queryWrapper);
        // 填充套餐/订单信息
        List<Long> itemIdList = CollUtils.toListLongDistinct(page.getRecords(), OrderConsumerlogDO::getItemId);
        Map<Long, ItemResponse> itemMap = itemService.listByIds(itemIdList).stream()
                .collect(Collectors.toMap(ItemResponse::getId, Function.identity()));
        List<Long> orderIdList = CollUtils.toListLongDistinct(page.getRecords(), OrderConsumerlogDO::getOrderId);
        Map<Long,OrderDO> orderRef = orderRepositoryService.listRef(orderIdList);

        List<OrderConsumerLogResponse> list = page.getRecords().stream()
                .map(consumerlog -> {
                    OrderConsumerLogResponse data = OrderBuilder.toOrderConsumerLogResponse(consumerlog);
                    ItemResponse item = itemMap.get(data.getItemId());
                    if (Objects.nonNull(item)) {
                        data.setItemName(item.getName());
                    }
                    OrderDO order = orderRef.get(data.getOrderId());
                    if (Objects.nonNull(order)) {
                        data.setPatientName(order.getPatientName());
                    }
                    return data;
                }).collect(Collectors.toList());
        return PageResponse.build(page,list);
    }

    @Override
    public OrderConsumerLogCreateResponse create(OrderConsumeCreateRequest request) {
        OrderDO orderDO = orderRepositoryService.getById(request.getOrderId());
        AssertUtils.isNotNull(orderDO, ErrorCode.ORDER_NOT_EXIST);
        List<OrderNursingDO> nursingDOList = orderNursingRepositoryService.getByOrderId(request.getOrderId());

        List<Long> itemIdList = request.getItemIdList();
        Map<Long,ItemResponse> itemMap = itemService.listByIds(itemIdList).stream()
                .collect(Collectors.toMap(ItemResponse::getId,Function.identity()));
        List<OrderConsumerlogDO> list = OrderBuilder.toOrderConsumerlogDOListCreate(orderDO,nursingDOList,itemIdList,itemMap,request.getDate(),request.getOperator());
        boolean saveFlag = orderConsumerlogRepositoryService.saveBatch(list);
        return OrderConsumerLogCreateResponse.build(saveFlag);
    }

    @Override
    public OrderConsumerLogCreateResponse createByOrderConform(OrderConsumeCreateRequest request) {
        log.info("OrderConsumerlogServiceImpl[]createByOrder start>> request:{}", JSON.toJSONString(request));
        OrderDO orderDO = orderRepositoryService.getById(request.getOrderId());
        AssertUtils.isNotNull(orderDO, ErrorCode.ORDER_NOT_EXIST);
        List<OrderNursingDO> nursingDOList = orderNursingRepositoryService.getByOrderId(request.getOrderId());

        List<OrderItemDO> orderItemDOList = orderItemRepositoryService.getByOrderId(orderDO.getId());
        List<Long> itemIdList = CollUtils.toListLongDistinct(orderItemDOList,OrderItemDO::getItemId);
        // 生成消耗明细
        List<ItemResponse> itemList = itemService.listByIds(itemIdList);
        Map<Long, ItemResponse> itemMap = itemList.stream()
                .collect(Collectors.toMap(ItemResponse::getId, Function.identity()));
        // 套餐 对应消耗日期
        LocalDateTime now = LocalDateTime.now();
        int hour = now.getHour();
        LocalDate today = LocalDate.now();
        Integer dataInt = DateUtils.dateInt(today);
        LocalDate tomorrow = today.plusDays(1);
        Integer tomorrowInt = DateUtils.dateInt(tomorrow);
        Map<Long,Integer> itemDateRef = Maps.newHashMap();
        for (ItemResponse item : itemList) {
            Integer date = dataInt;
            // 收费时间点,<= 当前时间点
            if (item.getChargingTime() <= hour) {
                date = tomorrowInt;
            }
            log.info("itemId:{},c:{},hour:{},date:{}",item.getId(),item.getChargingTime(),hour,date);
            itemDateRef.put(item.getId(),date);
        }
        List<OrderConsumerlogDO> list = OrderBuilder.toOrderConsumerlogDOListCreateDateMap(orderDO,nursingDOList,itemIdList,itemMap,itemDateRef,request.getOperator());
        boolean saveFlag = orderConsumerlogRepositoryService.saveBatch(list);
        return OrderConsumerLogCreateResponse.build(saveFlag);
    }

    @Override
    public OrderConsumerLogCreateResponse resetCalculation(OrderConsumeResetCalculationRequest request) {
        log.info("OrderConsumerlogServiceImpl[]resetCalculation start >> request:{}",JSON.toJSONString(request));
        OrderDO orderDO = orderRepositoryService.getById(request.getOrderId());
        AssertUtils.isNotNull(orderDO, ErrorCode.ORDER_NOT_EXIST);
        List<OrderItemDO> orderItemList = orderItemRepositoryService.getByOrderId(orderDO.getId());
        Map<Long, ItemResponse> itemMap = null;
        if (orderDO.noSettingRate()) {
            // 没有设置过折扣 获取套餐折扣
            List<Long> itemIds = orderItemList.stream()
                    .map(item -> item.getItemId())
                    .collect(Collectors.toList());
            itemMap = itemService.listByIds(itemIds)
                    .stream()
                    .collect(Collectors.toMap(ItemResponse::getId, Function.identity()));
        }
        Long version = OrderUtils.getNewVsersion();
        List<OrderConsumerlogDO> oldList = orderConsumerlogRepositoryService.findAll(request.getOrderId());
        List<OrderConsumerlogDO> newlist = OrderBuilder.toOrderConsumerlogDOListReCreate(oldList,orderDO,itemMap,request.getOperator());
        oldList.forEach(item -> delConsumerLog(item,version));
        boolean saveFlag = orderConsumerlogRepositoryService.savesByResetCalculation(oldList,newlist);
        log.info("OrderConsumerlogServiceImpl[]resetCalculation end");
        return OrderConsumerLogCreateResponse.build(saveFlag);
    }

    @Override
    public OrderConsumeSumPriceResponse sumPrice(OrderConsumeSumPriceRequest request) {
        Integer price = orderConsumerlogRepositoryService.sumPrice(request.getOrderId(),request.getStartDate(),request.getEndDate());
        return OrderConsumeSumPriceResponse.build(price);
    }

    @Override
    public List<OrderConsumerlogExportDTO> exportPaging(OrderConsumerExportPageRequest request) {
        OrderConsumerPageRequest req = OrderBuilder.toOrderConsumerPageRequest(request);
        PageResponse<OrderConsumerLogResponse> paging = paging(req);
        List<OrderConsumerlogExportDTO> list = OrderBuilder.toOrderConsumerlogExportDTOList(paging.getRecords());
        return list;
    }

    @Override
    public List<OrderNursingSimpleResponse> nursingList(Long orderId, Integer startDate, Integer endDate) {
        LambdaQueryWrapper<OrderConsumerlogDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderConsumerlogDO::getOrderId, orderId);
        queryWrapper.ge(OrderConsumerlogDO::getDate, startDate);
        queryWrapper.le(OrderConsumerlogDO::getDate, endDate);
        queryWrapper.le(OrderConsumerlogDO::getVersion, 1L);
        queryWrapper.groupBy(OrderConsumerlogDO::getNursingId);
        List<OrderConsumerlogDO> list = orderConsumerlogRepositoryService.list(queryWrapper);
        if (!CollUtils.isEmpty(list)) {
            List<OrderNursingSimpleResponse> result = list.stream()
                    .flatMap(item -> toOrderNursingSimpleResponse(OrderConsumerlogExtraNursingInfoDTO.build(item.getExtraNursingInfo())))
                    .distinct()
                    .collect(Collectors.toList());
            return result;
        }
        return Lists.newArrayList();
    }

    public Stream<OrderNursingSimpleResponse> toOrderNursingSimpleResponse(OrderConsumerlogExtraNursingInfoDTO dto) {
        if (Objects.isNull(dto) || CollUtils.isEmpty(dto.getList())) {
            return Stream.empty();
        }
        Stream<OrderNursingSimpleResponse> stream = dto.getList()
                .stream()
                .map(item -> OrderNursingSimpleResponse.build(item.getNursingId(), item.getNursingName()));
        return stream;
    }

    /**
     * 删除记录
     * @param orderConsumerlogDO
     * @param version
     * @return
     */
    private OrderConsumerlogDO delConsumerLog(OrderConsumerlogDO orderConsumerlogDO,Long version) {
        orderConsumerlogDO.setVersion(version);
        return orderConsumerlogDO;
    }


    private LambdaQueryWrapper<OrderConsumerlogDO> queryWrapper() {
        LambdaQueryWrapper<OrderConsumerlogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderConsumerlogDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
