package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:10
 **/
@Data
public class CashDepositRequest implements Serializable, OperatorReq {
    private static final long serialVersionUID = 1L;


    /**
     * 存入人ID
     */
    private Long personId;

    /**
     * 存入人姓名
     */
    private String personName;

    /**
     * 存入金额
     */
    @Min(0)
    private Integer price;

    /**
     * 金额类型 1 现金 3 支付宝
     * @see com.avatar.hospital.chaperone.database.order.enums.CashSource
     */
    private Integer cashSource;


    /**
     * 操作用户
     */
    private Operator operatorUser;
}
