package com.avatar.hospital.chaperone.response.statistics;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 10:11
 **/
@Data
public class StatisticsTaskResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 年度巡检任务统计 状态码,数量
     */
    private Map<Integer,Integer> patrolForYear;
    /**
     * 年度维保任务统计 状态码,数量
     */
    private Map<Integer,Integer> maintenanceForYear;
    /**
     * 月度巡检任务统计 状态码,数量
     */
    private Map<Integer,Integer> patrolForMonth;
    /**
     * 月度维保任务统计 状态码,数量
     */
    private Map<Integer,Integer> maintenanceForMonth;


}
