package com.avatar.hospital.chaperone.service.part;

import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.request.part.PartBatchForCPageRequest;
import com.avatar.hospital.chaperone.request.part.PartBatchRequest;
import com.avatar.hospital.chaperone.request.part.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartBatchVO;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 13:12
 */
public interface PartBatchService {


    Long create(PartBatchRequest partBatchRequest);


    Long update(PartBatchRequest partBatchRequest);

    PageResponse<PartBatchVO> paging(QueryRequest queryRequest);

    /**
     * 批次分页查询C端
     * @param request
     * @return
     */
    PageResponse<PartBatchVO> pagingForC(PartBatchForCPageRequest request);

    PartBatchVO detail(Long id);

    Boolean entryStock(LocalDateTime entryTime, Long stockApplyId, PartStockApplyRefPartBatchDO partBatch, Long operator);
}
