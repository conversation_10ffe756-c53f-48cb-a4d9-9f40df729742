package com.avatar.hospital.chaperone.database.part.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.mapper.PartStockApplyRefPartBatchMapper;
import com.avatar.hospital.chaperone.database.part.repository.PartStockApplyRefPartBatchRepositoryService;
import com.avatar.hospital.chaperone.request.part.PartStockApplyRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 备件入库审批单关联备件批次 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class PartStockApplyRefPartBatchRepositoryServiceImpl extends ServiceImpl<PartStockApplyRefPartBatchMapper, PartStockApplyRefPartBatchDO> implements PartStockApplyRefPartBatchRepositoryService {

    @Override
    public boolean update2delete(PartStockApplyRequest request) {

        LambdaUpdateWrapper<PartStockApplyRefPartBatchDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PartStockApplyRefPartBatchDO::getStockApplyId, request.getId());
        updateWrapper.set(PartStockApplyRefPartBatchDO::getUpdateBy, request.getOperator());
        updateWrapper.set(PartStockApplyRefPartBatchDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.setSql(" deleted = id");
        update(updateWrapper);
        return true;
    }

    @Override
    public List<Long> getPartBatchIds(Long stockApplyId) {

        LambdaQueryWrapper<PartStockApplyRefPartBatchDO> queryWrapper = queryWrapper();
        queryWrapper.eq(PartStockApplyRefPartBatchDO::getStockApplyId, stockApplyId);
        queryWrapper.eq(PartStockApplyRefPartBatchDO::getDeleted, DeletedEnum.NO.getStatus());
        List<PartStockApplyRefPartBatchDO> stockApplyRefPartBatchDOS = this.list(queryWrapper);
        return stockApplyRefPartBatchDOS.stream().map(o -> o.getSparePartBatchId()).collect(Collectors.toList());
    }
    @Override
    public List<PartStockApplyRefPartBatchDO> getPartBatchBy(Long stockApplyId) {

        LambdaQueryWrapper<PartStockApplyRefPartBatchDO> queryWrapper = queryWrapper();
        queryWrapper.eq(PartStockApplyRefPartBatchDO::getStockApplyId, stockApplyId);
        queryWrapper.eq(PartStockApplyRefPartBatchDO::getDeleted, DeletedEnum.NO.getStatus());
        List<PartStockApplyRefPartBatchDO> stockApplyRefPartBatchDOS = this.list(queryWrapper);
        return stockApplyRefPartBatchDOS;
    }
    private LambdaQueryWrapper<PartStockApplyRefPartBatchDO> queryWrapper() {
        LambdaQueryWrapper<PartStockApplyRefPartBatchDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PartStockApplyRefPartBatchDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}
