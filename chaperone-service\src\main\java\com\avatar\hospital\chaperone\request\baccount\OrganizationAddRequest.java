package com.avatar.hospital.chaperone.request.baccount;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class OrganizationAddRequest implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * 上级组织机构ID 顶级节点默认为null
     */
    private Long parentId;

    /**
     * 联络人名称
     */
    private String liaisonName;

    /**
     * 联络人电话
     */
    private String liaisonPhoneNumber;

    /**
     * 银行账号
     */
    private String bankAccountNumber;

    /**
     * 组织机构类型
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationType
     */
    private Integer type;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationStatus
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 更新者ID
     */
    private Long updateBy;

}
