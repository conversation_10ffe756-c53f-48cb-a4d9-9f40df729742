package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderConsumerlogDO;
import com.avatar.hospital.chaperone.database.order.mapper.OrderConsumerlogMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderConsumerlogRepositoryService;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.avatar.hospital.chaperone.service.order.consts.OrderConst.CONSUMER_LOG_VERSION;

/**
 * <p>
 * 订单-消耗明细表(天，套餐); 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class OrderConsumerlogRepositoryServiceImpl extends ServiceImpl<OrderConsumerlogMapper, OrderConsumerlogDO> implements OrderConsumerlogRepositoryService {

    private final String STATISTICS_SQL = "sum( `total_price`) as id";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean savesByResetCalculation(List<OrderConsumerlogDO> oldList, List<OrderConsumerlogDO> newlist) {
        updateBatchById(oldList);
        saveBatch(newlist);
        return Boolean.TRUE;
    }

    @Override
    @NotNull
    public List<OrderConsumerlogDO> findAll(Long orderId) {
        LambdaQueryWrapper<OrderConsumerlogDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderConsumerlogDO::getOrderId,orderId);
        queryWrapper.eq(OrderConsumerlogDO::getVersion,CONSUMER_LOG_VERSION);
        List<OrderConsumerlogDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list;
    }

    @Override
    @NotNull
    public List<OrderConsumerlogDO> findAll(Long orderId,Integer startDate,Integer endDate) {
        LambdaQueryWrapper<OrderConsumerlogDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderConsumerlogDO::getOrderId,orderId);
        queryWrapper.ge(Objects.nonNull(startDate),OrderConsumerlogDO::getDate,startDate);
        queryWrapper.le(Objects.nonNull(endDate),OrderConsumerlogDO::getDate,endDate);
        queryWrapper.eq(OrderConsumerlogDO::getVersion,CONSUMER_LOG_VERSION);
        List<OrderConsumerlogDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list;
    }

    @Override
    @NotNull
    public Integer sumPrice(Long orderId,Integer startDate,Integer endDate) {
        Integer price = baseMapper.sumPrice(orderId,startDate,endDate);
        return Objects.isNull(price) ? 0 : price;
    }

    @Override
    public Long statistics(LocalDateTime star, LocalDateTime end) {
        QueryWrapper<OrderConsumerlogDO> queryWrapper = new QueryWrapper();
        queryWrapper.select(STATISTICS_SQL)
                .lambda()
                .eq(OrderConsumerlogDO::getDeleted, DelUtils.NO_DELETED)
                .eq(OrderConsumerlogDO::getVersion,CONSUMER_LOG_VERSION)
                .ge(OrderConsumerlogDO::getDate, DateUtils.dateInt(star))
                .lt(OrderConsumerlogDO::getDate,DateUtils.dateInt(end));
        OrderConsumerlogDO result = getOne(queryWrapper);
        return Objects.isNull(result) || Objects.isNull(result.getId()) ?
                0L : result.getId();
    }

    private LambdaQueryWrapper<OrderConsumerlogDO> queryWrapper() {
        LambdaQueryWrapper<OrderConsumerlogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderConsumerlogDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }

}
