package com.avatar.hospital.chaperone.builder.emergency;

import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.database.emergencylog.dataobject.EmergencyHandlingLogDO;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogAddRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogUpdateRequest;
import com.avatar.hospital.chaperone.response.baccount.RolePagingResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogPagingResponse;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * @author:sp0420
 * @Description:
 */
public class EmergencyLogBuilder {
    public static EmergencyHandlingLogDO buildEmergencyLogDO(EmergencyLogAddRequest request) {
        if (request == null) {
            return null;
        }
        EmergencyHandlingLogDO date = new EmergencyHandlingLogDO();
        date.setTitle(request.getTitle());
        date.setEventTime(request.getEventTime());
        date.setOrgId(request.getOrgId());
        date.setRemark(request.getRemark());
        date.setCreateBy(request.getCreateBy());
        date.setUpdateBy(request.getCreateBy());
        date.setCreatedAt(LocalDateTime.now());
        date.setUpdatedAt(LocalDateTime.now());
        date.setDeleted(DelUtils.NO_DELETED);
        return date;
    }

    public static EmergencyHandlingLogDO buildEmergencyLogDO(EmergencyLogUpdateRequest request) {
        if (request == null) {
            return null;
        }

        EmergencyHandlingLogDO date = new EmergencyHandlingLogDO();
        date.setId(request.getId());
        date.setTitle(request.getTitle());
        date.setEventTime(request.getEventTime());
        date.setOrgId(request.getOrgId());
        date.setRemark(request.getRemark());
        date.setUpdateBy(request.getUpdateBy());
        date.setUpdatedAt(LocalDateTime.now());
        return date;
    }

    public static PageResponse<EmergencyLogPagingResponse> buildEmergencyLogDO(PageResponse<EmergencyHandlingLogDO> pageResponse) {
        if (pageResponse == null) {
            return null;
        }

        PageResponse<EmergencyLogPagingResponse> response = new PageResponse<>();
        response.setTotal(pageResponse.getTotal());
        response.setCurrent(pageResponse.getCurrent());
        response.setSize(pageResponse.getSize());
        response.setRecords(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(pageResponse.getRecords())) {
            response.setRecords(buildEmergencyLogPagingResponse(pageResponse.getRecords()));
        }
        return response;
    }

    private static List<EmergencyLogPagingResponse> buildEmergencyLogPagingResponse(List<EmergencyHandlingLogDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        List<EmergencyLogPagingResponse> list = Lists.newLinkedList();
        for (EmergencyHandlingLogDO logDO : records) {
            list.add(buildEmergencyLogPagingResponse(logDO));
        }
        return list;
    }

    public static EmergencyLogPagingResponse buildEmergencyLogPagingResponse(EmergencyHandlingLogDO logDO) {
        if (logDO == null) {
            return null;
        }
        EmergencyLogPagingResponse logPagingResponse = new EmergencyLogPagingResponse();
        logPagingResponse.setId(logDO.getId());
        logPagingResponse.setTitle(logDO.getTitle());
        logPagingResponse.setEventTime(DateUtils.dateTimeStr(logDO.getEventTime()));
        logPagingResponse.setRemark(logDO.getRemark());
        logPagingResponse.setOrgId(logDO.getOrgId());
        return logPagingResponse;
    }
}
