package com.avatar.hospital.chaperone.response;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class PageResponse<T> implements Serializable {

    private static final long serialVersionUID = 1912215713237065148L;

    private List<T> records;

    /**
     * 总条数
     */
    private long total;

    /**
     * 当前页码
     */
    private long current;

    /**
     * 页长
     */
    private long size;

    public static <T> PageResponse<T> empty(Long current, Long size) {
        PageResponse<T> pageResponse = new PageResponse<>();
        pageResponse.total = 0L;
        pageResponse.current = current;
        pageResponse.size = size;
        pageResponse.records = Collections.emptyList();
        return pageResponse;
    }

    public static <T> PageResponse<T> build(Long total, Long current, Long size, List<T> records) {
        PageResponse<T> pageResponse = new PageResponse<>();
        pageResponse.total = total;
        pageResponse.current = current;
        pageResponse.size = size;
        pageResponse.records = records;
        return pageResponse;
    }

    public static <T,D> PageResponse<T> build(Page<D> page, Function<D,T> function) {
        PageResponse<T> pageResponse = new PageResponse<>();
        pageResponse.total = page.getTotal();
        pageResponse.current = page.getCurrent();
        pageResponse.size = page.getSize();
        pageResponse.records = page.getRecords().stream()
                                 .map(item -> function.apply(item))
                                .collect(Collectors.toList());
        return pageResponse;
    }

    public static <T,D> PageResponse<T> build(Page<D> page, List<T> list) {
        PageResponse<T> pageResponse = new PageResponse<>();
        pageResponse.total = page.getTotal();
        pageResponse.current = page.getCurrent();
        pageResponse.size = page.getSize();
        pageResponse.records = list;
        return pageResponse;
    }

    public static <T,D> PageResponse<T> build(Long total, Long current, Long size, List<D> list,Function<D,T> function) {
        PageResponse<T> pageResponse = new PageResponse<>();
        pageResponse.total = total;
        pageResponse.current = current;
        pageResponse.size = size;
        pageResponse.records = list.stream()
                .map(item -> function.apply(item))
                .collect(Collectors.toList());
        return pageResponse;
    }
}
