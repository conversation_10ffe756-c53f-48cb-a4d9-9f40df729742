package com.avatar.hospital.chaperone.consumer.validator.caccount;

import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountLoginRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
public class LoginValidator {

    public static void loginValidate(ConsumerAccountLoginRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getCode(), ErrorCode.PARAMETER_ERROR);
    }

}
