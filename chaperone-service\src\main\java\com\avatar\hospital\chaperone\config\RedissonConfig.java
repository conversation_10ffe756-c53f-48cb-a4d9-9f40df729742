package com.avatar.hospital.chaperone.config;

import com.avatar.hospital.chaperone.template.util.StrUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-20 16:41
 **/

@Configuration
public class RedissonConfig {

    @Value("${redisson.host}")
    private String redisHost;
    @Value("${redisson.port}")
    private String redisPort;
    @Value("${redisson.database}")
    private Integer redisDatabase;
    @Value("${redisson.username}")
    private String username;
    @Value("${redisson.password}")
    private String password;

    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient(){
        Config config = new Config();
        if (StrUtils.hasText(username)) {
            config.useSingleServer()
                    .setAddress("redis://" + redisHost + ":" + redisPort)
                    .setUsername(username)
                    .setPassword(password)
                    .setDatabase(redisDatabase);
        } else {
            config.useSingleServer()
                    .setAddress("redis://" + redisHost + ":" + redisPort)
                    .setDatabase(redisDatabase);
        }

        RedissonClient redissonClient = Redisson.create(config);
        return redissonClient;
    }
}
