package com.avatar.hospital.chaperone.response.order;

import com.avatar.hospital.chaperone.database.order.enums.OrderEstimateSource;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:06
 **/
@Data
public class OrderEstimateResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 星级
     */
    private Integer level;

    /**
     * 描述
     */
    private String remark;

    /**
     * 来源,参考,order_estimate_source
     * @see OrderEstimateSource
     */
    private Integer source;
}
