package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.database.order.enums.OrderBillSubType;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillType;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 16:47
 **/
@Data
public class OrderBillModifyTradeRequest implements OperatorReq,Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @NotNull
    private Long orderId;

    /**
     * 账单类型:参考，bill_type 1 周期订单 2 预付单
     * @see OrderBillType
     */
    private Integer billType;

    /**
     * 账单子类型:参考，bill_sub_type 11 月度 12 季度 13 年度
     * @see OrderBillSubType
     */
    private Integer billSubType;

    /**
     * 预付单金额 单位分
     */
    private Integer price;

    /**
     * 操作用户
     */
    private Operator operatorUser;

}
