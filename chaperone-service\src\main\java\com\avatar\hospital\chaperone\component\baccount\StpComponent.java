package com.avatar.hospital.chaperone.component.baccount;

import cn.dev33.satoken.session.SaSessionCustomUtil;
import cn.dev33.satoken.stp.StpUtil;
import com.avatar.hospital.chaperone.constant.StpConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class StpComponent {

    /**
     * 删除角色/给角色分配权限时 删除角色对应权限缓存
     *
     * @param roleKey -
     */
    public void deleteRolePermissionCache(String roleKey) {
        if (StringUtils.isBlank(roleKey)) {
            return;
        }
        SaSessionCustomUtil.deleteSessionById(StpConstant.ROLE_PERMISSION_KEY_PREFIX + roleKey);
    }

    /**
     * 删除角色/给角色分配权限时 删除角色对应权限缓存
     *
     * @param roleKeys -
     */
    public void deleteRolePermissionCache(Set<String> roleKeys) {
        if (CollectionUtils.isEmpty(roleKeys)) {
            return;
        }
        roleKeys.forEach(roleKey -> {
            if (StringUtils.isBlank(roleKey)) {
                return;
            }
            SaSessionCustomUtil.deleteSessionById(StpConstant.ROLE_PERMISSION_KEY_PREFIX + roleKey);
        });
    }

    /**
     * 删除用户/给用户分配角色时 删除用户缓存数据 使用户重新登录
     *
     * @param accountId -
     */
    public void deleteUserRoleCache(Long accountId) {
        if (accountId == null) {
            return;
        }
        StpUtil.logout(accountId);
    }

    /**
     * 删除用户/给用户分配角色时 删除用户缓存数据 使用户重新登录
     *
     * @param accountIds -
     */
    public void deleteUserRoleCache(Set<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }
        accountIds.forEach(accountId -> {
            if (accountId == null) {
                return;
            }
            StpUtil.logout(accountId);
        });
    }
}
