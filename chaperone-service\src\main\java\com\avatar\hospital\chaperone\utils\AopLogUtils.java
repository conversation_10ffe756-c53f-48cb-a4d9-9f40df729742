package com.avatar.hospital.chaperone.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-30 15:04
 **/
@Slf4j
public class AopLogUtils {
    public static Object proceed(String topic,ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String contentType = request.getHeader("Content-Type");
        log.info("{} Entry 入口URL :{} {}",topic, request.getMethod().toUpperCase(), request.getRequestURL().toString());
        try {
            Object[] args = proceedingJoinPoint.getArgs();
            if (Objects.nonNull(contentType) && !contentType.contains("multipart/form-data;")
                    && Objects.nonNull(args) && args.length > 0) {
                for (Object arg : args) {
                    if ((arg instanceof HttpServletResponse) || (arg instanceof HttpServletRequest)) {
                        continue;
                    }
                    log.info("{} Entry 请求参数 :{}",topic,JSONObject.toJSON(arg));
                }

            }
        }catch (Exception e) {
            log.error("AopLogUtils proceed error",e);
        }

        Object result = proceedingJoinPoint.proceed();
        log.info("{} Out   响应 cost:{} ms {}",topic, System.currentTimeMillis() - startTime, JSONObject.toJSON(result));
        return result;
    }
}
