package com.avatar.hospital.chaperone.database.part.repository.impl;

import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyDO;
import com.avatar.hospital.chaperone.database.part.mapper.PartStockApplyMapper;
import com.avatar.hospital.chaperone.database.part.repository.PartStockApplyRepositoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 备品备件入库审批单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class PartStockApplyRepositoryServiceImpl extends ServiceImpl<PartStockApplyMapper, PartStockApplyDO> implements PartStockApplyRepositoryService {

}
