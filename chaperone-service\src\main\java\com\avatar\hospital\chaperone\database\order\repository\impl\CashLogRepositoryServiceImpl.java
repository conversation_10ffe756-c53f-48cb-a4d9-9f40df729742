package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.CashLogDO;
import com.avatar.hospital.chaperone.database.order.mapper.CashLogMapper;
import com.avatar.hospital.chaperone.database.order.repository.CashLogRepositoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <p>
 * 现金记录表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Service
public class CashLogRepositoryServiceImpl extends ServiceImpl<CashLogMapper, CashLogDO> implements CashLogRepositoryService {

    @Override
    @NotNull
    public Integer sumByType(Integer type) {
        Integer price = baseMapper.sumByType(type);
        return Objects.isNull(price) ? 0 : price;
    }

    @Override
    @NotNull
    public Integer sumByType(Integer type, Integer source) {
        Integer price = baseMapper.sumByTypeAndSource(type,source);
        return Objects.isNull(price) ? 0 : price;
    }
}
