package com.avatar.hospital.chaperone.request.item;

import com.avatar.hospital.chaperone.database.item.enums.ItemDisplay;
import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 15:45
 **/
@Data
public class ItemPageRequest extends PageRequest implements OperatorReq {

    /**
     * 套餐ID
     */
    private Long id;

    /**
     * 套餐名称
     */
    private String name;


    /**
     * 是否外显示 1 是 0 不是
     * @see ItemDisplay
     */
    private Integer display;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
