package com.avatar.hospital.chaperone.database.nursing.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDayDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingOrderDO;
import com.avatar.hospital.chaperone.database.nursing.mapper.NursingDayMapper;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingDayRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingOrderRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.nursing.NursingDayPagingRequest;
import com.avatar.hospital.chaperone.response.nursing.NursingDayOrderResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import java.util.List;

/**
 * <p>
 * 护工-考勤信息; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
@RequiredArgsConstructor
public class NursingDayRepositoryServiceImpl extends ServiceImpl<NursingDayMapper, NursingDayDO> implements NursingDayRepositoryService {

    private final NursingOrderRepositoryService nursingOrderRepositoryService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveByAdd(List<NursingDayDO> nursingDayDOlist, List<NursingOrderDO> nursingOrderDOlist) {
        saveOrUpdateBatch(nursingDayDOlist);
        // 更新数据
        List<NursingOrderDO> updateList = nursingOrderDOlist.stream()
                .filter(item -> Objects.equals(DelUtils.NO_DELETED, item.getDeleted()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateList)) {
            nursingOrderRepositoryService.saveOrUpdateBatch(updateList);
        }
        // 删除数据
        List<NursingOrderDO> delList = nursingOrderDOlist.stream()
                .filter(item -> !Objects.equals(DelUtils.NO_DELETED, item.getDeleted()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(delList)) {
            nursingOrderRepositoryService.removeBatchByIds(delList);
        }

    }

    @Override
    public Map<Integer, NursingDayDO> findAllByNursingId(Long nursingId, Integer startDate, Integer endDate) {
        LambdaQueryWrapper<NursingDayDO> queryWrapper = queryWrapper();
        queryWrapper.eq(NursingDayDO::getNursingId, nursingId);
        queryWrapper.ge(NursingDayDO::getDate, startDate);
        queryWrapper.le(NursingDayDO::getDate, endDate);
        List<NursingDayDO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream()
                .collect(Collectors.toMap(NursingDayDO::getDate, Function.identity()));
    }

    @Override
    public Boolean incrementByNursingUpdate(NursingDayDO nursingDayDO) {
        if (nursingDayDO == null || nursingDayDO.getNursingId() == null) {
            return null;
        }
        LambdaUpdateWrapper<NursingDayDO> updateWrapper = updateWrapper();
        updateWrapper.set(nursingDayDO.getNursingName() != null, NursingDayDO::getNursingName, nursingDayDO.getNursingName());
        updateWrapper.set(nursingDayDO.getStatus() != null, NursingDayDO::getStatus, nursingDayDO.getStatus());

        updateWrapper.eq(NursingDayDO::getNursingId, nursingDayDO.getNursingId());
        return update(updateWrapper);
    }

    @Override
    public Long countByB(NursingDayPagingRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }
        Map<String, Object> criteria = new HashMap<>();
        if (Objects.nonNull(request.getOrderId())) {
            criteria.put("orderId", request.getOrderId());
        }
        if (Objects.nonNull(request.getStatus())) {
            criteria.put("status", request.getStatus());
        }
        if (Objects.nonNull(request.getStartTime())) {
            criteria.put("startTime", request.getStartTime());
        }
        if (Objects.nonNull(request.getEndTime())) {
            criteria.put("endTime", request.getEndTime());
        }
        if (Objects.nonNull(request.getNursingId())){
            criteria.put("nursingId",request.getNursingId());
        }
        return baseMapper.countByB(criteria);
    }

    @Override
    public List<NursingDayPagingResponse> listByB(NursingDayPagingRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }
        Map<String, Object> criteria = new HashMap<>();
        if (Objects.nonNull(request.getOrderId())) {
            criteria.put("orderId", request.getOrderId());
        }
        if (Objects.nonNull(request.getStatus())) {
            criteria.put("status", request.getStatus());
        }
        if (Objects.nonNull(request.getStartTime())) {
            criteria.put("startTime", request.getStartTime());
        }
        if (Objects.nonNull(request.getEndTime())) {
            criteria.put("endTime", request.getEndTime());
        }
        if (Objects.nonNull(request.getPageIndex())) {
            criteria.put("offset", request.getPageIndex());
        }
        if (Objects.nonNull(request.getPageSize())) {
            criteria.put("limit", request.getPageSize());
        }
        if (Objects.nonNull(request.getSortType())) {
            criteria.put("sortType", request.getSortType());
        }
        if (Objects.nonNull(request.getNursingId())){
            criteria.put("nursingId",request.getNursingId());
        }
        List<NursingDayPagingResponse> responseList = baseMapper.listByB(criteria);
        if (CollectionUtils.isEmpty(responseList)) {
            return null;
        }

        List<Long> collect = responseList.stream().map(NursingDayPagingResponse::getNursingId).distinct().collect(Collectors.toList());
        List<NursingDayOrderResponse> nursingDayOrderResponses = baseMapper.listByNursingOrderByB(collect);
        if (!CollectionUtils.isEmpty(nursingDayOrderResponses)) {
            for (NursingDayPagingResponse a : responseList) {
                List<NursingDayOrderResponse> orderList = new ArrayList<>();
                for (NursingDayOrderResponse b : nursingDayOrderResponses) {
                    NursingDayOrderResponse nursingDayOrderResponse = new NursingDayOrderResponse();
                    if (Objects.equals(a.getNursingId(), b.getNursingId()) && Objects.equals(a.getDate().intValue(), b.getOrderDate())) {
                        nursingDayOrderResponse.setId(b.getId());
                        nursingDayOrderResponse.setNursingId(b.getNursingId());
                        nursingDayOrderResponse.setOrderDate(b.getOrderDate());
                        nursingDayOrderResponse.setOrderName(b.getOrderName());
                        nursingDayOrderResponse.setOrderId(b.getOrderId());
                        orderList.add(nursingDayOrderResponse);
                    }
                }
                a.setOrderList(orderList);
                a.setStatus();
            }
        }
        return responseList;
    }

    @Override
    public NursingDayDO findByNursingIdAndDate(Long nursingId, Integer date) {
        if (nursingId == null || nursingId < 0 || date == null || date < 0) {
            return null;
        }
        LambdaQueryWrapper<NursingDayDO> queryWrapper = queryWrapper();
        queryWrapper.eq(NursingDayDO::getNursingId, nursingId)
                .eq(NursingDayDO::getDate, date);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public NursingDayDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<NursingDayDO> queryWrapper = queryWrapper();
        queryWrapper.eq(NursingDayDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public Long add(NursingDayDO nursingDayDO) {
        if (nursingDayDO == null) {
            return null;
        }
        nursingDayDO.setId(IdUtils.getId());
        if (!save(nursingDayDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        return nursingDayDO.getId();
    }

    @Override
    public Boolean incrementUpdate(NursingDayDO nursingDayDO) {
        if (nursingDayDO == null) {
            return null;
        }
        LambdaUpdateWrapper<NursingDayDO> updateWrapper = updateWrapper();
        updateWrapper.set(nursingDayDO.getDate() != null, NursingDayDO::getDate, nursingDayDO.getDate());
        updateWrapper.set(nursingDayDO.getNursingId() != null, NursingDayDO::getNursingId, nursingDayDO.getNursingId());
        updateWrapper.set(nursingDayDO.getNursingName() != null, NursingDayDO::getNursingName, nursingDayDO.getNursingName());
        updateWrapper.set(nursingDayDO.getStatus() != null, NursingDayDO::getStatus, nursingDayDO.getStatus());
        updateWrapper.set(nursingDayDO.getUpdateBy() != null, NursingDayDO::getUpdateBy, nursingDayDO.getUpdateBy());
        updateWrapper.set(nursingDayDO.getUpdatedAt() != null, NursingDayDO::getUpdatedAt, nursingDayDO.getUpdatedAt());

        updateWrapper.eq(NursingDayDO::getId, nursingDayDO.getId());
        return update(updateWrapper);
    }

    private LambdaQueryWrapper<NursingDayDO> queryWrapper() {
        LambdaQueryWrapper<NursingDayDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NursingDayDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<NursingDayDO> updateWrapper() {
        LambdaUpdateWrapper<NursingDayDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(NursingDayDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
