<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.device.mapper.ProjectDeviceMapper">

    <select id="fault10" resultType="com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO">
        SELECT dev.id,
               dev.`name`,
                dev.`code`,
               count(rep.id) as status
        FROM `t_project_device` dev
        LEFT JOIN(
             SELECT t.`id`, t.device_id
             from `t_project_repair_form` t
             WHERE t.`deleted`= 0
             and `created_at` &gt;= #{start}
             and `created_at` &lt;= #{end}
        ) as rep on dev.`id`= rep.`device_id`
        WHERE dev.`deleted`= 0
        GROUP BY dev.`id`,
                dev.`name`
        <if test="model == 0">
            ORDER BY status asc
        </if>
        <if test="model == 1">
            ORDER BY status desc
        </if>
        LIMIT 10;
    </select>
</mapper>