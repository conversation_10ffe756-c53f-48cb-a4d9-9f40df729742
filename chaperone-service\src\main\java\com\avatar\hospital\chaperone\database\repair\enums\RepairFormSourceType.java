package com.avatar.hospital.chaperone.database.repair.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 13:23
 **/
@Getter
public enum RepairFormSourceType implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    PC(1, "PC"),
    H5(2, "H5"),
    ;

    private final Integer status;

    private final String describe;


    RepairFormSourceType(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static RepairFormSourceType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (RepairFormSourceType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        RepairFormSourceType e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    public static  final Map<Integer, String> toMap() {
        RepairFormSourceType[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (RepairFormSourceType value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

}
