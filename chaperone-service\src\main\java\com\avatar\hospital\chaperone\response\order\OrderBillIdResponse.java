package com.avatar.hospital.chaperone.response.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillIdResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long billId;

    public static final OrderBillIdResponse build(Long billId) {
        OrderBillIdResponse obj = new OrderBillIdResponse();
        obj.setBillId(billId);
        return obj;
    }

}
