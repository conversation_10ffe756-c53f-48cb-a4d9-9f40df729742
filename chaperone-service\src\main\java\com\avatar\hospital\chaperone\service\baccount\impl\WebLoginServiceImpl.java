package com.avatar.hospital.chaperone.service.baccount.impl;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.baccount.repository.WebAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.PhoneNumberPasswordWebLoginRequest;
import com.avatar.hospital.chaperone.response.baccount.PhoneNumberPasswordWebLoginResponse;
import com.avatar.hospital.chaperone.service.baccount.WebLoginService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.PasswordUtils;
import com.avatar.hospital.chaperone.utils.RequestUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WebLoginServiceImpl implements WebLoginService {

    private final WebAccountRepositoryService webAccountRepositoryService;

    @Override
    public PhoneNumberPasswordWebLoginResponse login(PhoneNumberPasswordWebLoginRequest request) {
        AccountDO accountDO = webAccountRepositoryService.findByPhoneNumber(request.getPhoneNumber());
        AssertUtils.isNotNull(accountDO, ErrorCode.WEB_ACCOUNT_USERNAME_OR_PASSWORD_ERROR);
        AssertUtils.isTrue(PasswordUtils.compare(request.getPassword(), accountDO.getPassword()), ErrorCode.WEB_ACCOUNT_USERNAME_OR_PASSWORD_ERROR);

        AssertUtils.isTrue(accountDO.enable(), ErrorCode.WEB_ACCOUNT_STATUS_NOT_ENABLED);

        StpUtil.login(accountDO.getId());
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();

        // 更新用户最后登录信息
        Boolean result = webAccountRepositoryService.updateLoginInformation(accountDO.getId(), RequestUtils.getRequestHost(), LocalDateTime.now());
        if (!Boolean.TRUE.equals(result)) {
            log.warn("WebLoginServiceImpl login updateLoginInformation fail accountId:{}", accountDO.getId());
        }

        return PhoneNumberPasswordWebLoginResponse.builder().tokenName(tokenInfo.getTokenName()).tokenValue(tokenInfo.getTokenValue()).build();
    }

}
