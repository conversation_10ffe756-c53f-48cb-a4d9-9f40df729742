package com.avatar.hospital.chaperone.template.util;


import com.alibaba.cola.dto.SingleResponse;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public class ResponseUtils {

    public static boolean isFail(SingleResponse singleResponse) {
        if (null == singleResponse || !singleResponse.isSuccess()) {
            return true;
        }
        Object data = singleResponse.getData();
        if (null == data) {
            return true;
        }
        return false;
    }
}
