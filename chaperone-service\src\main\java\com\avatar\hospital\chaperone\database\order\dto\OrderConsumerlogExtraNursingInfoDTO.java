package com.avatar.hospital.chaperone.database.order.dto;

import com.alibaba.fastjson.JSONArray;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderNursingDO;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.avatar.hospital.chaperone.utils.OrderUtils;
import com.avatar.hospital.chaperone.utils.PriceUtil;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * 护工每日分层信息
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-12-20 16:35
 **/
@Data
public class OrderConsumerlogExtraNursingInfoDTO implements Serializable {
    private final static long serialVersionUID = 1L;

    private OrderConsumerlogExtraNursingInfoDTO() {}

    private List<DAT> list;

    public void addDat(DAT dat) {
        list = CollectionUtils.isEmpty(list) ? new ArrayList<>() : list;
        list.add(dat);
    }

    public static final OrderConsumerlogExtraNursingInfoDTO build() {
        OrderConsumerlogExtraNursingInfoDTO dto = new OrderConsumerlogExtraNursingInfoDTO();
        return dto;
    }

    public static final OrderConsumerlogExtraNursingInfoDTO buildByList(List<DAT> list) {
        OrderConsumerlogExtraNursingInfoDTO dto = new OrderConsumerlogExtraNursingInfoDTO();
        dto.setList(list);
        return dto;
    }

    public static final OrderConsumerlogExtraNursingInfoDTO build(String arrayJson) {
        OrderConsumerlogExtraNursingInfoDTO dto = new OrderConsumerlogExtraNursingInfoDTO();
        if (!StrUtils.hasText(arrayJson)) {
            return dto;
        }
        List<DAT> listTmp = JSONArray.parseArray(arrayJson, DAT.class);
        dto.setList(listTmp);
        return dto;
    }

    public static final OrderConsumerlogExtraNursingInfoDTO build(Long nursingId,String nursingName,Integer priceNursing, Integer rateNursing) {
        OrderConsumerlogExtraNursingInfoDTO dto = new OrderConsumerlogExtraNursingInfoDTO();
        DAT dat = new DAT();
        dat.setNursingId(nursingId);
        dat.setNursingName(nursingName);
        dat.setRateNursing(rateNursing);
        dat.setPriceNursing(priceNursing);
        dto.addDat(dat);
        return dto;
    }

    public static final OrderConsumerlogExtraNursingInfoDTO build(List<OrderUtils.CalculationNursingRateResult> list) {
        List<DAT> datList = list.stream()
                .map(item -> OrderConsumerlogExtraNursingInfoDTO.toDAT(item))
                .collect(Collectors.toList());

        OrderConsumerlogExtraNursingInfoDTO dto = new OrderConsumerlogExtraNursingInfoDTO();
        dto.setList(datList);
        return dto;
    }

    public static final DAT toDAT(OrderUtils.CalculationNursingRateResult result) {
        DAT dto = new DAT();
        dto.setNursingId(result.getNursingId());
        dto.setNursingName(result.getNursingName());
        dto.setRateNursing(result.getRateNursing());
        dto.setPriceNursing(result.getPriceNursing());
        return dto;
    }

    public String toArrayJson() {
        return JSONArray.toJSONString(list);
    }

    public List<OrderNursingDO> toOrderNursingDOList() {
        return list.stream()
               .map(item -> {
                    OrderNursingDO nursingDO = new OrderNursingDO();
                    nursingDO.setNursingId(item.getNursingId());
                    nursingDO.setNursingName(item.getNursingName());
                    return nursingDO;
                })
                .collect(Collectors.toList());
    }

    public String exportInfo() {
        if (CollectionUtils.isEmpty(list)) {
            return StrUtils.EMPTY;
        }
        String data = list.stream()
                .map(dat -> {
                    StringBuilder sb = new StringBuilder();
                    sb.append(dat.getNursingName())
                            .append("(")
                            .append(PriceUtil.fenToYuan(dat.getPriceNursing()))
                            .append("元)");
                    return sb.toString();
                }).reduce((result, item) -> result + ";\n" + item).get();
        return data;
    }

    // inner

    @Data
    public static class DAT implements Serializable {
        /**
         * 护工ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long nursingId;

        /**
         * 护工名称
         */
        private String nursingName;

        /**
         * 护工分成比例
         */
        private Integer rateNursing;

        /**
         * 护工金额
         */
        private Integer priceNursing;

    }
}
