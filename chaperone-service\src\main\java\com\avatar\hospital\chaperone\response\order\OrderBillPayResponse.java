package com.avatar.hospital.chaperone.response.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillPayResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 交易订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 账单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long billId;

    /**
     * 三方订单号(微信)
     */
    private String outTradeNo;

    /**
     * 交易类型，参考：trade_type
     * @see com.avatar.hospital.chaperone.database.order.enums.OrderTradeType
     */
    private Integer tradeType;

    /**
     * 金额，单位分
     */
    private Integer price;

    /**
     * 付款时间
     */
    private String payTime;

    /**
     * 订单超时时间
     */
    private String payOutTime;

    /**
     * 退款金额，单位分
     */
    private Integer refundAmount;

    /**
     * 支付备注
     */
    private String remark;

    /**
     * 交易状态，参考：pay_status
     * @see com.avatar.hospital.chaperone.database.order.enums.OrderPayStatus
     */
    private Integer payStatus;

    /**
     * 支付信息
     */
    private OrderBillPayInfoResponse payInfo;

    public static final OrderBillPayResponse build(Long billId) {
        OrderBillPayResponse obj = new OrderBillPayResponse();
        obj.setBillId(billId);
        return obj;
    }

}
