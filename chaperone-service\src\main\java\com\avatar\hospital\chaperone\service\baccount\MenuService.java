package com.avatar.hospital.chaperone.service.baccount;

import com.avatar.hospital.chaperone.request.baccount.MenuAccountPermissionRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuAddRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuTreeRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuAccountPermissionResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuAddResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuPagingResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuTreeResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuUpdateResponse;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
public interface MenuService {

    /**
     * 添加菜单权限
     *
     * @param request -
     * @return -
     */
    MenuAddResponse add(MenuAddRequest request);

    /**
     * 更新菜单权限
     *
     * @param request -
     * @return -
     */
    MenuUpdateResponse update(MenuUpdateRequest request);

    /**
     * 删除菜单权限
     *
     * @param request -
     * @return -
     */
    MenuDeleteResponse delete(MenuDeleteRequest request);

    /**
     * 菜单权限列表
     *
     * @param request -
     * @return -
     */
    PageResponse<MenuPagingResponse> paging(MenuPagingRequest request);

    /**
     * 菜单权限详情
     *
     * @param menuId -
     * @return -
     */
    MenuDetailResponse detail(Long menuId);

    /**
     * 查询菜单权限树
     *
     * @param request -
     * @return -
     */
    List<MenuTreeResponse> tree(MenuTreeRequest request);

    /**
     * 返回当前登录用户的左菜单栏
     *
     * @param request -
     * @return -
     */
    List<MenuTreeResponse> accountMenuTree(MenuTreeRequest request);

    /**
     * 返回用户的权限字符串
     *
     * @param request -
     * @return -
     */
    MenuAccountPermissionResponse accountPermission(MenuAccountPermissionRequest request);
}
