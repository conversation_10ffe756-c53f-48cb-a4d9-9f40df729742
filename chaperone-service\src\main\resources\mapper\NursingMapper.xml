<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.nursing.mapper.NursingMapper">
    <resultMap id="nursing" type="com.avatar.hospital.chaperone.response.nursing.NursingPagingResponse">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="gender" property="gender"/>
        <result column="age" property="age"/>
        <result column="work_year" property="workYear"/>
        <result column="specialty" property="specialty"/>
        <result column="remark" property="remark"/>
        <result column="settle_way" property="settleWay"/>
        <result column="settle_time" property="settleTime"/>
        <result column="sponsor" property="sponsor"/>
        <result column="sponsor_price" property="sponsorPrice"/>
        <result column="certification_pic" property="certificationPic"/>
        <result column="id_card_pic" property="idCardPic"/>
        <result column="status" property="status"/>
        <result column="nursing_star" property="nursingStar"/>
        <!--        <collection property="hospitalList"-->
        <!--                    ofType="com.avatar.hospital.chaperone.response.baccount.OrganizationHospitalResponse"-->
        <!--                    resultMap="nursingHospital"/>-->
    </resultMap>

    <resultMap id="nursingDetail" type="com.avatar.hospital.chaperone.response.nursing.NursingDetailResponse">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="gender" property="gender"/>
        <result column="age" property="age"/>
        <result column="work_year" property="workYear"/>
        <result column="specialty" property="specialty"/>
        <result column="remark" property="remark"/>
        <result column="settle_way" property="settleWay"/>
        <result column="settle_time" property="settleTime"/>
        <result column="sponsor" property="sponsor"/>
        <result column="sponsor_price" property="sponsorPrice"/>
        <result column="certification_pic" property="certificationPic"/>
        <result column="id_card_pic" property="idCardPic"/>
        <result column="status" property="status"/>
        <result column="nursing_star" property="nursingStar"/>
        <result column="mobile" property="mobile"/>
        <collection property="hospitalList"
                    ofType="com.avatar.hospital.chaperone.response.baccount.OrganizationHospitalResponse"
                    resultMap="nursingHospital"/>
    </resultMap>

    <resultMap id="nursingHospitalPage" type="com.avatar.hospital.chaperone.response.nursing.NursingHospitalResponse">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="nursingId" property="nursingId"/>
    </resultMap>

    <resultMap id="nursingHospital" type="com.avatar.hospital.chaperone.response.baccount.OrganizationHospitalResponse">
        <id column="hid" property="id"/>
        <result column="hname" property="name"/>
    </resultMap>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>
    <sql id="cols_exclude_id">
        name,
        gender,
        age,
        workYear,
        specialty,
        remark,
        settleWay,
        settleTime,
        sponsor,
        sponsorPrice,
        certificationPic,
        idCardPic,
        status,
        nursing_star,
        created_by,
        created_at,
        updated_by,
        updated_at
    </sql>

    <select id="countByB" resultType="java.lang.Long">
        SELECT count(*)
        FROM (
        SELECT a1.id
        FROM `t_nursing` a1
                 LEFT JOIN `t_nursing_hospital` a2 on a1.`id` = a2.`nursing_id`
                 LEFT JOIN `t_organization` a3 on a2.`org_id` = a3.`id`
        <where>
            a1.deleted = 0
            <if test="name != null">
                and a1.name like CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null">
                and a1.status = #{status}
            </if>
        </where>
        GROUP BY a1.id
        )a
    </select>

    <select id="listByB" resultMap="nursing">
        SELECT a1.id as id,
               a1.name,
               a1.gender,
               a1.age,
               a1.work_year,
               a1.specialty,
               a1.remark,
               a1.settle_way,
               a1.settle_time,
               a1.sponsor,
               a1.sponsor_price,
               a1.id_card_pic,
               a1.certification_pic,
               a1.status,
               a1.nursing_star,
               a1.mobile
        FROM `t_nursing` a1
                 LEFT JOIN `t_nursing_hospital` a2 on a1.`id` = a2.`nursing_id`
                 LEFT JOIN `t_organization` a3 on a2.`org_id` = a3.`id`
        <where>
            a1.deleted = 0
            <if test="name != null">
                and a1.name like CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null">
                and a1.status = #{status}
            </if>
        </where>
        GROUP BY a1.id
        ORDER BY a1.id desc
        limit ${(offset-1)*limit},${limit}
    </select>

    <select id="findByBId" resultType="com.avatar.hospital.chaperone.response.nursing.NursingDetailResponse"
            resultMap="nursingDetail">
        SELECT a1.id   as id,
               a1.name,
               a1.gender,
               a1.age,
               a1.work_year,
               a1.specialty,
               a1.remark,
               a1.settle_way,
               a1.settle_time,
               a1.sponsor,
               a1.sponsor_price,
               a1.id_card_pic,
               a1.certification_pic,
               a1.status,
               a1.nursing_star,
               a1.mobile as mobile,
               a3.id   as hid,
               a3.name as hname
        FROM `t_nursing` a1
                 LEFT JOIN `t_nursing_hospital` a2 on a1.`id` = a2.`nursing_id`
                 LEFT JOIN `t_organization` a3 on a2.`org_id` = a3.`id`
        <where>
            a1.deleted = 0
            and a2.deleted = 0
            and a3.deleted = 0
            <if test="id != null">
                and a1.id = #{id}
            </if>
        </where>
    </select>

    <!--    <select id="getHospital" resultType="com.avatar.hospital.chaperone.response.baccount.OrganizationHospitalResponse">-->
    <!--        select a3.id   as id,-->
    <!--               a3.name as name,-->
    <!--               a2.nursing_id-->
    <!--        from `t_nursing_hospital` a2-->
    <!--                 inner join `t_organization` a3 on a2.`org_id` = a3.`id`-->
    <!--        where a2.nursing_id = #{nursingId}-->
    <!--    </select>-->

    <select id="listHospitalByB" resultType="com.avatar.hospital.chaperone.response.nursing.NursingHospitalResponse"
            resultMap="nursingHospitalPage">
        select a3.id,
               a3.name,
               a1.id as nursingId
        FROM `t_nursing` a1
                 LEFT JOIN `t_nursing_hospital` a2 on a1.`id` = a2.`nursing_id`
                 LEFT JOIN `t_organization` a3 on a2.`org_id` = a3.`id`
        <where>
            a1.deleted = 0 and a2.deleted = 0
              and a1.id in
            <foreach collection="nursingIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
</mapper>