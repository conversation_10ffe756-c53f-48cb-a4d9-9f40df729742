package com.avatar.hospital.chaperone.component.wx;

import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.*;
import com.github.binarywang.wxpay.bean.result.*;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/17
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WxPayComponent {

    private final WxPayService wxPayService;

    /**
     * 下单接口
     *
     * @param tradeType 交易类型
     * @param request   -
     * @return -
     */
    public WxPayUnifiedOrderV3Result payV3(TradeTypeEnum tradeType, WxPayUnifiedOrderV3Request request) {
        try {
            return wxPayService.unifiedOrderV3(tradeType, request);
        } catch (WxPayException e) {
            log.error("WxPayComponent pay error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 下单接口
     *
     * @param request   -
     * @return -
     */
    public WxPayUnifiedOrderResult pay(WxPayUnifiedOrderRequest request) {
        try {
            return wxPayService.unifiedOrder(request);
        } catch (WxPayException e) {
            log.error("WxPayComponent pay error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 退款接口
     *
     * @param request -
     * @return -
     */
    public WxPayRefundResult refund(WxPayRefundRequest request) {
        try {
            return wxPayService.refund(request);
        } catch (WxPayException e) {
            log.error("WxPayComponent refund error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 退款接口
     *
     * @param request -
     * @return -
     */
    public WxPayRefundV3Result refundV3(WxPayRefundV3Request request) {
        try {
            return wxPayService.refundV3(request);
        } catch (WxPayException e) {
            log.error("WxPayComponent refund error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 回调接口处理
     *
     * @param notifyData -
     * @return -
     */
    public WxPayOrderNotifyV3Result notifyV3(String notifyData, SignatureHeader header) {
        try {
            return wxPayService.parseOrderNotifyV3Result(notifyData,header);
        } catch (WxPayException e) {
            log.error("WxPayComponent notify error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }
    /**
     * 回调接口处理
     *
     * @param notifyData -
     * @return -
     */
    public WxPayOrderNotifyResult notify(String notifyData) {
        try {
            return wxPayService.parseOrderNotifyResult(notifyData);
        } catch (WxPayException e) {
            log.error("WxPayComponent notify error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 订单主要查询
     *
     * @param payId -
     * @return -
     */
    public WxPayOrderQueryResult queryOrder(Long payId) {
        log.info("WxPayComponent queryOrder payId:{}",payId);
        try {
            WxPayOrderQueryRequest request = new WxPayOrderQueryRequest();
            request.setOutTradeNo(payId.toString());
            return wxPayService.queryOrder(request);
        } catch (WxPayException e) {
            log.error("WxPayComponent queryOrder error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

}
