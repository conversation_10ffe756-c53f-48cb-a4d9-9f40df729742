package com.avatar.hospital.chaperone.response.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description: 订单简易信息
 * @author: sp0372
 * @create: 2023-10-11 16:55
 **/
@Data
public class OrderIdResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 陪护单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    public static final OrderIdResponse build(Long orderId) {
        OrderIdResponse obj = new OrderIdResponse();
        obj.setOrderId(orderId);
        return obj;
    }

}
