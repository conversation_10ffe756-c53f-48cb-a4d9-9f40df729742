<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.repair.mapper.RepairFormMapper">

    <select id="countForC" resultType="java.lang.Long">
        SELECT count(f.`id`) as num
        FROM `t_project_repair_form_task` task
        LEFT JOIN `t_project_repair_form` f on task.`repair_form_id`= f.id
        WHERE task.`deleted`= 0
        AND f.`deleted`= 0
        AND task.`executor_account_id`= #{operator}
        <if test="status != null">
            AND f.`status`= #{status}
        </if>

    </select>

    <select id="listForC" resultType="com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO">
        SELECT f.*
        FROM `t_project_repair_form_task` task
        LEFT JOIN `t_project_repair_form` f on task.`repair_form_id`= f.id
        WHERE task.`deleted`= 0
        AND f.`deleted`= 0
        AND task.`executor_account_id`= #{operator}
        <if test="status != null">
            AND f.`status`= #{status}
        </if>
        ORDER BY  id desc
        LIMIT #{offset},#{size};
    </select>
</mapper>