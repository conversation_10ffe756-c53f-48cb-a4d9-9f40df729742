package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.avatar.hospital.chaperone.database.order.mapper.OrderItemMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderItemRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单-套餐关联表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class OrderItemRepositoryServiceImpl extends ServiceImpl<OrderItemMapper, OrderItemDO> implements OrderItemRepositoryService {

    @Override
    public List<OrderItemDO> getByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderItemDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderItemDO::getOrderId,orderId);
        return list(queryWrapper);
    }

    @Override
    public List<OrderItemDO> getByOrderIdList(List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderItemDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrderItemDO::getOrderId,orderIdList);
        return list(queryWrapper);
    }

    @Override
    public List<OrderItemDO> getSetDelByOrderId(Long orderId, Long delVersion) {
        LambdaQueryWrapper<OrderItemDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderItemDO::getOrderId,orderId);
        List<OrderItemDO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        list.forEach(item -> item.setDeleted(delVersion));
        return list;
    }

    @Override
    public List<OrderItemDO> listByConsumerLog(List<Long> itemIdList,Integer dateHour) {
        if (CollUtils.isEmpty(itemIdList)) {
            return new ArrayList<>();
        }
        return super.baseMapper.listByConsumerLog(itemIdList,dateHour);
    }

    @Override
    public void checkExist(Long orderId) {
        List<OrderItemDO> orderItemDOList = getByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderItemDOList)) {
            throw BusinessException.buildBusinessException(ErrorCode.ORDER_NOT_BIND_ITEM_ERROR);
        }
    }

    @Override
    public Map<Long, String> listByOrderIdList(List<Long> orderIds) {
        if (CollUtils.isEmpty(orderIds)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<OrderItemDO> queryWrapper = queryWrapper();
        queryWrapper.select(OrderItemDO::getOrderId,OrderItemDO::getItemName);
        queryWrapper.in(OrderItemDO::getOrderId,orderIds);
        List<OrderItemDO> list = list(queryWrapper);
        Map<Long, List<OrderItemDO>> map = list.stream()
                .collect(Collectors.groupingBy(OrderItemDO::getOrderId));
        Map<Long,String> result = Maps.newHashMap();
        map.forEach((orderid,itemList) -> result.put(orderid,toItemName(itemList)));
        return result;
    }

    public String toItemName(List<OrderItemDO> list) {
        if (CollUtils.isEmpty(list)) {
            return StrUtils.EMPTY;
        }
        String itemNames = list.stream()
                .filter(item -> Objects.nonNull(item) && !StringUtils.isEmpty(item.getItemName()))
                .map(item -> item.getItemName())
                .collect(Collectors.joining(","));
        return itemNames;
    }

    private LambdaQueryWrapper<OrderItemDO> queryWrapper() {
        LambdaQueryWrapper<OrderItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItemDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
