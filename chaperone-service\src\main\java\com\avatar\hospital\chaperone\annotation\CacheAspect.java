package com.avatar.hospital.chaperone.annotation;

import com.alibaba.cola.dto.SingleResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-20 17:19
 **/
@Slf4j
@Aspect
@Component
@ConditionalOnClass(RedissonClient.class)
public class CacheAspect {
    private static final String KEY_TEMPLATE = "cache";
    @Resource
    private RedissonClient redissonClient;
    private final DefaultParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();
    private final SpelExpressionParser spelExpressionParser = new SpelExpressionParser();

    @Pointcut("@annotation(com.avatar.hospital.chaperone.annotation.Cache)")
    public void executeCache() {
    }

    @Around("executeCache()")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        String cacheKey = null;
        Object result = null;
        try {
            MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
            Cache cacheInfo = signature.getMethod().getAnnotation(Cache.class);
            String value = cacheInfo.value();
            // SpEL表达式解析key
            // 获得方法参数名数组
            String[] parameterNames = parameterNameDiscoverer.getParameterNames(signature.getMethod());
            if (parameterNames != null && parameterNames.length > 0) {
                EvaluationContext context = new StandardEvaluationContext();
                //获取方法参数值
                Object[] args = proceedingJoinPoint.getArgs();
                for (int i = 0; i < args.length; i++) {
                    // 替换SpEL里的变量值为实际值， 比如 #user -->  user对象
                    context.setVariable(parameterNames[i], args[i]);
                }
                // 解析出实际的信息
                cacheKey = buildCacheKey(spelExpressionParser.parseExpression(value).getValue(context, String.class));
            }
            Boolean cacheFlag = Boolean.TRUE;
            RBucket<Object> bucket = redissonClient.getBucket(cacheKey);
            result = bucket.get();
            if (Objects.isNull(result)) {
                result = proceedingJoinPoint.proceed();
                if (result instanceof SingleResponse) {
                    SingleResponse singleResponse = (SingleResponse) result;
                    cacheFlag = singleResponse.isSuccess();
                }
                // 缓存
                if (cacheFlag) {
                    bucket.set(result);
                    bucket.expire(cacheInfo.expire(),TimeUnit.SECONDS);
                }
            } else {
                log.info("Cache 命中缓存 cacheKey:{}",cacheKey);
            }
        } catch (Exception e) {
            log.warn("IdempotentAspect to proceed, e:{}", e.getMessage(), e);
            throw e;
        }
        return result;
    }

    /**
     * 缓存
     */
    public static String buildCacheKey(String... keys) {
        StringBuilder sb = new StringBuilder(KEY_TEMPLATE);
        for (String key : keys) {
            sb.append(":").append(key);
        }
        return sb.toString();
    }
}
