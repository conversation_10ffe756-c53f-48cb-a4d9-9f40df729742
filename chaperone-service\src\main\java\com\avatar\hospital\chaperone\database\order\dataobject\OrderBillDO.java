package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.order.enums.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单-账单;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_order_bill")
public class OrderBillDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 订单ID
     */
      private Long orderId;

  /**
   * 账单业务ID
   *   唯一: 订单号  + 账单类型 + 账单子类型 + 业务ID + deleted
   */
  private String bizId;

      /**
     * 总账单ID
     */
      private Long parentBillId;

      /**
     * 账单类型:参考，bill_type
       * @see OrderBillType
     */
      private Integer billType;

      /**
     * 账单子类型:参考，bill_sub_type
       * @see OrderBillSubType
     */
      private Integer billSubType;

      /**
     * 总共应收款金额，单位:分
     */
      private Integer priceReceivable;

      /**
     * 已收金额，单位:分
     */
      private Integer priceReceived;

      /**
     * 总共应退款金额，单位:分
     */
      private Integer priceRefundable;

      /**
     * 已退款金额，单位:分
     */
      private Integer priceRefunded;

      /**
     * 套餐ID名称
     */
      private String itemNames;

      /**
     * 用户是否可见 1 可见 0 不可见
       * @see OrderDisplay
     */
      private Integer display;

      /**
     * 备注,账单说明
     */
      private String remark;

      /**
     * 折扣
     */
      private Integer discount;

    /**
     * 支付方式
     * @see OrderTradeType
     */
    private Integer tradeType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 结束时间
     */
    private String nursingJson;

    /**
     * 病人姓名
     */
    private String patientName;

      public OrderBillDO createCashPay(Long operator) {
        OrderBillDO bill = new OrderBillDO();
        bill.setId(id);
        bill.setPriceReceived(priceReceivable);
        bill.setPriceRefunded(priceRefundable);
        bill.setTradeType(OrderTradeType.CASH.getStatus());
        bill.setUpdateBy(operator);
        return bill;
      }
}
