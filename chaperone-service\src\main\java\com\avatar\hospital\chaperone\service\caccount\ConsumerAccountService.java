package com.avatar.hospital.chaperone.service.caccount;

import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountBasicInfoWriteConditionRequest;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountUpdateRequest;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountBasicInfoWriteConditionResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountDetailResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountUpdateResponse;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
public interface ConsumerAccountService {


    /**
     * C端用户基本信息填写情况
     *
     * @param request -
     * @return -
     */
    ConsumerAccountBasicInfoWriteConditionResponse basicInfoWriteCondition(ConsumerAccountBasicInfoWriteConditionRequest request);

    /**
     * C端用户更新
     *
     * @param request -
     * @return -
     */
    ConsumerAccountUpdateResponse update(ConsumerAccountUpdateRequest request);

    /**
     * C端用户详情
     *
     * @param accountId -
     * @return -
     */
    ConsumerAccountDetailResponse detail(Long accountId);


    /**
     * C端用户详情
     *
     * @param mobile -
     * @return -
     */
    ConsumerAccountDetailResponse getByMobile(String mobile);
}
