package com.avatar.hospital.chaperone.database.nursing.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleMenuDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingHospitalDO;
import com.avatar.hospital.chaperone.database.nursing.mapper.NursingHospitalMapper;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingHospitalRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 护工-院区; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class NursingHospitalRepositoryServiceImpl extends ServiceImpl<NursingHospitalMapper, NursingHospitalDO> implements NursingHospitalRepositoryService {



    @Override
    public Boolean deleteByNursingIds(Set<Long> nursingIds) {
        if (CollectionUtils.isEmpty(nursingIds)) {
            return false;
        }
        LambdaUpdateWrapper<NursingHospitalDO> updateWrapper = updateWrapper();
        updateWrapper.set(NursingHospitalDO::getDeleted,System.currentTimeMillis());
        if (nursingIds.size() == 1) {
            updateWrapper.eq(NursingHospitalDO::getNursingId, nursingIds.toArray()[0]);
        }
        else {
            updateWrapper.in(NursingHospitalDO::getNursingId, nursingIds);
        }
        return update(updateWrapper);
    }

    @Override
    public List<NursingHospitalDO> findByNursingIds(Set<Long> nursingIds) {
        if (CollectionUtils.isEmpty(nursingIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<NursingHospitalDO> queryWrapper = queryWrapper();
        if (nursingIds.size() == 1) {
            queryWrapper.eq(NursingHospitalDO::getNursingId, nursingIds.toArray()[0]);
        }
        else {
            queryWrapper.in(NursingHospitalDO::getNursingId, nursingIds);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(List<NursingHospitalDO> buildNursingHospitalDO) {
        if (CollectionUtils.isEmpty(buildNursingHospitalDO)){
            return ;
        }
        if (!saveBatch(buildNursingHospitalDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
    }

    private LambdaQueryWrapper<NursingHospitalDO> queryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    private LambdaUpdateWrapper<NursingHospitalDO> updateWrapper() {
        return new LambdaUpdateWrapper<>();
    }
}
