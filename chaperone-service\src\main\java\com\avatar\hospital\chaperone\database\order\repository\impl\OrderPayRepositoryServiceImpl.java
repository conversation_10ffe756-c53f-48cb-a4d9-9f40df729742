package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.builder.order.OrderPayBuilder;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderPayDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderPayStatus;
import com.avatar.hospital.chaperone.database.order.mapper.OrderPayMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderPayRepositoryService;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.OrderUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单-支付记录; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class OrderPayRepositoryServiceImpl extends ServiceImpl<OrderPayMapper, OrderPayDO> implements OrderPayRepositoryService {

    @Override
    @NotNull
    public List<OrderPayDO> findByOrderIdList(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds) ) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderPayDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrderPayDO::getOrderId,orderIds);
        queryWrapper.eq(OrderPayDO::getPayStatus, OrderPayStatus.SUCCESS.getStatus());
        List<OrderPayDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list;
    }

    @Override
    public OrderPayDO getPay(Long billId, Integer price) {
        if (Objects.isNull(billId) || Objects.isNull(price)) {
            return null;
        }
        // 获取最新一条支付中的数据
        Page<OrderPayDO> page = PageDTO.of(1, 1);
        LambdaQueryWrapper<OrderPayDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderPayDO::getBillId,billId);
        queryWrapper.ge(OrderPayDO::getPayStatus,OrderPayStatus.PAYING.getStatus());
        queryWrapper.orderByDesc(OrderPayDO::getCreatedAt);
        page = page(page,queryWrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return null;
        }
        OrderPayDO orderPayDO = page.getRecords().get(0);
        // 如果是待支付,且订单已过期 , 返回空订单重新生成
        if (OrderPayStatus.PAYING.matchWith(orderPayDO.getPayStatus())
                && (OrderUtils.outTime(orderPayDO.getPayOutTime()) || !Objects.equals(orderPayDO.getPrice(),price))) {
            updateById(OrderPayBuilder.updatePayStatus(orderPayDO.getId(),OrderPayStatus.TIME_OUT.getStatus()));
            return null;
        }
        if (OrderPayStatus.PAYING.matchWith(orderPayDO.getPayStatus())
                && StringUtils.isEmpty(orderPayDO.getPayInfo())) {
            updateById(OrderPayBuilder.updateDel(orderPayDO.getId()));
            return null;
        }

        return orderPayDO;
    }

    @Override
    public OrderPayDO getPaySuccess(Long billId) {
        LambdaQueryWrapper<OrderPayDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderPayDO::getBillId,billId);
        queryWrapper.eq(OrderPayDO::getPayStatus,OrderPayStatus.SUCCESS.getStatus());
        List<OrderPayDO> list = list(queryWrapper);
        if (CollUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    private LambdaQueryWrapper<OrderPayDO> queryWrapper() {
        LambdaQueryWrapper<OrderPayDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPayDO::getDeleted, OrderConst.NO_DELETED);
        return queryWrapper;
    }
}
