package com.avatar.hospital.chaperone.database.baccount.repository;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountOrganizationDO;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * B端用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface WebAccountRepositoryService extends IService<AccountDO> {

    /**
     * 添加B端用户
     *
     * @param accountDO               -
     * @param accountOrganizationList -
     * @return -
     */
    Long add(AccountDO accountDO, List<AccountOrganizationDO> accountOrganizationList);

    /**
     * 增量更新B端用户
     *
     * @param accountDO               -
     * @param accountOrganizationList -
     * @return -
     */
    Boolean incrementUpdate(AccountDO accountDO, List<AccountOrganizationDO> accountOrganizationList);

    /**
     * 修改用户最后登录信息
     *
     * @param accountId     -
     * @param lastLoginIp   -
     * @param lastLoginDate -
     * @return -
     */
    Boolean updateLoginInformation(Long accountId, String lastLoginIp, LocalDateTime lastLoginDate);

    /**
     * 修改密码
     *
     * @param accountId   -
     * @param newPassword -
     * @param updateBy -
     * @return -
     */
    Boolean changePassword(Long accountId, String newPassword, Long updateBy);

    /**
     * 根据id批量删除
     *
     * @param ids      -
     * @param updateBy -
     * @return -
     */
    Boolean deleteByIds(Set<Long> ids, Long updateBy);

    /**
     * 给用户分配角色
     *
     * @param accountId -
     * @param roleIds   -
     * @return -
     */
    Boolean allocationRole(Long accountId, Set<Long> roleIds);

    /**
     * 根据ID查询
     *
     * @param id -
     * @return -
     */
    AccountDO findById(Long id);

    /**
     * 根据ID批量查询
     *
     * @param ids -
     * @return -
     */
    List<AccountDO> findByIds(Set<Long> ids);

    /**
     * 根据手机号码查询用户
     *
     * @param phoneNumber -
     * @return -
     */
    AccountDO findByPhoneNumber(String phoneNumber);

    /**
     * 分页查询B端用户
     *
     * @param pageIndex -
     * @param pageSize  -
     * @param accountDO -
     * @return -
     */
    PageResponse<AccountDO> paging(Integer pageIndex, Integer pageSize, AccountDO accountDO);

    /**
     * 通过id
     * @param idList
     * @return id,Name
     */
    Map<Long, String> findMapByIdList(List<Long> idList);

    /**
     *
     * @param id
     * @return
     */
    String findNameById(Long id);
}
