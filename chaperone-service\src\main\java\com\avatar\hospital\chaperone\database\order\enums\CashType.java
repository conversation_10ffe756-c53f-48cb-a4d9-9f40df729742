package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * Description:
 *  账单类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum CashType implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    ADD(1, "增加"),
    EXTRACT(2, "提取"),
    ;

    private final Integer status;

    private final String describe;


    CashType(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static CashType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (CashType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        CashType e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    public static  final Map<Integer, String> toMap() {
        CashType[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (CashType value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

}
