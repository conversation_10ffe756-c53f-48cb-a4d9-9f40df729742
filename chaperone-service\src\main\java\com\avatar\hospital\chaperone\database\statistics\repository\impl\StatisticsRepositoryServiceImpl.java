package com.avatar.hospital.chaperone.database.statistics.repository.impl;

import com.avatar.hospital.chaperone.database.statistics.dataobject.StatisticsDO;
import com.avatar.hospital.chaperone.database.statistics.mapper.StatisticsMapper;
import com.avatar.hospital.chaperone.database.statistics.repository.StatisticsRepositoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 统计-报表数据; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class StatisticsRepositoryServiceImpl extends ServiceImpl<StatisticsMapper, StatisticsDO> implements StatisticsRepositoryService {

}
