package com.avatar.hospital.chaperone.request.plan;

import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.request.repair.RepairFormCreateRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 17:26
 */
@Data
public class TaskExecuteRequest implements OperatorReq {

    private Long id;
    /**
     * 设备情况
     */
    private String remark;
    /**
     * 是否需要报修
     */
    private Boolean isRepair;

    private PlanType planType;

    /**
     * 报修单
     */
    RepairFormCreateRequest repairFormCreateRequest;

    /**
     * 操作人
     */
    public Operator operatorUser;
}
