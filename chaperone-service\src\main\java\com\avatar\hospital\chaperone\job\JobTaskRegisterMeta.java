package com.avatar.hospital.chaperone.job;

import lombok.Data;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.lang.reflect.Type;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-25 11:32
 **/
@Data
public class JobTaskRegisterMeta implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 任务名称
     */
    private String code;
    /**
     * 描述
     */
    private String description;
    /**
     * spring Bean
     */
    private Object bean;
    /**
     * 任务方法
     */
    private Method method;

    private Type[] parameterTypes;

    public String toStr() {
        return description + "("+code+")";
    }
}
