package com.avatar.hospital.chaperone.database.baccount.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * B端用户和组织机构关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@TableName("t_account_organization")
public class AccountOrganizationDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 组织机构ID
     */
    private Long organizationId;

}
