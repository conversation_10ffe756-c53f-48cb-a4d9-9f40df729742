package com.avatar.hospital.chaperone.component.wx;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountOpenIdDO;
import com.avatar.hospital.chaperone.database.caccount.repository.AccountOpenIdRepositoryService;
import com.binarywang.spring.starter.wxjava.miniapp.properties.WxMaProperties;
import com.binarywang.spring.starter.wxjava.mp.properties.WxMpProperties;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 微信公众号同步组件
 *
 * <AUTHOR>
 * @since 2023/10/17
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WxUserSyncComponent {
    private final WxMpProperties wxMpProperties;
    private final WxMaProperties wxMaProperties;
    private final WxMpService wxMpService;
    private final WxMaService wxMaService;

    private final AccountOpenIdRepositoryService accountOpenIdRepositoryService;

    /**
     * 发送固定模板消息
     *
     * @param accountId         -
     * @param templateMessageId 模板消息ID
     */
    public void sync() {
        log.info("WxUserSyncComponent[]sync start ");
        String nextOpenId = null;
        WxMpUserList wxMpUserList = null;
        Boolean finish = false;
        LocalDateTime now = LocalDateTime.now();
        try {
            do {
                LinkedList<WxMpUser> WxMpUserList = new LinkedList<>();
                wxMpUserList = StringUtils.isNotBlank(nextOpenId) ?
                        wxMpService.getUserService().userList(nextOpenId)
                        : wxMpService.getUserService().userList();
                if (Objects.nonNull(wxMpUserList)) {
                    List<String> openids = wxMpUserList.getOpenids();
                    if (CollectionUtils.isNotEmpty(openids)) {
                        for (String openid : openids) {
                            WxMpUser wxMpUser = wxMpService.getUserService().userInfo(openid);
                            WxMpUserList.add(wxMpUser);
                        }
                    }
                    nextOpenId = wxMpUserList.getNextOpenid();
                }
                // 通过unionId获取用户数据
                if (CollectionUtils.isNotEmpty(WxMpUserList)) {
                    List<AccountOpenIdDO> addList = new LinkedList<>();
                    List<String> unionIdList = WxMpUserList.stream().map(item -> item.getUnionId())
                            .collect(Collectors.toList());
                    List<AccountOpenIdDO> list =  accountOpenIdRepositoryService.findAllByUnionIdList(unionIdList);
                    Map<String, List<AccountOpenIdDO>> collect = list.stream()
                            .collect(Collectors.groupingBy(AccountOpenIdDO::getUnionId));

                    Map<String, WxMpUser> wxMpUserUnionIdRef = WxMpUserList.stream().collect(Collectors.toMap(WxMpUser::getUnionId, Function.identity()));

                    collect.forEach((k,l) -> {
                        // 如果list 只有一个 并且是小程序的appid 那么需要把公众号的数据添加到数据库
                        boolean addFlag = l.stream()
                                .filter(item -> Objects.equals(item.getAppId(),wxMpProperties.getAppId()))
                                .count() <= 0;

                        if (addFlag) {
                            AccountOpenIdDO accountOpenIdDO = l.get(0);
                            accountOpenIdDO.setId(null);
                            accountOpenIdDO.setOpenId(wxMpUserUnionIdRef.get(accountOpenIdDO.getUnionId()).getOpenId());
                            accountOpenIdDO.setAppId(wxMpProperties.getAppId());
                            accountOpenIdDO.setCreatedAt(now);
                            accountOpenIdDO.setUpdatedAt(now);
                            addList.add(accountOpenIdDO);
                        }
                    });
                    // 批量添加数据
                    accountOpenIdRepositoryService.addBatch(addList);
                }

                finish = wxMpUserList.getOpenids().isEmpty()
                        || wxMpUserList.getCount() < 1000;
            }while (!finish);
            log.info("WxUserSyncComponent[]sync end ");
        } catch (Exception e) {
            log.error("WxUserSyncComponent sync error:{}", Throwables.getStackTraceAsString(e));
        }

    }

}
