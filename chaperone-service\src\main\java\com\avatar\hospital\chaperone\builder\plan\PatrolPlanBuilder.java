package com.avatar.hospital.chaperone.builder.plan;

import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.database.plan.dataobject.*;
import com.avatar.hospital.chaperone.database.plan.enums.CircleType;
import com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType;
import com.avatar.hospital.chaperone.database.plan.enums.TaskStatusType;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.response.plan.PlanVO;
import com.avatar.hospital.chaperone.utils.CodeUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 10:52
 */
public class PatrolPlanBuilder {

    public static PlanVO build(PatrolPlanDO patrolPlanDO) {
        PlanVO planVO = new PlanVO();
        if (Objects.isNull(patrolPlanDO)) {
            return null;
        }
        BeanUtils.copyProperties(patrolPlanDO, planVO);
        return planVO;

    }

    public static PatrolPlanDO build(PlanRequest request) {
        PatrolPlanDO planDO = new PatrolPlanDO();
        BeanUtils.copyProperties(request, planDO);
        planDO.setUpdateBy(request.getOperator());
        if (Objects.nonNull(request.getId())) {
            return planDO;
        }
        planDO.setStatus(PlanStatusType.VALID.getCode());
        planDO.setCode(CodeUtil.generateCode(CodeBizType.XJJH));
        planDO.setCreateBy(request.getOperator());

        return planDO;

    }

    public static List<PatrolPlanRefDeviceDO> buildRefDevice(PlanRequest request) {
        List<PatrolPlanRefDeviceDO> refDeviceDOS = request.getDeviceIds().stream().map(o -> {
            PatrolPlanRefDeviceDO refDeviceDO = new PatrolPlanRefDeviceDO();
            refDeviceDO.setDeviceId(o);
            refDeviceDO.setPlanId(request.getId());
            refDeviceDO.setCreateBy(request.getOperator());
            refDeviceDO.setUpdateBy(request.getOperator());
            return refDeviceDO;
        }).collect(Collectors.toList());

        return refDeviceDOS;

    }

    public static List<PatrolPlanRefOrgDO> buildRefOrg(PlanRequest request) {
        List<PatrolPlanRefOrgDO> refOrgDOS = request.getOrgIds().stream().map(o -> {
            PatrolPlanRefOrgDO refOrgDO = new PatrolPlanRefOrgDO();
            refOrgDO.setOrgId(o);
            refOrgDO.setPlanId(request.getId());
            refOrgDO.setCreateBy(request.getOperator());
            refOrgDO.setUpdateBy(request.getOperator());
            return refOrgDO;
        }).collect(Collectors.toList());

        return refOrgDOS;

    }

    public static List<PatrolPlanRefExecutorDO> buildRefExecutor(PlanRequest request) {
        List<PatrolPlanRefExecutorDO> refExecutorDOS = request.getExecutorIds().stream().map(o -> {
            PatrolPlanRefExecutorDO refExecutorDO = new PatrolPlanRefExecutorDO();
            refExecutorDO.setExecutorAccountId(o);
            refExecutorDO.setPlanId(request.getId());
            refExecutorDO.setCreateBy(request.getOperator());
            refExecutorDO.setUpdateBy(request.getOperator());
            return refExecutorDO;
        }).collect(Collectors.toList());

        return refExecutorDOS;

    }

    public static List<PatrolPlanTaskDO> buildTask(PatrolPlanDO patrolPlanDO, List<Long> deviceIds) {

        List<PatrolPlanTaskDO> taskDOS = Lists.newArrayListWithCapacity(deviceIds.size());
        for (int i = 0; i < deviceIds.size(); i++) {
            PatrolPlanTaskDO patrolPlanTaskDO = new PatrolPlanTaskDO();
            taskDOS.add(patrolPlanTaskDO);
            patrolPlanTaskDO.setCode(CodeUtil.generateCode(CodeBizType.XJRW));
            patrolPlanTaskDO.setDeviceId(deviceIds.get(i));
            patrolPlanTaskDO.setPlanId(patrolPlanDO.getId());
            patrolPlanTaskDO.setName(PatrolPlanTaskDO.generateName(patrolPlanDO.getName(), CircleType.of(patrolPlanDO.getCircleType()), i + 1));
            patrolPlanTaskDO.setStatus(TaskStatusType.NON_COMPLETED.getCode());
            patrolPlanTaskDO.setCreateBy(patrolPlanDO.getUpdateBy());
            patrolPlanTaskDO.setUpdateBy(patrolPlanDO.getUpdateBy());
        }
        return taskDOS;
    }
}
