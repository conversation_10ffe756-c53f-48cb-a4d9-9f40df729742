package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.ScheduledLogDO;
import com.avatar.hospital.chaperone.database.order.mapper.ScheduledLogMapper;
import com.avatar.hospital.chaperone.database.order.repository.ScheduledLogRepositoryService;
import com.avatar.hospital.chaperone.job.ScheduledType;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 定时任务执行记录; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Service
public class ScheduledLogRepositoryServiceImpl extends ServiceImpl<ScheduledLogMapper, ScheduledLogDO> implements ScheduledLogRepositoryService {

    @Override
    public ScheduledLogDO create(ScheduledType scheduledType) {
        long id = IdUtils.getId();

        ScheduledLogDO scheduledLogDO = new ScheduledLogDO();
        scheduledLogDO.setId(id);
        scheduledLogDO.setName(scheduledType.getDescribe());
        scheduledLogDO.setCode(scheduledType.getCode());
        scheduledLogDO.setCost(0);
        scheduledLogDO.setResult(0);
        scheduledLogDO.setRemark("");
        scheduledLogDO.setCreateBy(1L);
        scheduledLogDO.setUpdateBy(1L);
        scheduledLogDO.setDeleted(DelUtils.NO_DELETED);
        save(scheduledLogDO);
        return scheduledLogDO;
    }
}
