package com.avatar.hospital.chaperone.database.item.enums;

import lombok.Getter;

/**
 * Description:
 *  商品上下架状态
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum ItemDisplay {

    NONE(-1, "未知"),
    YES(1, "外显"),

    NO(0, "不外显"),


    ;

    private final Integer status;

    private final String describe;


    ItemDisplay(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static ItemDisplay of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (ItemDisplay itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
