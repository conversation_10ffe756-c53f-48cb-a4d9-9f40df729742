package com.avatar.hospital.chaperone.database.baccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrganizationStatus {

    /**
     * 禁用
     */
    DISABLE(0, "禁用"),


    /**
     * 启用
     */
    ENABLE(1, "启用"),


    ;

    private final Integer status;

    private final String describe;


    OrganizationStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrganizationStatus of(Integer status) {
        if (status == null) {
            return null;
        }
        for (OrganizationStatus organizationStatus : values()) {
            if (status.equals(organizationStatus.getStatus())) {
                return organizationStatus;
            }
        }
        return null;
    }
}
