-- MySQL dump 10.13  Distrib 8.0.31, for Win64 (x86_64)
--
-- Host: localhost    Database: message_service
-- ------------------------------------------------------
-- Server version	5.7.36-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `t_c_account`
--

DROP TABLE IF EXISTS `t_c_account`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_c_account` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `name` varchar(256) COMMENT '用户名称',
  `phone_number` varchar(20) COMMENT '电话号码',
  `sex` tinyint(4) unsigned DEFAULT '0' COMMENT '0:未知;1:男;2:女',
  `birthday` date COMMENT '出生日期',
  `avatar_url` varchar(256) COMMENT '用户头像地址',
  `status` tinyint(4) unsigned DEFAULT '1' COMMENT '启用状态 0:停用;1:启用',
  `create_by` bigint(20) unsigned NOT NULL COMMENT '创建者',
  `update_by` bigint(20) unsigned NOT NULL COMMENT '更新者',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `deleted` bigint(20) unsigned DEFAULT '0' COMMENT '是否删除 0:未删除;其他值标识已删除',
  PRIMARY KEY (`id`),
  KEY `idx_phone_number` (`phone_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='C端用户表';
/*!40101 SET character_set_client = @saved_cs_client */;



--
-- Table structure for table `t_account_open_id`
--

DROP TABLE IF EXISTS `t_account_open_id`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_account_open_id` (
  `id` bigint(20) unsigned AUTO_INCREMENT NOT NULL COMMENT '主键',
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `open_id` varchar(256) COMMENT '三方用户标识openId',
  `union_id` varchar(256) COMMENT '三方用户标识union_id',
  `type` tinyint(4) unsigned DEFAULT '1' COMMENT '三方账户类型 1:微信',
  `app_id` varchar(256) COMMENT 'appId',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `deleted` bigint(20) unsigned DEFAULT '0' COMMENT '是否删除 0:未删除;其他值标识已删除',
  PRIMARY KEY (`id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_open_id` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='C端用户关联三方用户openId表';
/*!40101 SET character_set_client = @saved_cs_client */;



/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2023-04-27 16:19:19
