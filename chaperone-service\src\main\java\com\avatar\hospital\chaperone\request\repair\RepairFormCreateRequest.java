package com.avatar.hospital.chaperone.request.repair;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:48
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormCreateRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 报修系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他,7-暖通系统，8-建筑系统，9-消防系统）
     */
    private Integer systemType;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 附件URL,数组
     */
    private String attachments;

    /**
     * 来源（1-pc,2-h5）
     * @see
     */
    private Integer sourceType;

    /**
     * 关联业务ID
     */
    private Long bizId;

    /**
     * 关联业务（0 未关联业务 1-巡检计划任务，2-维保计划任务）
     * @see com.avatar.hospital.chaperone.database.repair.enums.RepairFormBizType
     */
    private Integer bizType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    /**
     * 报修位置
     */
    private String location;
}
