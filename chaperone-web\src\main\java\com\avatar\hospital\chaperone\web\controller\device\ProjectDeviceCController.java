package com.avatar.hospital.chaperone.web.controller.device;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.device.DeviceAddRequest;
import com.avatar.hospital.chaperone.request.device.DeviceCPagingRequest;
import com.avatar.hospital.chaperone.request.device.DevicePagingRequest;
import com.avatar.hospital.chaperone.request.device.DeviceUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.device.DeviceAddResponse;
import com.avatar.hospital.chaperone.response.device.DevicePagingResponse;
import com.avatar.hospital.chaperone.response.device.DeviceUpdateResponse;
import com.avatar.hospital.chaperone.service.device.DeviceService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.utils.QrUtils;
import com.avatar.hospital.chaperone.web.validator.device.DeviceValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * C端工程-设备(web)
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/c/project/device")
public class ProjectDeviceCController {

    private final DeviceService deviceService;

    /**
     * 查询
     */
    @PostMapping(value = "/paging")
    public SingleResponse<PageResponse<DevicePagingResponse>> paging(@RequestBody DeviceCPagingRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return deviceService.pagingForC(request);
        });
    }

}
