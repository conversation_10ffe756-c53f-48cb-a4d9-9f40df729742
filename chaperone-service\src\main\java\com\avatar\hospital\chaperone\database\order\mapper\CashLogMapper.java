package com.avatar.hospital.chaperone.database.order.mapper;

import com.avatar.hospital.chaperone.database.order.dataobject.CashLogDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 现金记录表; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
public interface CashLogMapper extends BaseMapper<CashLogDO> {

    Integer sumByType(@Param("type") Integer type);

    /**
     * 通过类型修改
     * @param type
     * @param cashSource
     * @return
     */
    Integer sumByTypeAndSource(@Param("type") Integer type,@Param("cashSource")Integer cashSource);
}
