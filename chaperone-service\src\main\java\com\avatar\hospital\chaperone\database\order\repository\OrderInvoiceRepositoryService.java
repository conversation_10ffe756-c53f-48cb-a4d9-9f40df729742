package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderInvoiceDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 订单-发票; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderInvoiceRepositoryService extends IService<OrderInvoiceDO> {

    /**
     * 创建
     * @param invoice
     * @return
     */
    Long add(OrderInvoiceDO invoice);
}
