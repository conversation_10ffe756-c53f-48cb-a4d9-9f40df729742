package com.avatar.hospital.chaperone.builder.plan;

import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.response.plan.PlanVO;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 10:52
 */
public class PlanBuilder {


    public static PlanVO build(PlanDO planDO) {
        PlanVO planVO = new PlanVO();
        if (Objects.isNull(planDO)) {
            return null;
        }
        BeanUtils.copyProperties(planDO, planVO);
        return planVO;

    }

    public static PlanDO build(PlanRequest request) {

        if (PlanType.PATROL.equals(request.planType)) {
            return PatrolPlanBuilder.build(request);
        } else {
            return MaintenancePlanBuilder.build(request);

        }
    }

    public static List buildRefDevice(PlanRequest request) {
        if (PlanType.PATROL.equals(request.planType)) {
            return PatrolPlanBuilder.buildRefDevice(request);
        } else {
            return MaintenancePlanBuilder.buildRefDevice(request);
        }

    }

    public static List buildRefOrg(PlanRequest request) {
        if (PlanType.PATROL.equals(request.planType)) {
            return PatrolPlanBuilder.buildRefOrg(request);
        } else {
            return MaintenancePlanBuilder.buildRefOrg(request);
        }

    }

    public static List buildRefExecutor(PlanRequest request) {
        if (PlanType.PATROL.equals(request.planType)) {
            return PatrolPlanBuilder.buildRefExecutor(request);
        } else {
            return MaintenancePlanBuilder.buildRefExecutor(request);
        }

    }

    public static List buildTask(PlanType planType, PlanDO planDO, List<Long> deviceIds) {

        if (PlanType.PATROL.equals(planType)) {
            return PatrolPlanBuilder.buildTask((PatrolPlanDO) planDO, deviceIds);
        } else {
            return MaintenancePlanBuilder.buildTask((MaintenancePlanDO) planDO, deviceIds);
        }
    }
}
