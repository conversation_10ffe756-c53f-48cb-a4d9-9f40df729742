package com.avatar.hospital.chaperone.database.item.mapper;

import com.avatar.hospital.chaperone.database.item.dataobject.ItemDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 套餐表; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface ItemMapper extends BaseMapper<ItemDO> {


    Long countByC(@Param("orgId") Long orgId, @Param("serverType") Integer serverType);

    List<ItemDO> listByC(@Param("orgId")Long orgId, @Param("serverType")Integer serverType, @Param("offset")Integer offset, @Param("pageSize")Integer pageSize);
}
