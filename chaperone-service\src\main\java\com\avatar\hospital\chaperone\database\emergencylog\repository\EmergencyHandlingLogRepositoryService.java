package com.avatar.hospital.chaperone.database.emergencylog.repository;

import com.avatar.hospital.chaperone.database.emergencylog.dataobject.EmergencyHandlingLogDO;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogPagingRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.avatar.hospital.chaperone.response.PageResponse;

/**
 * <p>
 * 应急处置日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface EmergencyHandlingLogRepositoryService extends IService<EmergencyHandlingLogDO> {

    Long add(EmergencyHandlingLogDO emergencyHandlingLogDO);

    Boolean incrementUpdate(EmergencyHandlingLogDO emergencyHandlingLogDO);

    PageResponse<EmergencyHandlingLogDO> paging(EmergencyLogPagingRequest request);

    EmergencyHandlingLogDO findById(Long id);

    /**
     * 统计指定年份应急处置日志数量
     * @param year
     * @return
     */
    Integer countForYear(Integer year);

    /**
     * 统计指定月份应急处置日志数量
     * @param month
     * @return
     */
    Integer countForMonth(Integer month);
}
