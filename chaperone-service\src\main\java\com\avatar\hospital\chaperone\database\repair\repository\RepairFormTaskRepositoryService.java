package com.avatar.hospital.chaperone.database.repair.repository;

import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormTaskDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 报修单任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface RepairFormTaskRepositoryService extends IService<RepairFormTaskDO> {

    /**
     * 根据报修单ID列表查询
     * @param formIdList
     * @return
     */
    List<RepairFormTaskDO> getByFromIdList(List<Long> formIdList);

    /**
     * 根据报修单ID查询
     * @param id
     * @return
     */
    List<RepairFormTaskDO> getByFromId(Long id);
}
