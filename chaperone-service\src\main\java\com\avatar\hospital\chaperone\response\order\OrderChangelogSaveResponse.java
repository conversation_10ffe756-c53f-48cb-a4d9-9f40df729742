package com.avatar.hospital.chaperone.response.order;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderChangelogSaveResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Boolean success;

    public static final OrderChangelogSaveResponse build(Boolean id) {
        OrderChangelogSaveResponse obj = new OrderChangelogSaveResponse();
        obj.setSuccess(id);
        return obj;
    }

}
