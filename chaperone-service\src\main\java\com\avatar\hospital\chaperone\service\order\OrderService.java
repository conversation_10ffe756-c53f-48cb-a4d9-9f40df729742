package com.avatar.hospital.chaperone.service.order;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderDetailResponse;
import com.avatar.hospital.chaperone.response.order.OrderIdResponse;
import com.avatar.hospital.chaperone.response.order.OrderPriceCalculationResponse;
import com.avatar.hospital.chaperone.response.order.OrderResponse;

import java.util.List;
import java.util.Map;

/**
 * @program: hospital-chaperone
 * @description: 订单查询接口
 * @author: sp0372
 * @create: 2023-10-11 16:41
 **/
public interface OrderService {
    // c端
    /**
     * 订单提交接口
     *    C端提交订单数据,给到B端审核
     *    生成账单
     */
    OrderIdResponse create(OrderCreateRequest request);

    /**
     * 订单查询详情接口
     *     * 订单基本信息
     *     * 套餐信息
     *     * 预估费用信息
     * @param request
     * @return
     */
    OrderDetailResponse getById(OrderRequest request);

    /**
     * C端订单查询-分页
     *    入参
     *     * 用户ID
     *     * 订单状态
     * @param request
     * @return
     */
    PageResponse<OrderResponse> pagingForC(OrderCPageRequest request);

    /**
     * C端订单查询-分页
     *    入参
     *     * 用户ID
     *     * 订单状态
     * @param request
     * @return
     */
    PageResponse<OrderDetailResponse> pagingForCFull(OrderCPageRequest request);

    /**
     * 价格计算接口
     *     入参:
     *       套餐，开始日期,结束日期
     *     逻辑: 套餐单个价格
     */
    OrderPriceCalculationResponse priceCalculation(OrderPriceCalculationRequest request);

    /**
     * 取消申请接口
     *    先查询订单当前状态，是否已确认，如果已确认不让取消，走结算逻辑
     *    如果审核中，直接取消
     *    如果审核通过，算关闭
     */
    OrderIdResponse cancel(OrderRequest request);

    /**
     * 确认下单
     */
    OrderIdResponse confirm(OrderRequest request);

    /**
     * 结算申请
     */
    OrderIdResponse applySettle(OrderRequest request);

    // B端

    /**
     * 医院机构审核
     */
    OrderIdResponse commitHospital(OrderRequest request);

    /**
     * 物业机构审核
     */
    OrderIdResponse commitCertifiedProperty(OrderRequest request);

    /**
     * 确认完善信息
     */
    OrderIdResponse commitCompleteInfo(OrderRequest request);

    /**
     * 查询订单
     */
    PageResponse<OrderResponse> paging(OrderPageRequest request);


    /**
     * 订单修改接口
     */
    OrderIdResponse modify(OrderModifyRequest request);

    /**
     * 查询订单
     *   消耗记录查询
     */
    PageResponse<OrderResponse> pagingForConsumerLog(OrderConsumerLogPageRequest request);

    /**
     * 修改订单折扣
     *    生成一条修改记录
     *    修改总账单的应收金额，分追溯，不追溯2中情况
     *    追溯，修改消费明细数据
     *
     */
    OrderIdResponse modifyDiscount(OrderModifyDiscountRequest request);

    /**
     * 修改利润分层
     *    生成一条修改记录
     *    追溯，修改消费明细数据
     */
    OrderIdResponse modifyRate(OrderModifyRateRequest request);

    /**
     * 指派护工
     */
    OrderIdResponse modifyNursing(OrderModifyNursingRequest request);

    /**
     * 修改套餐
     */
    OrderIdResponse modifyItem(OrderModifyItemRequest request);

    /**
     * 结算提交
     * @param request
     * @return
     * @param
     */
    OrderIdResponse commitSettle(OrderSettleApplyRequest request);
}
