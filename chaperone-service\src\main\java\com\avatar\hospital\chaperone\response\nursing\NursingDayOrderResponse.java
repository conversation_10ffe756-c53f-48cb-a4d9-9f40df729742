package com.avatar.hospital.chaperone.response.nursing;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingDayOrderResponse implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    private String orderName;

    private Integer orderDate;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long nursingId;
}
