package com.avatar.hospital.chaperone.database.baccount.dataobject;

import com.avatar.hospital.chaperone.database.baccount.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * B端组织机构表-配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_organization_control")
public class OrganizationControlDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 订单审批策略
     *    默认 医确认 -> 物业确认 -> 待完善信息
     *    1 待完善信息
     */
    private Integer orderApproveModel;
}
