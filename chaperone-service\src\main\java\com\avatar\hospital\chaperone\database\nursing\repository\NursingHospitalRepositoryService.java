package com.avatar.hospital.chaperone.database.nursing.repository;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingHospitalDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 护工-院区; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface NursingHospitalRepositoryService extends IService<NursingHospitalDO> {


    /**
     * 根据护工id删除关联关系
     *
     * @param nursingIds
     * @return Boolean
     */
    Boolean deleteByNursingIds(Set<Long> nursingIds);

    /**
     * 根据nursingIds批量查找
     *
     * @param nursingIds -
     * @return -
     */
    List<NursingHospitalDO> findByNursingIds(Set<Long> nursingIds);

    /**
     * 新增医护和医院关联
     *
     * @param buildNursingHospitalDO
     * @return Long
     */
    void add(List<NursingHospitalDO> buildNursingHospitalDO);
}
