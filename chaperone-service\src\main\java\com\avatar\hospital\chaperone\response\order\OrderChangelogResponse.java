package com.avatar.hospital.chaperone.response.order;

import com.avatar.hospital.chaperone.database.order.enums.OrderLogEvent;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderChangelogResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 时间
     */
    private String time;

    /**
     * 修改事件
     * @see OrderLogEvent
     */
    private String event;


    /**
     * 事件描述
     * @see OrderLogEvent
     */
    private String eventName;

    /**
     * 修改前
     */
    private String before;

    /**
     * 修改后
     */
    private String after;

    /**
     * 操作人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 操作人手机号
     */
    private String operatorMobile;

    /**
     * 操作人来源 1 c端 2 b端 3 定时任务
     */
    private Integer operatorSource;

}
