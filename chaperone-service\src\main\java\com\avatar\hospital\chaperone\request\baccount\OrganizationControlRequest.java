package com.avatar.hospital.chaperone.request.baccount;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class OrganizationControlRequest implements Serializable {

    /**
     * 组织机构ID
     */
    private Long orgId;

    public static final OrganizationControlRequest build(Long orgId) {
        OrganizationControlRequest obj = new OrganizationControlRequest();
        obj.setOrgId(orgId);
        return obj;
    }

}
