package com.avatar.hospital.chaperone.database.plan.repository;

import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanRefDeviceDO;
import com.avatar.hospital.chaperone.database.plan.repository.base.PlanRefRepositoryService;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 巡检计划关联设备 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PatrolPlanRefDeviceRepositoryService extends IService<PatrolPlanRefDeviceDO> , PlanRefRepositoryService {


}
