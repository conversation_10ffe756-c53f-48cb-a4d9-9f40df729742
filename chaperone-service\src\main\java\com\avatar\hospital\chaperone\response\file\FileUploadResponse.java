package com.avatar.hospital.chaperone.response.file;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class FileUploadResponse implements Serializable {

    /**
     * 文件访问路径
     */
    private String fileUrl;

}
