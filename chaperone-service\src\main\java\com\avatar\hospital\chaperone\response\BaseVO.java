package com.avatar.hospital.chaperone.response;

import com.avatar.hospital.chaperone.annotation.serializer.LocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Setter
@Getter
public class BaseVO implements Serializable {

    private static final long serialVersionUID = 7733146836849713335L;
    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 创建时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updatedAt;

    /**
     * 删除时间 1:未删除;已删除:时间戳
     */
    private Long deleted;

}
