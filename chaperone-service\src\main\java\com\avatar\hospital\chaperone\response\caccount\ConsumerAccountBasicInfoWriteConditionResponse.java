package com.avatar.hospital.chaperone.response.caccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/19
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ConsumerAccountBasicInfoWriteConditionResponse implements Serializable {

    /**
     * 手机号码是否填写 true:已填写;false未填写
     */
    private Boolean phoneNumberWrite;

}
