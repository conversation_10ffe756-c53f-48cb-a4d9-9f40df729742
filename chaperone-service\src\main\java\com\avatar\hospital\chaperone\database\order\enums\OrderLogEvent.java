package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.avatar.hospital.chaperone.service.order.dto.OrderChangeLogDTO;
import lombok.Getter;

import java.util.Objects;

/**
 * Description:
 *  订单日志事件
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderLogEvent implements IEnumConvert<String> {
    NONE("NONE", "未知", OrderChangeLogDTO.AbsCompareDTO.class),
    ORDER_CREATE("ORDER_CREATE", "陪护单创建", OrderChangeLogDTO.Create.class),
    ORDER_MODIFY("ORDER_MODIFY", "陪护单修改(B端)", OrderChangeLogDTO.Modify.class),
    ORDER_CANCEL("CANCEL", "陪护单取消", OrderChangeLogDTO.Cancel.class),
    ORDER_CONFIRM("CONFIRM", "陪护单确认", OrderChangeLogDTO.Confirm.class),
    ORDER_COMMIT_HOSPITAL("ORDER_COMMIT_HOSPITAL", "院方审核", OrderChangeLogDTO.ModifyStatus.class),
    ORDER_COMMIT_CERTIFIED_PROPERTY("ORDER_COMMIT_CERTIFIED_PROPERTY", "物业审核", OrderChangeLogDTO.ModifyStatus.class),
    ORDER_COMMIT_COMPLETE_INFO("ORDER_COMMIT_COMPLETE_INFO", "完善信息确认", OrderChangeLogDTO.ModifyStatus.class),
    ORDER_APPLY_SETTLE("APPLY_SETTLE", "陪护单申请结算", OrderChangeLogDTO.ApplySettle.class),

    ORDER_MODIFY_DISCOUNT("MODIFY_DISCOUNT", "陪护单修改折扣", OrderChangeLogDTO.ModifyDiscount.class),
    ORDER_MODIFY_RATE("MODIFY_RATE", "陪护单修改费率", OrderChangeLogDTO.ModifyRate.class),
    ORDER_MODIFY_NURSING("MODIFY_NURSING", "陪护单修改护工", OrderChangeLogDTO.ModifyNursing.class),
    ORDER_MODIFY_ITEM("ORDER_MODIFY_ITEM", "陪护单修改套餐", OrderChangeLogDTO.ModifyItem.class),
    ORDER_MODIFY_BILL("MODIFY_BILL_INFO", "陪护单修改账单信息", OrderChangeLogDTO.ModifyBillInfo.class),
    ORDER_MODIFY_BILL_CASH_APY("MODIFY_BILL_CASH_PAY", "账单现金支付", OrderChangeLogDTO.ModifyBillCashPay.class),
    ORDER_GENERATOR_SETTLE("ORDER_GENERATOR_SETTLE", "生成结算单", OrderChangeLogDTO.ModifySettle.class),
    ORDER_SETTLE_REJECT("ORDER_SETTLE_REJECT", "结算单申请驳回", OrderChangeLogDTO.RejectSettleApply.class),
    ;

    private final String status;

    private final String describe;

    private final Class clazz;


    OrderLogEvent(String status, String describe,Class clazz) {
        this.status = status;
        this.describe = describe;
        this.clazz = clazz;
    }

    public static OrderLogEvent of(String status) {
        if (Objects.isNull(status)) {
            return NONE;
        }
        for (OrderLogEvent itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(String val) {
        OrderLogEvent e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }
}
