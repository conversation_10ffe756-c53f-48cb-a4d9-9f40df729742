package com.avatar.hospital.chaperone.service.baccount.impl;

import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.builder.baccount.MenuBuilder;
import com.avatar.hospital.chaperone.component.baccount.RoleComponent;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.MenuDO;
import com.avatar.hospital.chaperone.database.baccount.enums.MenuStatus;
import com.avatar.hospital.chaperone.database.baccount.enums.MenuType;
import com.avatar.hospital.chaperone.database.baccount.repository.MenuRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.WebAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.MenuAccountPermissionRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuAddRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuTreeRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuAccountPermissionResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuAddResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuPagingResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuTreeResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuUpdateResponse;
import com.avatar.hospital.chaperone.service.baccount.MenuService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MenuServiceImpl implements MenuService {

    private final MenuRepositoryService menuRepositoryService;
    private final WebAccountRepositoryService webAccountRepositoryService;
    private final RoleComponent roleComponent;

    @Override
    public MenuAddResponse add(MenuAddRequest request) {
        MenuDO menuDO = menuRepositoryService.findById(request.getParentId());
        AssertUtils.isNotNull(menuDO, ErrorCode.MENU_PARENT_NOT_EXIST);
        AssertUtils.isTrue(MenuStatus.ENABLE.getStatus().equals(menuDO.getStatus()), ErrorCode.MENU_PARENT_STATUS_NOT_ENABLED);

        Long menuId = menuRepositoryService.add(MenuBuilder.buildMenuDO(request, menuDO.getType()));
        return MenuAddResponse.builder().menuId(menuId).build();
    }

    @Override
    public MenuUpdateResponse update(MenuUpdateRequest request) {
        MenuDO menuDO = menuRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(menuDO, ErrorCode.MENU_NOT_EXIST);

        Boolean result = menuRepositoryService.incrementUpdate(MenuBuilder.buildMenuDO(request));
        AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);
        return MenuUpdateResponse.builder().success(true).build();
    }

    @Override
    public MenuDeleteResponse delete(MenuDeleteRequest request) {
        List<MenuDO> menuList = menuRepositoryService.findByIds(request.getIds());
        if (CollectionUtils.isEmpty(menuList)) {
            return MenuDeleteResponse.builder().success(true).build();
        }
        Set<Long> menuIds = menuList.stream().map(MenuDO::getId).collect(Collectors.toSet());
        Boolean result = menuRepositoryService.deleteByIds(menuIds, request.getUpdateBy());
        return MenuDeleteResponse.builder().success(result).build();
    }

    @Override
    public PageResponse<MenuPagingResponse> paging(MenuPagingRequest request) {
        PageResponse<MenuDO> accountPageResponse = menuRepositoryService.paging(request.getPageIndex(), request.getPageSize(), MenuBuilder.buildMenuDO(request));
        return MenuBuilder.buildMenuPagingResponse(accountPageResponse);
    }

    @Override
    public MenuDetailResponse detail(Long accountId) {
        MenuDO menuDO = menuRepositoryService.findById(accountId);
        AssertUtils.isNotNull(menuDO, ErrorCode.MENU_NOT_EXIST);
        return MenuBuilder.buildMenuDetailResponse(menuDO);
    }

    @Override
    public List<MenuTreeResponse> tree(MenuTreeRequest request) {
        log.info("MenuServiceImpl tree request:{}", JSON.toJSONString(request));
        List<MenuDO> menuList = menuRepositoryService.listAll();
        // 设置子节点
        return MenuBuilder.buildMenuTreeChildrenList(MenuBuilder.buildMenuTreeResponseList(menuList));
    }

    @Override
    public List<MenuTreeResponse> accountMenuTree(MenuTreeRequest request) {
        AccountDO accountDO = webAccountRepositoryService.findById(request.getAccountId());
        Set<Long> menuIds;
        if (accountDO != null && accountDO.admin()) {
            menuIds = menuRepositoryService.listAll().stream().map(MenuDO::getId).collect(Collectors.toSet());
        }
        else {
            menuIds = roleComponent.findAccountPermissionList(request.getAccountId());
        }
        List<MenuDO> menuList = menuRepositoryService.findByIdsAndTypesAndStatus(menuIds, Sets.newHashSet(MenuType.CATALOGUE.getType(), MenuType.BUTTON.getType(), MenuType.AUTHORITY.getType()), MenuStatus.ENABLE.getStatus());
        if (CollectionUtils.isEmpty(menuList)) {
            return Collections.emptyList();
        }
        return MenuBuilder.buildMenuTreeChildrenList(MenuBuilder.buildMenuTreeResponseList(menuList));
    }

    @Override
    public MenuAccountPermissionResponse accountPermission(MenuAccountPermissionRequest request) {
        Set<Long> menuIds = roleComponent.findAccountPermissionList(request.getAccountId());
        List<MenuDO> menuList = menuRepositoryService.findByIdsAndTypesAndStatus(menuIds, Sets.newHashSet(MenuType.AUTHORITY.getType()), MenuStatus.ENABLE.getStatus());
        if (CollectionUtils.isEmpty(menuList)) {
            return MenuAccountPermissionResponse.buildEmpty();
        }
        Set<String> permissions = menuList.stream().map(MenuDO::getMenuKey).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        return MenuAccountPermissionResponse.builder().permissions(permissions).build();
    }
}
