package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.template.serialize.ToDateTimeHForStringDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description: c端列表
 * @author: sp0372
 * @create: 2023-10-11 16:50
 **/
@Data
public class OrderPriceCalculationRequest implements Serializable {

    private static final long serialVersionUID = -1537431942438821886L;

    /**
     * 开始时间 ,格式,时间戳/毫秒
     */
    @JsonDeserialize(using = ToDateTimeHForStringDeserializer.class)
    private Integer startTime;

    /**
     * 结束时间 格式,时间戳/毫秒
     */
    @JsonDeserialize(using = ToDateTimeHForStringDeserializer.class)
    private Integer endTime;

    /**
     * 套餐列表
     */
    private List<Long> itemList;

    /**
     * 订单则扣(默认100000)
     *  1 - 100000
     */
    private Integer discount;

    public static final OrderPriceCalculationRequest build(Integer startTime,Integer endTime,List<Long> itemList,Integer discount) {
        OrderPriceCalculationRequest obj = new OrderPriceCalculationRequest();
        obj.setStartTime(startTime);
        obj.setEndTime(endTime);
        obj.setItemList(itemList);
        obj.setDiscount(discount);
        return obj;
    }

}
