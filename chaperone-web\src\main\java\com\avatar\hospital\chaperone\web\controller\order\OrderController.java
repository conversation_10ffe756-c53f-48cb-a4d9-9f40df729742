package com.avatar.hospital.chaperone.web.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.database.order.enums.OrderStatus;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.*;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.web.validator.order.OrderValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * B端陪护单-订单
 * @program: hospital-chaperone
 * @description: 套餐
 * @author: sp0372
 * @create: 2023-10-13 10:07
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/order/base")
public class OrderController {
    private final OrderService orderService;

    /**
     * 查询
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<OrderResponse>> paging(@RequestBody OrderPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderService.paging(request);
        });
    }

    /**
     * 查询-详情
     * @param request
     * @return
     */
    @PostMapping("detail")
    public SingleResponse<OrderDetailResponse> getById(@RequestBody OrderRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderService.getById(request);
        });
    }

    /**
     * 创建-订单创建
     * @param request
     * @return
     */
    @PostMapping("create")
    public SingleResponse<OrderIdResponse> create(@RequestBody OrderCreateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.createValidate(request);
            return orderService.create(request);
        });
    }

    /**
     * 查询-价格计算
     * @param request
     * @return
     */
    @PostMapping("calculation")
    public SingleResponse<OrderPriceCalculationResponse> priceCalculation(@RequestBody OrderPriceCalculationRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderService.priceCalculation(request);
        });
    }

    /**
     * 修改-基本信息
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.id")
    @PostMapping("modify")
    public SingleResponse<OrderIdResponse> modify(@RequestBody OrderModifyRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.modifyValidate(request);
            return orderService.modify(request);
        });
    }

    /**
     * 修改-折扣
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.orderId")
    @PostMapping("modify/discount")
    public SingleResponse<OrderIdResponse> modifyDiscount(@RequestBody OrderModifyDiscountRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.modifyDiscountValidate(request);
            return orderService.modifyDiscount(request);
        });
    }

    /**
     * 修改-分层比例
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.orderId")
    @PostMapping("modify/rate")
    public SingleResponse<OrderIdResponse> modifyRate(@Validated @RequestBody OrderModifyRateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.modifyRateValidate(request);
            return orderService.modifyRate(request);
        });
    }

    /**
     * 修改-绑定护工
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.orderId")
    @PostMapping("modify/nursing")
    public SingleResponse<OrderIdResponse> modifyNursing(@Validated @RequestBody OrderModifyNursingRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.modifyNursingValidate(request);
            return orderService.modifyNursing(request);
        });
    }

    /**
     * 修改-绑定套餐
     * @param request
     * @return 击鼓欧
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.orderId")
    @PostMapping("modify/item")
    public SingleResponse<OrderIdResponse> modifyOrderItem(@Validated @RequestBody OrderModifyItemRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.modifyOrderItemValidate(request);
            return orderService.modifyItem(request);
        });
    }

    /**
     * 审核-院方
     * @param request
     * @return 结果
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.orderId")
    @PostMapping("commit/hospital")
    public SingleResponse<OrderIdResponse> commitHospital(@RequestBody OrderRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.commitHospitalValidate(request);
            return orderService.commitHospital(request);
        });
    }

    /**
     * 审核-物业
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.orderId")
    @PostMapping("commit/certifiedProperty")
    public SingleResponse<OrderIdResponse> commitCertifiedProperty(@RequestBody OrderRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.commitCertifiedPropertyValidate(request);
            return orderService.commitCertifiedProperty(request);
        });
    }

    /**
     * 审核-信息确认
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.orderId")
    @PostMapping("commit/completeInfo")
    public SingleResponse<OrderIdResponse> commitCompleteInfo(@RequestBody OrderRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.commitCompleteInfoValidate(request);
            return orderService.commitCompleteInfo(request);
        });
    }

    /**
     * 审核-结算单
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.orderId")
    @PostMapping("commit/settle")
    public SingleResponse<OrderIdResponse> commitSettle(@RequestBody OrderSettleApplyRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.commitSettleValidate(request);
            return orderService.commitSettle(request);
        });
    }

    /**
     * 枚举-订单状态枚举(orderStatus)
     * @return
     */
    @PostMapping("enum/orderStatus")
    public SingleResponse<Map<Integer,String>> orderStatus() {
        return TemplateProcess.doProcess(log, () -> {
            return OrderStatus.toMap();
        });
    }


}
