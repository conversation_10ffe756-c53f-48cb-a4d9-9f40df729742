package com.avatar.hospital.chaperone.builder.order;

import com.alibaba.fastjson.JSONObject;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderPayDO;
import com.avatar.hospital.chaperone.response.order.OrderBillPayInfoResponse;
import com.avatar.hospital.chaperone.utils.DelUtils;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 16:28
 **/
public class OrderPayBuilder {
    public static OrderPayDO updatePayInfo(Long id, OrderBillPayInfoResponse payInfo) {
        OrderPayDO orderPayDO = new OrderPayDO();
        orderPayDO.setId(id);
        orderPayDO.setPayInfo(JSONObject.toJSONString(payInfo));
        return orderPayDO;
    }

    public static OrderPayDO updateDel(Long id) {
        OrderPayDO orderPayDO = new OrderPayDO();
        orderPayDO.setId(id);
        orderPayDO.setDeleted(DelUtils.delVersion());
        return orderPayDO;
    }

    public static OrderPayDO updatePayStatus(Long id, Integer status) {
        OrderPayDO orderPayDO = new OrderPayDO();
        orderPayDO.setId(id);
        orderPayDO.setPayStatus(status);
        return orderPayDO;
    }
}
