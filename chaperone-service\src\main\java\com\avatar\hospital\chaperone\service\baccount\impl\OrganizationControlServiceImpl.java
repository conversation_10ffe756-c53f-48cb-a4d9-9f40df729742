package com.avatar.hospital.chaperone.service.baccount.impl;


import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationControlDO;
import com.avatar.hospital.chaperone.database.baccount.repository.OrganizationControlRepositoryService;
import com.avatar.hospital.chaperone.request.baccount.*;
import com.avatar.hospital.chaperone.response.baccount.*;
import com.avatar.hospital.chaperone.service.baccount.OrganizationControlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrganizationControlServiceImpl implements OrganizationControlService {

    private final OrganizationControlRepositoryService organizationControlRepositoryService;

    @Override
    public OrganizationControlResponse getByOrgId(OrganizationControlRequest request) {
        OrganizationControlDO controlDO = organizationControlRepositoryService.findByOrgId(request.getOrgId());
        if (Objects.isNull(controlDO)) {
            return null;
        }
        return OrganizationControlResponse.builder()
                .orderApproveModel(controlDO.getOrderApproveModel())
                .build();
    }
}
