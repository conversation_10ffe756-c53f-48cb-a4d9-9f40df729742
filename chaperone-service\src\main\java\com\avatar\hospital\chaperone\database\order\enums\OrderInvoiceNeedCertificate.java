package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import lombok.Getter;

import java.util.Objects;

/**
 * Description:
 *  是否需要付费凭证
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderInvoiceNeedCertificate implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    NEED(1, "需要"),
    NO_NEED(0, "不需要"),

    ;

    private final Integer status;

    private final String describe;


    OrderInvoiceNeedCertificate(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderInvoiceNeedCertificate of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderInvoiceNeedCertificate itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        OrderInvoiceNeedCertificate e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }
}
