package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderEstimateDO;
import com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsEstimateModel;
import com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsNursingModel;
import com.avatar.hospital.chaperone.database.order.mapper.OrderEstimateMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderEstimateRepositoryService;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单-评价; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class OrderEstimateRepositoryServiceImpl extends ServiceImpl<OrderEstimateMapper, OrderEstimateDO> implements OrderEstimateRepositoryService {

    private final static String STATISTICS_STAR_SQL = "level,count(id) as source";

    @Override
    public OrderEstimateDO getByOrderId(Long orderId, Integer source) {
        LambdaQueryWrapper<OrderEstimateDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderEstimateDO::getOrderId,orderId);
        queryWrapper.eq(OrderEstimateDO::getSource,source);
        List<OrderEstimateDO> list = list(queryWrapper);

        return CollUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public StatisticsEstimateModel statisticsStar(LocalDateTime start, LocalDateTime end) {
        QueryWrapper<OrderEstimateDO> queryWrapper = new QueryWrapper();
        queryWrapper.select(STATISTICS_STAR_SQL)
                        .lambda()
                 .eq(OrderEstimateDO::getDeleted, DelUtils.NO_DELETED)
                .ge(OrderEstimateDO::getCreatedAt,start)
                .lt(OrderEstimateDO::getCreatedAt,end)
                .groupBy(OrderEstimateDO::getLevel);
        Map<Integer,Integer> starNumRef = list(queryWrapper).stream()
                .collect(Collectors.toMap(OrderEstimateDO::getLevel,OrderEstimateDO::getSource));
        StatisticsEstimateModel result = StatisticsEstimateModel.build(starNumRef);
        return result;
    }

    @Override
    public List<StatisticsNursingModel> statisticsNursing(LocalDateTime start, LocalDateTime end) {
        List<StatisticsNursingModel> list = baseMapper.statisticsNursing(start,end);
        return list;
    }

    private LambdaQueryWrapper<OrderEstimateDO> queryWrapper() {
        LambdaQueryWrapper<OrderEstimateDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderEstimateDO::getDeleted, OrderConst.NO_DELETED);
        return queryWrapper;
    }
}
