package com.avatar.hospital.chaperone.template.model;


import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.template.util.StrUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description:
 * Action上下文
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public class BizContext extends Context {

    private final Map<Class<?>, Object> CONTEXT = new ConcurrentHashMap<>();
    private final Map<String, Object> KEY_CONTEXT = new ConcurrentHashMap<>();
    private final Map<Class<?>, Collection> COLLECTION_CONTEXT = new ConcurrentHashMap<>();


    public BizContext(Object... parameters) {
        put(parameters);
    }


    public static BizContext build(Object... parameters) {
        BizContext bizContext = new BizContext();
        bizContext.put(parameters);
        return bizContext;
    }

    public static BizContext buildKey(String key, Object obj) {
        BizContext bizContext = new BizContext();
        bizContext.putKey(key, obj);
        return bizContext;
    }

    public static <T> BizContext buildList(Class<T> aClass, List<T> list) {
        BizContext bizContext = new BizContext();
        bizContext.putList(aClass, list);
        return bizContext;
    }

    public static <T> BizContext buildSet(Class<T> aClass, Set<T> set) {
        BizContext bizContext = new BizContext();
        bizContext.putSet(aClass, set);
        return bizContext;
    }

    public void put(Object... parameters) {
        for (Object parameter : parameters) {
            put(parameter);
        }
    }

    public void put(Object parameter) {
        if (null == parameter) {
            return;
        }
        CONTEXT.put(parameter.getClass(), parameter);
    }

    public void put(Class<?> clazz, Object parameter) {
        CONTEXT.put(clazz, parameter);
    }

    public void putKey(String key, Object parameter) {
        if (null == parameter || !StrUtils.hasText(key)) {
            return;
        }
        KEY_CONTEXT.put(key, parameter);
    }

    public <T> void putList(Class<T> aClass, List<T> list) {
        if (list == null) {
            return;
        }
        COLLECTION_CONTEXT.put(aClass, list);
    }

    @SuppressWarnings("unchecked")
    public <T> List<T> getList(Class<T> aClass) {
        Collection list = COLLECTION_CONTEXT.get(aClass);
        if (list == null) {
            return null;
        }
        return (List<T>) COLLECTION_CONTEXT.get(aClass);
    }

    @SuppressWarnings("unchecked")
    public <T> List<T> getList(Class<T> aClass, List<T> defaultList) {
        Collection collection = COLLECTION_CONTEXT.get(aClass);
        return collection == null ? defaultList : (List<T>) collection;
    }

    public <T> void putSet(Class<T> aClass, Set<T> set) {
        if (set == null) {
            return;
        }
        COLLECTION_CONTEXT.put(aClass, set);
    }

    @SuppressWarnings("unchecked")
    public <T> Set<T> getSet(Class<T> aClass) {
        Collection set = COLLECTION_CONTEXT.get(aClass);
        if (set == null) {
            return null;
        }
        return (Set<T>) set;
    }

    @SuppressWarnings("unchecked")
    public <T> Set<T> getSet(Class<T> aClass, Set<T> defaultList) {
        Collection collection = COLLECTION_CONTEXT.get(aClass);
        return collection == null ? defaultList : (Set<T>) collection;
    }

    @SuppressWarnings("unchecked")
    public <T> T get(Class<T> aClass) {
        Object obj = CONTEXT.get(aClass);
        if (obj == null) {
            return null;
        }
        return (T) obj;
    }

    @SuppressWarnings("unchecked")
    public <R> R get(Class<?> aClass, Class<R> defaultResult) {
        Object obj = CONTEXT.get(aClass);
        return null == obj ? (R) defaultResult : (R) obj;
    }

    public Object getKey(String key) {
        return KEY_CONTEXT.get(key);
    }

    public <T> T getKey(String key, T defaultObject) {
        Object obj = KEY_CONTEXT.get(key);
        return obj == null ? defaultObject : (T) obj;
    }

    public <T> T getKey(String key, Class<T> tClass) {
        Object obj = KEY_CONTEXT.get(key);
        if (obj == null) {
            return null;
        }
        return (T) obj;
    }

    public boolean containsKey(String key) {
        if (StrUtils.hasText(key)) {
            return KEY_CONTEXT.containsKey(key);
        }
        return Boolean.FALSE;
    }

    public boolean containsKey(Class aClass) {
        return aClass == null ? Boolean.FALSE : CONTEXT.containsKey(aClass);
    }

    public boolean collectionContainsKey(Class aClass) {
        return aClass == null ? Boolean.FALSE : COLLECTION_CONTEXT.containsKey(aClass);
    }

    public void clean() {
        CONTEXT.clear();
        KEY_CONTEXT.clear();
        COLLECTION_CONTEXT.clear();
    }

    @Override
    public String toString() {
        return "{'BizContext':{" + "'CONTEXT':" + JSON.toJSONString(CONTEXT) +
                "," + "'KEY_CONTEXT':" + JSON.toJSONString(KEY_CONTEXT) +
                ", 'COLLECTION_CONTEXT':" + JSON.toJSONString(COLLECTION_CONTEXT) +
                "}}";
    }
}
