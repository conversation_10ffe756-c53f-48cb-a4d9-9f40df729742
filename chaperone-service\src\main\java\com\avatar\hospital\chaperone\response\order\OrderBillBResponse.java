package com.avatar.hospital.chaperone.response.order;

import com.avatar.hospital.chaperone.database.order.enums.*;
import com.avatar.hospital.chaperone.template.serialize.BillPayTimeStringSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillBResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 账单类型:参考，bill_type
     * @see OrderBillType
     */
    private Integer billType;

    /**
     * 账单子类型:参考，bill_sub_type
     * @see OrderBillSubType
     */
    private Integer billSubType;

    /**
     * 总共应收款金额，单位:分
     */
    private Integer priceReceivable;

    /**
     * 已收金额，单位:分
     */
    private Integer priceReceived;

    /**
     * 总共应退款金额，单位:分
     */
    private Integer priceRefundable;

    /**
     * 已退款金额，单位:分
     */
    private Integer priceRefunded;

    /**
     * 状态，参考：pay_status
     * @see OrderPayStatus
     */
    private Integer payStatus;

    /**
     * 账单包含套餐名称
     */
    private String itemNames;

    /**
     * 备注,账单说明
     */
    private String remark;

    /**
     * 折扣
     */
    private Integer discount;

    /**
     * 账单支付时间
     */
    @JsonSerialize(using = BillPayTimeStringSerializer.class)
    private String payTime;

    /**
     * 交易类型
     * @see OrderTradeType
     */
    private Integer tradeType;

    /**
     * 账单创建时间
     */
    private String createTime;

    /**
     * 应收/退金额(单位,分 小于0表示需要退)，展示字段
     */
    private Integer price;


    /**
     * 已收/退金额(单位,分 小于0表示需要退)，展示字段
     */
    private Integer priced;

    /**
     * 发票信息
     */
    private OrderInvoiceResponse invoice;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 账单涉及护工名称
     */
    private List<OrderNursingSimpleResponse> nursingList;

    /**
     * 账单开始时间
     * @return
     */
    @JsonSerialize(using = BillPayTimeStringSerializer.class)
    private String startTime;

    /**
     * 账单结束时间
     */
    @JsonSerialize(using = BillPayTimeStringSerializer.class)
    private String endTime;

    public Integer getPrice() {
        Integer price = priceReceivable - priceRefundable;
        return price;
    }

    public Integer getPriced() {
        Integer price = priceReceived - priceRefunded;
        return price;
    }

    public Integer getPayStatus() {
        if (OrderPayStatus.SUCCESS.getStatus().equals(payStatus)
                && getPrice() < 0) {
            // 账单金额如果小于0为退款
            return OrderPayStatus.REFUND_ALL.getStatus();
        }
        return payStatus;
    }
}
