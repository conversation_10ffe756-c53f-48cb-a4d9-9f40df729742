package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderNursingDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单-关联护工信息; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderNursingRepositoryService extends IService<OrderNursingDO> {

    /**
     * 通过订单查询护工ID
     * @param orderId
     * @return
     */
    @Nullable
    List<OrderNursingDO> getByOrderId(Long orderId);

    /**
     * 检测订单护工是否绑定
     * @param orderId
     * @return
     */
    void checkExist(Long orderId);

    /**
     * 添加护工
     * @param oldNursingDOList
     * @param newNursingDOList
     */
    void add(List<OrderNursingDO> oldNursingDOList, List<OrderNursingDO> newNursingDOList);

    /**
     * 当前绑定的护工ID
     * @param orderIdList
     * @return
     */
    Map<Long, Long> findAllByList(List<Long> orderIdList);
}
