package com.avatar.hospital.chaperone.response.order;

import com.avatar.hospital.chaperone.database.order.enums.*;
import com.avatar.hospital.chaperone.template.serialize.BillPayTimeStringSerializer;
import com.avatar.hospital.chaperone.template.serialize.ToIntegerNegativeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 总账单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentBillId;

    /**
     * 账单类型:参考，bill_type
     * @see OrderBillType
     */
    private Integer billType;

    /**
     * 账单子类型:参考，bill_sub_type
     * @see OrderBillSubType
     */
    private Integer billSubType;

    /**
     * 总共应收款金额，单位:分
     */
    private Integer priceReceivable;

    /**
     * 已收金额，单位:分
     */
    private Integer priceReceived;

    /**
     * 总共应退款金额，单位:分
     */
    @JsonSerialize(using = ToIntegerNegativeSerializer.class)
    private Integer priceRefundable;

    /**
     * 已退款金额，单位:分
     */
    private Integer priceRefunded;

    /**
     * 状态，参考：pay_status
     * @see OrderPayStatus
     */
    private Integer payStatus;

    /**
     * 套餐ID列表
     */
    private String itemIds;

    /**
     * 套餐ID名称
     */
    private String itemNames;

    /**
     * 用户是否可见 1 可见 0 不可见
     * @see OrderDisplay
     */
    private Integer display;

    /**
     * 备注,账单说明
     */
    private String remark;

    /**
     * 折扣
     */
    private Integer discount;

    /**
     * 折扣类型 0 不影响已产生费用 1 影响已产生费用
     * @see OrderDiscountType
     */
    private Integer discountType;

    /**
     * 发票信息
     */
    private OrderInvoiceResponse invoice;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 账单涉及护工名称
     */
    private List<OrderNursingSimpleResponse> nursingList;

    /**
     * 账单开始时间
     * @return
     */
    @JsonSerialize(using = BillPayTimeStringSerializer.class)
    private String startTime;

    /**
     * 账单结束时间
     */
    @JsonSerialize(using = BillPayTimeStringSerializer.class)
    private String endTime;
}
