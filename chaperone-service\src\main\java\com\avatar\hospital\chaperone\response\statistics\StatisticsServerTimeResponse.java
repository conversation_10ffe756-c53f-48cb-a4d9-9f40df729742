package com.avatar.hospital.chaperone.response.statistics;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 10:14
 **/
@Data
public class StatisticsServerTimeResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 年度-人天
     */
    private Integer year;

    /**
     * 月度-人天
     */
    private Integer month;

    public static final StatisticsServerTimeResponse build(Integer year,Integer month) {
        StatisticsServerTimeResponse obj = new StatisticsServerTimeResponse();
        obj.setYear(year);
        obj.setMonth(month);
        return obj;
    }
}
