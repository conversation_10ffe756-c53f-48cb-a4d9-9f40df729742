package com.avatar.hospital.chaperone.database.order.mapper;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderConsumerlogDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 订单-消耗明细表(天，套餐); Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderConsumerlogMapper extends BaseMapper<OrderConsumerlogDO> {

    /**
     * 统计指定日期内消耗金额
     * @param orderId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer sumPrice(@Param("orderId") Long orderId,@Param("startDate") Integer startDate,@Param("endDate") Integer endDate);
}
