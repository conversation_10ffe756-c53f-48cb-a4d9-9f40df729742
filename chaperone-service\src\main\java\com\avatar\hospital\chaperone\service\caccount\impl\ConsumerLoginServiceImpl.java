package com.avatar.hospital.chaperone.service.caccount.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.avatar.hospital.chaperone.builder.caccount.ConsumerLoginBuilder;
import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountOpenIdDO;
import com.avatar.hospital.chaperone.database.caccount.repository.AccountOpenIdRepositoryService;
import com.avatar.hospital.chaperone.database.caccount.repository.ConsumerAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountLoginRequest;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountLoginResponse;
import com.avatar.hospital.chaperone.service.caccount.ConsumerLoginService;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ConsumerLoginServiceImpl implements ConsumerLoginService {

    private final WxMaService wxMaService;
    private final ConsumerAccountRepositoryService consumerAccountRepositoryService;
    private final AccountOpenIdRepositoryService accountOpenIdRepositoryService;

    @Override
    public ConsumerAccountLoginResponse login(ConsumerAccountLoginRequest request) {
        try {
            String appId = wxMaService.getWxMaConfig().getAppid();
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(request.getCode());
            Long accountId;
            AccountOpenIdDO accountOpenIdDO = accountOpenIdRepositoryService.findByOpenIdAndAppId(session.getOpenid(), appId);
            if (accountOpenIdDO == null) {
                accountId = consumerAccountRepositoryService.add(ConsumerLoginBuilder.buildAccountDO(new WxMaUserInfo()), ConsumerLoginBuilder.buildConsumerLoginTripartite(session, appId));
                AssertUtils.isNotNull(accountId, ErrorCode.INSERT_ERROR);
            }
            else {
                if (StringUtils.isBlank(accountOpenIdDO.getUnionId())
                        && StringUtils.isNotBlank(session.getUnionid())) {
                    // 如果unionId为空,则更新unionId
                    AccountOpenIdDO updateEntity = new AccountOpenIdDO();
                    updateEntity.setId(accountOpenIdDO.getId());
                    updateEntity.setUnionId(session.getUnionid());
                    accountOpenIdRepositoryService.updateById(updateEntity);
                }
                accountId = accountOpenIdDO.getAccountId();
            }

            // 新增用户数据
            AccountDO accountDO = consumerAccountRepositoryService.findById(accountId);
            if (accountDO == null) {
                // 删除用户关联的openId数据,重新插入
                accountOpenIdRepositoryService.deleteByOpenIdAndAppId(session.getOpenid(), appId);
                accountId = consumerAccountRepositoryService.add(ConsumerLoginBuilder.buildAccountDO(new WxMaUserInfo()), ConsumerLoginBuilder.buildConsumerLoginTripartite(session, appId));
                AssertUtils.isNotNull(accountId, ErrorCode.INSERT_ERROR);
            }
            else {
                // C端用户状态不可用
                AssertUtils.isTrue(accountDO.enable(), ErrorCode.CONSUMER_ACCOUNT_STATUS_NOT_ENABLED);
            }
            // C端用户登录
            StpUtil.login(accountId);
            SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
            return ConsumerAccountLoginResponse.builder().tokenName(tokenInfo.getTokenName()).tokenValue(tokenInfo.getTokenValue()).build();
        } catch (WxErrorException wxErrorException) {
            log.error("ConsumerLoginServiceImpl login error:{}", Throwables.getStackTraceAsString(wxErrorException));
            throw BusinessException.of(ErrorCode.CONSUMER_ACCOUNT_LOGIN_ERROR);
        }
    }

}
