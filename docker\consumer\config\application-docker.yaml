server:
  port: 8080
  servlet:
    context-path: /api/v1/consumer

spring:
  application:
    name: chaperone-consumer
  datasource:
    url: jdbc:mysql://${DB_MYSQL_URL:mysql}:3306/${MYSQL_DATABASE:hospital_chaperone}?useSSL=false&useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${MYSQL_USER:chaperone}
    password: ${MYSQL_PASSWORD:chaperone123}
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      maxActive: 20
      initialSize: 1
      maxWait: 60000
      minIdle: 1
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxOpenPreparedStatements: 20
      connection-init-sqls: set names utf8mb4;

  redis:
    database: 0
    host: ${DB_REDIS_URL:redis}
    port: ${DB_REDIS_PORT:6379}
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

# Sa-Token配置
sa-token:
  token-name: ${spring.application.name}-token
  timeout: 604800
  active-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: true

# Redisson配置
redisson:
  host: ${DB_REDIS_URL:redis}
  port: ${DB_REDIS_PORT:6379}
  database: 0

# UID生成器配置
uid:
  timeBits: 28
  workerBits: 22
  seqBits: 13
  epochStr: "2023-08-01"
  type: standard
  db:
    switch: rds

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# 日志配置
logging:
  level:
    com.avatar.hospital.chaperone: INFO
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/chaperone-consumer.log
    max-size: 100MB
    max-history: 30
