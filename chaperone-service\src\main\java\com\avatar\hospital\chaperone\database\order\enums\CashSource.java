package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * Description:
 *  现金来源
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum CashSource implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    CASH(1, "现金"),
    ALIPAY(3, "支付宝"),
    ;

    private final Integer status;

    private final String describe;


    CashSource(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static CashSource of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (CashSource itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        CashSource e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    public static  final Map<Integer, String> toMap() {
        CashSource[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (CashSource value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

}
