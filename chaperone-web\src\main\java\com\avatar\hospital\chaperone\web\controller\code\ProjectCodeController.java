package com.avatar.hospital.chaperone.web.controller.code;

import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.service.code.ProjectCodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/code")
public class ProjectCodeController {

    private final ProjectCodeService codeService;


    /**
     * code
     */
    @GetMapping
    public String code(Integer codeType) {
        log.info("ProjectCodeController code request:{}", codeType);
        return codeService.generateCode(CodeBizType.of(codeType));
    }


}
