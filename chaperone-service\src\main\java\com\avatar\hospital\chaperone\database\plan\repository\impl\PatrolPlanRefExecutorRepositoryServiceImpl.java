package com.avatar.hospital.chaperone.database.plan.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanRefExecutorDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanRefExecutorDO;
import com.avatar.hospital.chaperone.database.plan.mapper.PatrolPlanRefExecutorMapper;
import com.avatar.hospital.chaperone.database.plan.repository.PatrolPlanRefExecutorRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 巡检计划关联人员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class PatrolPlanRefExecutorRepositoryServiceImpl extends ServiceImpl<PatrolPlanRefExecutorMapper, PatrolPlanRefExecutorDO> implements PatrolPlanRefExecutorRepositoryService {
    @Override
    public boolean update2delete(PlanRequest request) {

        LambdaUpdateWrapper<PatrolPlanRefExecutorDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PatrolPlanRefExecutorDO::getPlanId, request.getId());
        updateWrapper.set(PatrolPlanRefExecutorDO::getUpdateBy, request.getOperator());
        updateWrapper.set(PatrolPlanRefExecutorDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.setSql(" deleted = id");
        update(updateWrapper);
        return true;
    }

    @Override
    public List<Long> getRefIds(Long planId) {

        LambdaQueryWrapper<PatrolPlanRefExecutorDO> queryWrapper = queryWrapper();
        queryWrapper.eq(PatrolPlanRefExecutorDO::getPlanId, planId);
        queryWrapper.eq(PatrolPlanRefExecutorDO::getDeleted, DeletedEnum.NO.getStatus());
        List<PatrolPlanRefExecutorDO> stockApplyRefPartBatchDOS = this.list(queryWrapper);
        return stockApplyRefPartBatchDOS.stream().map(o -> o.getExecutorAccountId()).collect(Collectors.toList());
    }

    @Override
    public Set<Long> getRefIds(Set<Long> planIds) {
        LambdaQueryWrapper<PatrolPlanRefExecutorDO> queryWrapper = queryWrapper();
        queryWrapper.in(PatrolPlanRefExecutorDO::getPlanId, planIds);
        queryWrapper.eq(PatrolPlanRefExecutorDO::getDeleted, DeletedEnum.NO.getStatus());
        List<PatrolPlanRefExecutorDO> refOrgDOS = this.list(queryWrapper);
        return refOrgDOS.stream().map(o -> o.getExecutorAccountId()).collect(Collectors.toSet());

    }

    @Override
    public Set<Long> getPlanByAccount(Long accountId) {
        LambdaQueryWrapper<PatrolPlanRefExecutorDO> queryWrapper = queryWrapper();
        queryWrapper.eq(PlanRefExecutorDO::getDeleted, DeletedEnum.NO.getStatus());
        queryWrapper.orderByDesc(PlanRefExecutorDO::getId);
        queryWrapper.eq(PlanRefExecutorDO::getExecutorAccountId, accountId);

        List<PatrolPlanRefExecutorDO> refExecutorDOS = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(refExecutorDOS)) {
            return Sets.newHashSet();
        }
        return refExecutorDOS.stream().map(PlanRefExecutorDO::getPlanId).collect(Collectors.toSet());
    }

    private LambdaQueryWrapper<PatrolPlanRefExecutorDO> queryWrapper() {
        LambdaQueryWrapper<PatrolPlanRefExecutorDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatrolPlanRefExecutorDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}
