package com.avatar.hospital.chaperone.database.nursing.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum NursingDayStatus {
    LEAVE(0, "请假"),
    FREE(1, "空闲"),
    BUSY(2, "忙碌"),
    ;

    private final Integer status;

    private final String describe;


    NursingDayStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static NursingDayStatus of(Integer status) {
        if (status == null) {
            return FREE;
        }
        for (NursingDayStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return FREE;
    }
}
