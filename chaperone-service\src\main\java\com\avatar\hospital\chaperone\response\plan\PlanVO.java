package com.avatar.hospital.chaperone.response.plan;

import com.avatar.hospital.chaperone.response.BaseVO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 巡检计划
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter

public class PlanVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 8799883649857530114L;
    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 巡检周期类型（1-天，2-月）
     *
     * @see com.avatar.hospital.chaperone.database.plan.enums.CircleType
     */
    private Integer circleType;

    /**
     * 巡检周期
     */
    private Integer circle;

    /**
     * 状态（1-生效，2-作废）
     *
     * @see com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属院区ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;
    /**
     * 所属院区名称
     */
    private String orgName;
    /**
     * 巡检设备
     */
    private List<Value> devices;
    /**
     * 关联人员
     */
    private List<Value> executors;
    /**
     * 关联部门
     */
    private List<Value> departments;

    @Data
    public static class Value implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;
        private String name;

        public Value(Long id, String name) {
            this.id = id;
            this.name = name;
        }
    }

    public static List<Value> map2List(Map<Long, String> map) {
        List<Value> values = Lists.newArrayList();
        for (Map.Entry<Long, String> entry : map.entrySet()) {
            Value value = new Value(entry.getKey(), entry.getValue());
            values.add(value);
        }
        return values;
    }
}
