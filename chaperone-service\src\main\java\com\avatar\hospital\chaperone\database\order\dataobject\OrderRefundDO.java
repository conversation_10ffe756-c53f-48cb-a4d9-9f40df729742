package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderRefundStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单-退款记录;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_order_refund")
public class OrderRefundDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 订单ID
     */
      private Long orderId;

      /**
     * 账单ID
     */
      private Long billId;

      /**
     * 交易单号(非必填)
     */
      private Long payId;

      /**
     * 交易类型，参考：trade_type
       * @see com.avatar.hospital.chaperone.database.order.enums.OrderTradeType
     */
      private Integer tradeType;

      /**
     * 三方退款订单号(微信)
     */
      private String outTradeNo;

      /**
     * 退款金额，单位分
     */
      private Integer price;

      /**
     * 退款原因
     */
      private String refundMsg;

      /**
     * 退款状态，参考：refund_status
       * @see OrderRefundStatus
     */
      private Integer refundStatus;

      /**
     * 退款结果
     */
      private String refundResult;

      /**
     * 退款请求时间，格式yyyy-MM-dd HH:mm:ss
     */
      private String refundRequestDate;

      /**
     * 退款成功事件，格式yyyy-MM-dd HH:mm:ss
     */
      private String refundSuccessDate;


}
