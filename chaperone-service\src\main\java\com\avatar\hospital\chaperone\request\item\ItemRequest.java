package com.avatar.hospital.chaperone.request.item;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 15:47
 **/
@Data
public class ItemRequest implements OperatorReq,Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    private Long itemId;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
