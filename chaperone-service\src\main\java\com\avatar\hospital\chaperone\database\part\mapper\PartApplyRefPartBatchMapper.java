package com.avatar.hospital.chaperone.database.part.mapper;

import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartStatisticsModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 备件使用申请单关联备件批次 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PartApplyRefPartBatchMapper extends BaseMapper<PartApplyRefPartBatchDO> {

    List<PartStatisticsModel> getStatistics(@Param("start") LocalDateTime start,
                                            @Param("end") LocalDateTime end);
}
