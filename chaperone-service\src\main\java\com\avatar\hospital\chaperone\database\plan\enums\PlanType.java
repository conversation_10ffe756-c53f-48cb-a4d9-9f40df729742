package com.avatar.hospital.chaperone.database.plan.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/26 19:55
 */
@Getter
public enum PlanType {
    NONE(-1, "未知"),
    PATROL(1, "巡检"),
    MAINTENANCE(2, "维保"),
    ;
    private final Integer code;

    private final String describe;


    PlanType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static PlanType of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (PlanType type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
