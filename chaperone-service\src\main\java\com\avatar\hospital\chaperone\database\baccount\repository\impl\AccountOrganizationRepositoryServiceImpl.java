package com.avatar.hospital.chaperone.database.baccount.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountOrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.mapper.AccountOrganizationMapper;
import com.avatar.hospital.chaperone.database.baccount.repository.AccountOrganizationRepositoryService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * B端用户和组织机构关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Service
public class AccountOrganizationRepositoryServiceImpl extends ServiceImpl<AccountOrganizationMapper, AccountOrganizationDO> implements AccountOrganizationRepositoryService {

    @Override
    public List<AccountOrganizationDO> findByAccountId(Long accountId) {
        if (accountId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AccountOrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.eq(AccountOrganizationDO::getAccountId, accountId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<AccountOrganizationDO> findByAccountIds(Set<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AccountOrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.in(AccountOrganizationDO::getAccountId, accountIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<AccountOrganizationDO> findByOrganizationIds(Set<Long> organizationIds) {
        if (CollectionUtils.isEmpty(organizationIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AccountOrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.in(AccountOrganizationDO::getOrganizationId, organizationIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public Boolean deleteByOrganizationIds(Set<Long> organizationIds) {
        if (CollectionUtils.isEmpty(organizationIds)) {
            return true;
        }
        LambdaUpdateWrapper<AccountOrganizationDO> updateWrapper = updateWrapper();
        updateWrapper.in(AccountOrganizationDO::getOrganizationId, organizationIds);
        return remove(updateWrapper);
    }

    @Override
    public Boolean deleteByAccountIds(Set<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return true;
        }
        LambdaUpdateWrapper<AccountOrganizationDO> updateWrapper = updateWrapper();
        updateWrapper.in(AccountOrganizationDO::getAccountId, accountIds);
        return remove(updateWrapper);
    }

    private LambdaQueryWrapper<AccountOrganizationDO> queryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    private LambdaUpdateWrapper<AccountOrganizationDO> updateWrapper() {
        return new LambdaUpdateWrapper<>();
    }
}
