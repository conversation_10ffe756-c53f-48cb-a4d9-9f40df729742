package com.avatar.hospital.chaperone.request.part;

import com.avatar.hospital.chaperone.request.ExportPageRequest;
import com.avatar.hospital.chaperone.service.part.dto.PartExportDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 14:14
 */
@Data
public class QueryExportRequest extends ExportPageRequest implements Serializable {
    private static final long serialVersionUID = 884642518906736126L;
    private String code;
    private String name;
    private Long batchId;

    /**
     * 状态（0-待入库，1-可用，2-已使用）
     */
    private Integer status;

    /**
     * 出库时间
     */
    private List<Integer> outTime;

    /**
     * 出库开始时间
     */
    private LocalDateTime outTimeStart;
    /**
     * 出库结束时间
     */
    private LocalDateTime outTimeEnd;

    @Override
    public Class getClazz() {
        return PartExportDTO.class;
    }

    @Override
    public String getFileName() {
        return "备件数量明细";
    }
}
