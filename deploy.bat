@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 医院陪护系统 Docker 部署脚本 (Windows)
REM 作者: 系统管理员
REM 版本: 1.0.0

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 日志函数
:log_info
echo %GREEN%[INFO]%NC% %~1
goto :eof

:log_warn
echo %YELLOW%[WARN]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:log_debug
echo %BLUE%[DEBUG]%NC% %~1
goto :eof

REM 检查依赖
:check_dependencies
call :log_info "检查系统依赖..."

REM 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Docker 未安装，请先安装 Docker Desktop"
    exit /b 1
)

REM 检查Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Docker Compose 未安装，请先安装 Docker Compose"
    exit /b 1
)

REM 检查Maven
mvn --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Maven 未安装，请先安装 Maven"
    exit /b 1
)

call :log_info "依赖检查完成"
goto :eof

REM 构建应用
:build_application
call :log_info "开始构建应用..."

REM 清理并编译
mvn clean package -DskipTests

if errorlevel 1 (
    call :log_error "应用构建失败"
    exit /b 1
) else (
    call :log_info "应用构建成功"
)
goto :eof

REM 构建Docker镜像
:build_images
call :log_info "开始构建 Docker 镜像..."

REM 构建后台管理系统镜像
call :log_debug "构建后台管理系统镜像..."
docker build -f Dockerfile.web -t hospital-chaperone-web:latest .

REM 构建客户端系统镜像
call :log_debug "构建客户端系统镜像..."
docker build -f Dockerfile.consumer -t hospital-chaperone-consumer:latest .

call :log_info "Docker 镜像构建完成"
goto :eof

REM 启动服务
:start_services
call :log_info "启动服务..."

REM 创建网络（如果不存在）
docker network create chaperone-network >nul 2>&1

REM 启动服务
docker-compose up -d

call :log_info "服务启动完成"
goto :eof

REM 检查服务状态
:check_services
call :log_info "检查服务状态..."

REM 等待服务启动
timeout /t 30 /nobreak >nul

REM 检查MySQL
docker-compose ps mysql | findstr "Up" >nul
if errorlevel 1 (
    call :log_error "MySQL 服务启动失败"
) else (
    call :log_info "MySQL 服务运行正常"
)

REM 检查Redis
docker-compose ps redis | findstr "Up" >nul
if errorlevel 1 (
    call :log_error "Redis 服务启动失败"
) else (
    call :log_info "Redis 服务运行正常"
)

REM 检查后台管理系统
curl -f http://localhost:8081/actuator/health >nul 2>&1
if errorlevel 1 (
    call :log_warn "后台管理系统健康检查失败，请检查日志"
) else (
    call :log_info "后台管理系统健康检查通过"
)

REM 检查客户端系统
curl -f http://localhost:8080/actuator/health >nul 2>&1
if errorlevel 1 (
    call :log_warn "客户端系统健康检查失败，请检查日志"
) else (
    call :log_info "客户端系统健康检查通过"
)
goto :eof

REM 显示服务信息
:show_services_info
call :log_info "服务信息:"
echo ==================================
echo 后台管理系统: http://localhost:8081
echo 客户端系统:   http://localhost:8080
echo MySQL:       localhost:3306
echo Redis:       localhost:6379
echo Nginx:       http://localhost:80
echo ==================================
echo.
echo 查看日志命令:
echo docker-compose logs -f [service_name]
echo.
echo 停止服务命令:
echo docker-compose down
goto :eof

REM 主函数
:main
set "action=%~1"
if "%action%"=="" set "action=deploy"

if "%action%"=="build" (
    call :check_dependencies
    call :build_application
    call :build_images
) else if "%action%"=="start" (
    call :start_services
    call :check_services
    call :show_services_info
) else if "%action%"=="deploy" (
    call :check_dependencies
    call :build_application
    call :build_images
    call :start_services
    call :check_services
    call :show_services_info
) else if "%action%"=="stop" (
    call :log_info "停止服务..."
    docker-compose down
    call :log_info "服务已停止"
) else if "%action%"=="restart" (
    call :log_info "重启服务..."
    docker-compose restart
    call :check_services
    call :show_services_info
) else if "%action%"=="logs" (
    docker-compose logs -f %~2
) else if "%action%"=="status" (
    docker-compose ps
) else (
    echo 用法: %0 {deploy^|build^|start^|stop^|restart^|logs^|status}
    echo.
    echo 命令说明:
    echo   deploy  - 完整部署（构建+启动）
    echo   build   - 仅构建应用和镜像
    echo   start   - 仅启动服务
    echo   stop    - 停止服务
    echo   restart - 重启服务
    echo   logs    - 查看日志
    echo   status  - 查看服务状态
    exit /b 1
)

call :log_info "操作完成"
goto :eof

REM 执行主函数
call :main %*
