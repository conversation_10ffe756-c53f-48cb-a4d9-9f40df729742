package com.avatar.hospital.chaperone.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 11:36
 **/
@Slf4j
public class DateUtils {
    public static final String DAY_PATTERN = "yyyy-MM-dd";
    public static final String DAY_INT_PATTERN = "yyyyMMdd";
    public static final String MONTH_INT_PATTERN = "yyyyMM";
    public static final String DAY_HOUR_INT_PATTERN = "yyyyMMddHH";
    public static final String DAY_HOUR_INT_PATTERN_CH = "yyyy年MM月dd日";
    public static final String DAY_HOUR_STR_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DAY_LONG_PATTERN = "yyMMddHHmmss";
    public static final String DAY_LONG_20_PATTERN = "yyyyMMddHHmmss";
    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATETIME_MS_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String DATETIME_T_PATTERN = "yyyy-MM-dd'T'HH:mm:ss";

    public static final Set<Integer> SEASON_MONTH_MAP = new HashSet<>();
    public static final Integer YEAR_LAST_MONTH = 12;
    public static final Long ADD_ONE_DAY = 1L;

    static {
        // 3月 6 月 9 月 12 月
        SEASON_MONTH_MAP.add(3);
        SEASON_MONTH_MAP.add(6);
        SEASON_MONTH_MAP.add(9);
        SEASON_MONTH_MAP.add(12);
    }


    public final static String format(LocalDateTime localDateTime, String pattern) {
        return localDateTime.format(DateTimeFormatter.ofPattern(pattern, Locale.CHINA));
    }

    /**
     * 当前日期时间 格式化
     * yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public final static String dateTimeStr() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATETIME_PATTERN, Locale.CHINA));
    }

    /**
     * 当前日期时间 格式化
     * yyyyMMddHHmmss
     *
     * @return
     */
    public final static String dateTimeStr20() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(DAY_LONG_20_PATTERN, Locale.CHINA));
    }

    /**
     * 当前日期时间 格式化
     * yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public final static String dateTimeStr(String pattern) {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(pattern, Locale.CHINA));
    }

    /**
     * 当前日期时间 格式化
     * yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String dateTimeStr(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ofPattern(DATETIME_PATTERN, Locale.CHINA));
    }

    /**
     * 当前日期时间 格式化
     * yyyyMMdd
     *
     * @return
     */
    public final static Integer dateInt() {
        return dateInt(LocalDate.now());
    }

    /**
     * 当前日期时间 格式化 加1
     * yyyyMMdd
     *
     * @return
     */
    public final static Integer dateIntPlusDay1() {
        LocalDate localDate = LocalDate.now();
        localDate = localDate.plusDays(1);
        return dateInt(localDate);
    }

    /**
     * 当前日期时间 格式化
     * yyyyMMdd
     *
     * @return
     */
    public final static Integer dateInt(LocalDate localDate) {
        return Integer.parseInt(localDate.format(DateTimeFormatter.ofPattern(DAY_INT_PATTERN, Locale.CHINA)));
    }

    /**
     * 当前日期时间 格式化
     * yyyyMMdd
     *
     * @return
     */
    public final static Integer dateHourInt() {
        return Integer.parseInt(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA)));
    }

    /**
     * 当前日期时间 格式化
     * yyyyMMdd
     *
     * @return
     */
    public final static Integer dateInt(LocalDateTime localDate) {
        return Integer.parseInt(localDate.format(DateTimeFormatter.ofPattern(DAY_INT_PATTERN, Locale.CHINA)));
    }


    /**
     * 当前日期时间 格式化
     * yyyyMMdd
     *
     * @return
     */
    public final static Integer dateIntH(LocalDateTime localDate) {
        return Integer.parseInt(localDate.format(DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA)));
    }


    /**
     * 当前日期时间 格式化
     * yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public final static Integer dateInt(Long day) {
        LocalDate localDate = LocalDate.now().plusDays(day);
        return Integer.parseInt(localDate.format(DateTimeFormatter.ofPattern(DAY_INT_PATTERN, Locale.CHINA)));
    }

    /**
     * 当前日期时间 转换
     *
     * @param date yyyyMMdd
     * @return
     */
    public final static Integer dateInt(String date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return Integer.parseInt(date);
    }

    /**
     * 当前日期时间 转换
     *
     * @return
     */
    public final static Long dateLong() {
        String format = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DAY_LONG_PATTERN, Locale.CHINA));
        return Long.parseLong(format);
    }

    public final static Long dateLong(LocalDateTime localDateTime) {
        String format = localDateTime.format(DateTimeFormatter.ofPattern(DAY_LONG_PATTERN, Locale.CHINA));
        return Long.parseLong(format);
    }

    /**
     * 计算卡开始日期到结束日期的间隔天数
     *
     * @param startTime yyyyMMddHH
     * @param endTime   yyyyMMddHH
     * @return
     */
    public static Integer intervalDayInt(Integer startTime, Integer endTime) {
        LocalDate startLocalDate = LocalDate.parse(startTime.toString(), DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA));
        LocalDate endLocalDate = LocalDate.parse(endTime.toString(), DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA));
        Period period = Period.between(endLocalDate, startLocalDate);
        return period.getDays();
    }

    /**
     *
     * 开始时间: 2023110108 - 2023110506
     * 商品计算时间 2点
     * 2023110108 - 2023110202  => 1
     * 2023110202 - 2023110302 => 1
     * 2023110302 - 2023110402 => 1
     * 2023110402 - 2023110502 => 1
     * 2023110502 - 2023110506 => 1
     *
     * 商品计算时间 7点
     * 2023110108 - 2023110207  => 1
     * 2023110207 - 2023110307 => 1
     * 2023110307 - 2023110407 => 1
     * 2023110407 - 2023110506 => 1
     *
     * 商品计算时间 12点
     * 2023110108 - 2023110112 => 1
     * 2023110112 - 2023110212 => 1
     * 2023110212 - 2023110312 => 1
     * 2023110312 - 2023110412 => 1
     * 2023110412 - 2023110506 => 1
     *
     *
     * @param startTime
     * @param endTime
     * @param hour
     * @return
     */
    public static Integer intervalDayInt(Integer startTime, Integer endTime,Integer hour,boolean debug) {
        if (debug) {
            log.info("=================== 开始时间: {} 结束时间: {}, 计费时间点:{}",startTime,endTime,hour);
        }
        LocalDateTime startLocalDate = LocalDateTime.parse(startTime.toString(), DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA));
        LocalDateTime endLocalDate = LocalDateTime.parse(endTime.toString(), DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA));
        Long intervalDay = ChronoUnit.DAYS.between(startLocalDate,endLocalDate);
        Long intervalDaySource = intervalDay;
        int startHour = startLocalDate.getHour();
        int endHour = endLocalDate.getHour();
        if (startHour < hour) {
            if (debug) {
                log.info("开始:" + startLocalDate.getYear() + "-" + startLocalDate.getMonthValue() + "-" + startLocalDate.getDayOfMonth() + " " + startHour + " - "
                        + startLocalDate.getYear() + "-" + startLocalDate.getMonthValue() + "-" + startLocalDate.getDayOfMonth() + " " + hour);
            }
            intervalDay ++;
        } else {
            intervalDay = intervalDay <= 0 ? 1 : intervalDay;
            intervalDaySource = intervalDay;
        }
        if (debug) {
            for (Long i = 0L; i < intervalDaySource; i++) {
                startLocalDate = startLocalDate.plusDays(1);
                log.info("过程时间(结束时间点):" + startLocalDate.getYear() + "-" + startLocalDate.getMonthValue() + "-" + startLocalDate.getDayOfMonth() + " " + hour);
            }
        }
        if (endHour > hour) {
            if (debug) {
                log.info("结束:" + startLocalDate.getYear() + "-" + startLocalDate.getMonthValue() + "-" + startLocalDate.getDayOfMonth() + " " + hour + " - " + startLocalDate.getYear() + "-" + startLocalDate.getMonthValue() + "-" + startLocalDate.getDayOfMonth() + " " + endHour );
            }
            intervalDay ++;
        }
        if (debug) {
            log.info("共计: {}",intervalDay);
        }
        return intervalDay.intValue();
    }

    public static Integer intervalDayInt(Integer startTime, Integer endTime,Integer hour) {
        return intervalDayInt(startTime,endTime,hour,false);
    }

    /**
     * 获取当前小时 0 ~ 23
     *
     * @return
     */
    public static Integer hour() {
        int hour = LocalTime.now().atOffset(ZoneOffset.ofHours(8)).getHour();
        return hour;
    }

    public static LocalDate dataStrToLocalDate(String dataStr, String format) {
        return LocalDate.parse(dataStr, DateTimeFormatter.ofPattern(format));
    }

    /**
     * 是否是本月最后一天
     *
     * @param date yyyyMMdd
     * @return
     */
    public static Boolean lastDayOfMonth(String date) {

        LocalDate localDate = StringUtils.isNotBlank(date) ? LocalDate.parse(date, DateTimeFormatter.ofPattern(DAY_INT_PATTERN, Locale.CHINA)) : LocalDate.now();
        int dayOfMonth = localDate.getDayOfMonth();
        int lastDayOfMonth = localDate.lengthOfMonth();
        return dayOfMonth == lastDayOfMonth;
    }

    public static Boolean lastDayOfMonth() {
        return lastDayOfMonth(null);
    }

    /**
     * 是否是本季节最后一天
     *
     * @param date yyyyMMdd
     * @return
     */
    public static Boolean lastDayOfSeason(String date) {
        LocalDate localDate = StringUtils.isNotBlank(date) ? LocalDate.parse(date, DateTimeFormatter.ofPattern(DAY_INT_PATTERN, Locale.CHINA)) : LocalDate.now();
        int month = localDate.getMonthValue();
        if (SEASON_MONTH_MAP.contains(month)) {
            int dayOfMonth = localDate.getDayOfMonth();
            int lastDayOfMonth = localDate.lengthOfMonth();
            return dayOfMonth == lastDayOfMonth;
        }
        return Boolean.FALSE;
    }

    public static Boolean lastDayOfSeason() {
        return lastDayOfSeason(null);
    }

    public static LocalDate parseForInt(Integer date) {
        return LocalDate.parse(date.toString(), DateTimeFormatter.ofPattern(DAY_INT_PATTERN, Locale.CHINA));
    }

    public static LocalDateTime parseForIntH(Integer dateH) {
        return LocalDateTime.parse(dateH.toString(), DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA));
    }

    public static LocalDateTime parseForIntD(Integer date) {
        LocalDate localDate = LocalDate.parse(date.toString(), DateTimeFormatter.ofPattern(DAY_INT_PATTERN, Locale.CHINA));
        LocalDateTime localDateTime = localDate.atTime(0, 0, 0);
        return localDateTime;
    }

    public static LocalDateTime parseForIntH(Integer dateH,Integer hour) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateH.toString(), DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA));
        localDateTime = localDateTime.withHour(hour);
        return localDateTime;
    }

    /**
     * @param date
     * @return 2023-10-20 11
     */
    public static String parseForStr(Integer date) {
        return LocalDateTime.parse(date.toString(), DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA)).format(DateTimeFormatter.ofPattern(DAY_HOUR_STR_PATTERN, Locale.CHINA));
    }

    public static String convertStr(String str) {
        if (StringUtils.isBlank(str)) {
            return StringUtils.EMPTY;
        }
        String year = str.substring(0, 4);
        String month = str.substring(4, 6);
        String day = str.substring(6, 8);
        String hour = str.substring(8, 10);
        String minute = str.substring(10, 12);
        String sec = str.substring(12, 14);
        return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + sec;
    }

    /**
     * @param date
     * @return 2023-10-20 11
     */
    public static String parseDateForStrH(Integer date) {
        return LocalDateTime.parse(date.toString(), DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA))
                .format(DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN_CH, Locale.CHINA));
    }

    /**
     * 是否是本年最后一天
     *
     * @param date yyyyMMdd
     * @return
     */
    public static Boolean lastDayOfYear(String date) {
        LocalDate localDate = StringUtils.isNotBlank(date) ? LocalDate.parse(date, DateTimeFormatter.ofPattern(DAY_INT_PATTERN, Locale.CHINA)) : LocalDate.now();
        int month = localDate.getMonthValue();
        if (YEAR_LAST_MONTH == month) {
            int dayOfMonth = localDate.getDayOfMonth();
            int lastDayOfMonth = localDate.lengthOfMonth();
            return dayOfMonth == lastDayOfMonth;
        }
        return Boolean.FALSE;
    }

    public static Boolean lastDayOfYear() {
        return lastDayOfYear(null);
    }

    public static LocalDateTime dateStart() {
        LocalDate localDate = LocalDate.now();
        return localDate.atStartOfDay();
    }

    public static LocalDateTime dateStart(LocalDateTime time) {
        LocalDate localDate = time.toLocalDate();
        return localDate.atStartOfDay();
    }

    public static LocalDateTime dateEnd() {
        LocalDateTime localDateTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        return localDateTime;
    }

    public static LocalDateTime dateEnd(LocalDateTime time) {
        LocalDateTime localDateTime = time.withHour(23).withMinute(59).withSecond(59);
        return localDateTime;
    }

    public static LocalDate monthStart() {
        LocalDate localDate = LocalDate.now().withDayOfMonth(1);
        return localDate;
    }

    public static String monthStartStr() {
        return LocalDate.now().withDayOfMonth(1).format(DateTimeFormatter.ofPattern(DAY_INT_PATTERN));
    }

    /**
     * 上月1号
     *
     * @return
     */
    public static Integer lastMonthStart() {
        LocalDate localDate = LocalDate.now().plusMonths(-1).withDayOfMonth(1);
        return dateInt(localDate);
    }

    /**
     * 上月最后一条
     *
     * @return
     */
    public static Integer lastMonthEnd() {
        LocalDate localDate = LocalDate.now().plusMonths(-1);
        int day = localDate.lengthOfMonth();
        localDate = localDate.withDayOfMonth(day);
        return dateInt(localDate);
    }

    /**
     * 下月1号
     *
     * @return
     */
    public static Integer nextMonthStart() {
        LocalDate localDate = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        return dateInt(localDate);
    }

    /**
     * 下月最后一天
     *
     * @return
     */
    public static Integer nextMonthEnd() {
        LocalDate localDate = LocalDate.now().plusMonths(1);
        int day = localDate.lengthOfMonth();
        localDate = localDate.withDayOfMonth(day);
        return dateInt(localDate);
    }

    /**
     * 上季度1号
     *
     * @return
     */
    public static Integer lastSeasonStart() {
        LocalDate localDate = LocalDate.now().plusMonths(-4).withDayOfMonth(1);
        return dateInt(localDate);
    }

    /**
     * 上季度最后一天
     *
     * @return
     */
    public static Integer lastSeasonEnd() {
        LocalDate localDate = LocalDate.now().plusMonths(-1);
        int day = localDate.lengthOfMonth();
        localDate = localDate.withDayOfMonth(day);
        return dateInt(localDate);
    }

    /**
     * 上年1月1号
     *
     * @return
     */
    public static Integer lastYearStart() {
        LocalDate localDate = LocalDate.now().plusYears(-1).withMonth(1).withDayOfMonth(1);
        return dateInt(localDate);
    }

    /**
     * 上年12月最后一天
     *
     * @return
     */
    public static Integer lastYearEnd() {
        LocalDate localDate = LocalDate.now().plusYears(-1).withMonth(12).withDayOfMonth(31);
        return dateInt(localDate);
    }

    /**
     * 获取时间间隔内所有日期
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<Integer> getAllDate(Integer startDate, Integer endDate) {
        LocalDate sd = LocalDate.parse(startDate.toString(), DateTimeFormatter.ofPattern(DAY_INT_PATTERN));
        LocalDate ed = LocalDate.parse(endDate.toString(), DateTimeFormatter.ofPattern(DAY_INT_PATTERN));
        List<Integer> list = new LinkedList<>();
        while (sd.compareTo(ed) <= 0) {
            list.add(dateInt(sd));
            sd = sd.plusDays(1);
        }
        return list;
    }


    /**
     * 添加小时 开始 00
     *
     * @param date
     * @return
     */
    public static Integer toDateHourStart(Integer date) {
        return date * 100;
    }

    /**
     * 添加小时 开始 23
     *
     * @param date
     * @return
     */
    public static Integer toDateHourEnd(Integer date) {
        return date * 100 + 23;
    }

    /**
     * riqi
     *
     * @param date      yyyyMMdd
     * @param startDate yyyyMMddHH
     * @param endDate   yyyyMMddHH
     * @return
     */
    public static boolean between(Integer date, Integer startDate, Integer endDate) {
        startDate = startDate / 100;
        endDate = endDate / 100;
        LocalDate sd = LocalDate.parse(startDate.toString(), DateTimeFormatter.ofPattern(DAY_INT_PATTERN));
        LocalDate ed = LocalDate.parse(endDate.toString(), DateTimeFormatter.ofPattern(DAY_INT_PATTERN));
        LocalDate d = LocalDate.parse(date.toString(), DateTimeFormatter.ofPattern(DAY_INT_PATTERN));
        // d >= 开始时间 && d <= 结束时间
        return d.compareTo(sd) >= 0 && d.compareTo(ed) <= 0;
    }

    /**
     * 如果当前时间在25号之后(包括) 则时间范围截止时间到下一个月末尾
     * 如果当前时间在25号(不包括) 则时间范围截止时间到本一个月末尾
     *
     * @return
     */
    public static Integer[] dateStartEnd(Integer dateH) {
        LocalDate localDate = LocalDate.now();
        if (Objects.nonNull(dateH)) {
            Integer date = dateH / 100;
            localDate = parseForInt(date);
        }
        Integer startDate = dateInt(localDate);
        if (localDate.getDayOfMonth() >= 25) {
            localDate = localDate.plusMonths(1);
        }
        int day = localDate.lengthOfMonth();
        localDate = localDate.withDayOfMonth(day);
        Integer endDate = dateInt(localDate);
        return new Integer[]{startDate, endDate};
    }

    public static Integer[] dateStartEnd() {
        return dateStartEnd(null);
    }


    /**
     * 把日期和小时拼接在一起
     *
     * @param date yyyyMMdd
     * @param hour HH
     * @return
     */
    public static Integer toDateHour(Integer date, Integer hour) {
        return date * 100 + hour;
    }

    /**
     * 转换时间戳
     *
     * @param dateTime
     * @return
     */
    public static String toEpochMilliStr(Integer dateTime) {
        Long milli = LocalDateTime.parse(dateTime.toString(), DateTimeFormatter.ofPattern(DateUtils.DAY_HOUR_INT_PATTERN, Locale.CHINA)).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        return milli.toString();
    }

    /**
     * 转换时间戳
     *
     * @param mil 时间戳
     * @return yyyyHHddHH
     */
    public static Integer toDateTimeHForMilliStr(String mil) {
        Instant instant = Instant.ofEpochMilli(Long.valueOf(mil));
        ZoneId zone = ZoneOffset.ofHours(8);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        int dateTimeH = Integer.parseInt(localDateTime.format(DateTimeFormatter.ofPattern(DAY_HOUR_INT_PATTERN, Locale.CHINA)));
        return dateTimeH;
    }

    /**
     * 转换时间戳
     *
     * @param mil 时间戳
     * @return yyyyHHddHH
     */
    public static String toDateHForMilliStr(String mil) {
        Instant instant = Instant.ofEpochMilli(Long.valueOf(mil));
        ZoneId zone = ZoneOffset.ofHours(8);
        LocalDateTime localDate = LocalDateTime.ofInstant(instant, zone);
        return localDate.format(DateTimeFormatter.ofPattern(DAY_PATTERN, Locale.CHINA));
    }

    /**
     * 转换 LocalDateTime
     *
     * @param mil 时间戳
     * @return yyyyHHddHH
     */
    public static LocalDateTime toLocalDateTimeForMilliStr(Long mil) {
        Instant instant = Instant.ofEpochMilli(mil);
        ZoneId zone = ZoneOffset.ofHours(8);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime;
    }

    /**
     * 转换 当前时间LocalDateTime
     *
     * @return yyyyHHddHH
     */
    public static LocalDateTime toLocalDateTimeHNow() {
        LocalDateTime now = LocalDateTime.now()
                .atOffset(ZoneOffset.ofHours(8))
                .withMinute(0)
                .withSecond(0)
                .withNano(0)
                .toLocalDateTime();
        return now;
    }

    /**
     * 转换时间戳
     *
     * @param mil 时间戳
     * @return yyyy-HH-dd HH:mm:ss
     */
    public static String toDateTimeForMilliStr(Long mil) {
        Instant instant = Instant.ofEpochMilli(mil);
        ZoneId zone = ZoneOffset.ofHours(8);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.format(DateTimeFormatter.ofPattern(DAY_HOUR_STR_PATTERN, Locale.CHINA));
    }

    public static Long getSecond(LocalDateTime now, LocalDateTime createdAt) {
        ZoneId zone = ZoneId.systemDefault();
        long nowL = now.atZone(zone).toEpochSecond();
        long createL = createdAt.atZone(zone).toEpochSecond();
        return nowL - createL;
    }

    public static void main(String[] args) {
        // 套餐是18点 开始时间 和 结束时间在者中间
       /* Integer integer = intervalDayInt(2023110819, 2023110915, 18,true);
        intervalDayInt(2023110819, 2023110915, 0,true);
        System.out.printf("");*/
       /* LocalDateTime now = LocalDateTime.now();
        LocalDateTime plus = now.plusDays(-1);
        Long second = getSecond(now, plus);*/
/*
        Integer[] integers = dateStartEnd(2023112512);
        LocalDateTime localDateTime = parseForIntH(2023120600);
        long milli = localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        boolean between = between(20231112, 2023111211, 2023111212);*/
        String s = toyyyyMMddHHmmssStr(20240102);
        String s1 = toyyyyMMddHHmmssStr(2024010201);
        System.out.printf("");
    }
    /**
     * 获取一年的开始和结束时间
     *  不传为当前
     * @param year
     * @return
     */
    public static LocalDateTime[] getStartEndOfYear(Integer year) {
        if (Objects.isNull(year)) {
            year = LocalDateTime.now().getYear();
        }
        LocalDateTime start = LocalDateTime.of(year, 1, 1, 0, 0, 0);
        LocalDateTime end = LocalDateTime.of(year, 12, 31, 23, 59, 59);
        return new LocalDateTime[]{start,end};
    }
    /**
     * 获取月的开始和结束时间
     *  不传为当前
     * @param m
     * @return
     */
    public static LocalDateTime[] getStartEndOfMonth(Integer m) {
        LocalDateTime now = LocalDateTime.now();
        Integer year = now.getYear();
        Integer month = now.getMonthValue();
        if (Objects.nonNull(m)) {
            year = m / 100;
            month = m % 100;
        }

        LocalDateTime start = LocalDateTime.of(year, month, 1, 0, 0, 0);
        LocalDateTime end = LocalDateTime.of(LocalDate.from(start.with(TemporalAdjusters.lastDayOfMonth())), LocalTime.MAX);
        return new LocalDateTime[]{start,end};
    }

    public static List<LocalDateTime> toLocalDateTimeList(List<String> createTime) {
        List<LocalDateTime> result = new ArrayList<>(2);
        result.add(null);
        result.add(null);
        if (CollUtils.isEmpty(createTime)) {
            return result;
        }
        result.set(0, LocalDateTime.parse(createTime.get(0), DateTimeFormatter.ofPattern(DAY_HOUR_STR_PATTERN, Locale.CHINA)));
        result.set(1, LocalDateTime.parse(createTime.get(1), DateTimeFormatter.ofPattern(DAY_HOUR_STR_PATTERN, Locale.CHINA)));
        return result;
    }

    /**
     * 获取大的值
     * @param date1
     * @param date2
     * @return
     */
    public static String max(Integer date1, Integer date2) {
        Long date1Long = toyyyyMMddHHmmss(date1);
        Long date2Long = toyyyyMMddHHmmss(date2);

        return date1Long > date2Long ? date1Long.toString() : date2Long.toString();
    }

    /**
     * 获取大的值
     * @param date1
     * @param date2
     * @return
     */
    public static String min(Integer date1, Integer date2) {
        Long date1Long = toyyyyMMddHHmmss(date1);
        Long date2Long = toyyyyMMddHHmmss(date2);

        return date1Long > date2Long ? date2Long.toString() : date1Long.toString();
    }


    public static String toyyyyMMddHHmmssStr(Integer date)  {
        Long date1Long = toyyyyMMddHHmmss(date);

        return date1Long.toString();
    }



    /**
     * 统一格式
     * @param date
     * @return
     */
    public static Long toyyyyMMddHHmmss(Integer date) {
        if (Objects.isNull(date)) {
            return 0L;
        }
        String dataStr = date.toString();
        if (dataStr.length() == "yyyyMMdd".length()) {
            dataStr = dataStr + "000000";
            return Long.valueOf(dataStr);
        }
        if (dataStr.length() == "yyyyMMddHH".length()) {
            dataStr = dataStr + "0000";
            return Long.valueOf(dataStr);
        }
        if (dataStr.length() == "yyyyMMddHHmm".length()) {
            dataStr = dataStr + "00";
            return Long.valueOf(dataStr);
        }
        if (dataStr.length() == "yyyyMMddHHmmss".length()) {
            return Long.valueOf(dataStr);
        }
        return 0L;
    }
}
