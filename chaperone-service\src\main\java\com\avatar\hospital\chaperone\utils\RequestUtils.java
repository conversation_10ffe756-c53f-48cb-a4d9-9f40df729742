package com.avatar.hospital.chaperone.utils;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/12
 */
@Slf4j
public class RequestUtils {

    private static final String X_REAL_IP = "X-Real-IP";
    private static final String PROXY_CLIENT_IP = "Proxy-Client-IP";
    private static final String WL_PROXY_CLIENT_IP = "WL-Proxy-Client-IP";
    private static final String X_FORWARDED_FOR = "x-forwarded-for";
    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_ADDRESS = "127.0.0.1";
    private static final String COMMA = ",";
    private static final Integer FIFTEEN_NUMBER = 15;

    /**
     * 获取当前请求的IP地址
     *
     * @return -
     */
    public static String getRequestHost() {
        String ipAddress = null;
        try {
            HttpServletRequest request = getRequest();
            if (request == null) {
                return null;
            }
            ipAddress = request.getHeader(X_FORWARDED_FOR);
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader(PROXY_CLIENT_IP);
            }
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader(WL_PROXY_CLIENT_IP);
            }
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader(X_REAL_IP);
            }
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();
                if (LOCALHOST_ADDRESS.equals(ipAddress)) {
                    try {
                        ipAddress = InetAddress.getLocalHost().getHostAddress();
                    } catch (UnknownHostException e) {
                        log.error("RequestUtils getRequestHost getHostAddress error:{}", Throwables.getStackTraceAsString(e));
                    }
                }
            }
            if (ipAddress != null && ipAddress.length() > FIFTEEN_NUMBER) {
                if (ipAddress.indexOf(COMMA) > 0) {
                    ipAddress = ipAddress.substring(0, ipAddress.indexOf(COMMA));
                }
            }
        } catch (Exception e) {
            log.error("RequestUtils getRequestHost error:{}", Throwables.getStackTraceAsString(e));
        }
        return ipAddress;
    }


    /**
     * 获取当前HttpServletRequest对象
     *
     * @return request
     */
    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        if (requestAttributes instanceof ServletRequestAttributes) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;
    }

}
