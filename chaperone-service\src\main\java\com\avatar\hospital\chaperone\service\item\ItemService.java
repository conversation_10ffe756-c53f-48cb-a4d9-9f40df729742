package com.avatar.hospital.chaperone.service.item;

import com.avatar.hospital.chaperone.request.item.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.item.ItemCResponse;
import com.avatar.hospital.chaperone.response.item.ItemIdResponse;
import com.avatar.hospital.chaperone.response.item.ItemResponse;

import java.util.List;
import java.util.Map;

/**
 * @program: hospital-chaperone
 * @description: 套餐接口
 * @author: sp0372
 * @create: 2023-10-11 17:18
 **/
public interface ItemService {
    // c 端
    /**
     * C端 套餐分页接口
     *   用户下单选择套餐
     */
    PageResponse<ItemCResponse> pagingToC(ItemCPageRequest request);

    /**
     * C端 套餐查询详情接口
     */
    ItemCResponse getById(ItemRequest request);

    // B端

    /**
     * 套餐 创建接口
     */
    ItemIdResponse create(ItemCreateRequest request);


    /**
     * 套餐 更新接口
     */
    ItemIdResponse modify(ItemModifyRequest request);

    /**
     * C端 套餐查询详情接口
     */
    ItemResponse detail(ItemRequest request);

    /**
     * 分页查询
     *   B端
     * @param request
     * @return
     */
    PageResponse<ItemResponse> paging(ItemPageRequest request);

    /**
     * 套餐上架接口
     */
    ItemResponse on(ItemRequest request);


    /**
     * 套餐下架接口
     */
    ItemResponse off(ItemRequest request);


    /**
     * 套餐外显接口
     */
    ItemResponse onDisplay(ItemRequest request);


    /**
     * 套餐关闭外显接口
     */
    ItemResponse offDisplay(ItemRequest request);

    /**
     * 根据商品ID列表查询所有商品信息
     * @param itemIdList
     * @return
     */
    List<ItemResponse> listByIds(List<Long> itemIdList);

    /**
     * 根据商品ID列表查询所有商品信息
     * @param itemIdList
     * @return <Id,name>
     */
    Map<Long,String> mapByIds(List<Long> itemIdList);

    /**
     *
     * @param hour 时间节点
     * @return
     */
    List<ItemResponse> findAll(Integer hour);
}
