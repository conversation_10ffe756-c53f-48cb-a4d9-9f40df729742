package com.avatar.hospital.chaperone.database.part.repository;

import com.avatar.hospital.chaperone.database.part.dataobject.PartDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 备品备件明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PartRepositoryService extends IService<PartDO> {

    /**
     * 获取几条数据
     * @param batchId 批次ID
     * @param quantity
     * @return
     */
    List<PartDO> listN(Long batchId ,Integer quantity);
}
