package com.avatar.hospital.chaperone.service.plan.impl;

import com.avatar.hospital.chaperone.builder.plan.PlanBuilder;
import com.avatar.hospital.chaperone.component.baccount.OrganizationComponent;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanDO;
import com.avatar.hospital.chaperone.database.plan.enums.CircleType;
import com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRefDeviceRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRefExecutorRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRefOrgRepositoryAdaptor;
import com.avatar.hospital.chaperone.database.plan.repository.adaptor.PlanRepositoryAdaptor;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.plan.PlanTaskVO;
import com.avatar.hospital.chaperone.response.plan.PlanVO;
import com.avatar.hospital.chaperone.service.baccount.WebAccountService;
import com.avatar.hospital.chaperone.service.device.DeviceService;
import com.avatar.hospital.chaperone.service.plan.PlanService;
import com.avatar.hospital.chaperone.service.plan.PlanTaskService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.avatar.hospital.chaperone.enums.ErrorCode.PROJECT_PLAN_NOT_EXIST;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 10:48
 */
@Service("PlanService")
public class PlanServiceImpl implements PlanService {

    @Autowired
    PlanRepositoryAdaptor planRepositoryService;
    @Autowired
    PlanRefDeviceRepositoryAdaptor refDeviceRepositoryService;
    @Autowired
    PlanRefOrgRepositoryAdaptor refOrgRepositoryService;
    @Autowired
    PlanRefExecutorRepositoryAdaptor refExecutorRepositoryService;
    @Autowired
    @Qualifier("PlanTaskService")
    PlanTaskService planTaskService;

    @Autowired
    OrganizationComponent organizationComponent;
    @Autowired
    WebAccountService accountService;
    @Autowired
    DeviceService deviceService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long create(PlanRequest request) {

        PlanDO planDO = PlanBuilder.build(request);

        planRepositoryService.save(request.getPlanType(), planDO);

        request.setId(planDO.getId());

        if (CollectionUtils.isNotEmpty(request.getDeviceIds())) {
            refDeviceRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefDevice(request));
        }
        if (CollectionUtils.isNotEmpty(request.getExecutorIds())) {
            refExecutorRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefExecutor(request));
        }

        if (CollectionUtils.isNotEmpty(request.getOrgIds())) {
            refOrgRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefOrg(request));
        }

        return planDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean update(PlanRequest request) {

        PlanDO planDO = planRepositoryService.getById(request.getPlanType(), request.getId());
        AssertUtils.isNotNull(planDO, PROJECT_PLAN_NOT_EXIST);
        planDO.setUpdatedAt(LocalDateTime.now());
        planDO.setUpdateBy(request.getOperator());

        planRepositoryService.update(request);

        if (CollectionUtils.isNotEmpty(request.getDeviceIds())) {
            refDeviceRepositoryService.update2delete(request);
            refDeviceRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefDevice(request));
        }
        if (CollectionUtils.isNotEmpty(request.getExecutorIds())) {
            refExecutorRepositoryService.update2delete(request);
            refExecutorRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefExecutor(request));
        }

        if (Objects.nonNull(request.getOrgIds())) {
            refOrgRepositoryService.update2delete(request);
            refOrgRepositoryService.saveBatch(request.getPlanType(), PlanBuilder.buildRefOrg(request));
        }

        return true;
    }

    @Override
    public PageResponse<PlanVO> paging(QueryRequest request) {
        Page<PlanDO> page = request.ofPage();
        LambdaQueryWrapper<PlanDO> queryWrapper = planRepositoryService.queryWrapper(request.getPlanType());
        queryWrapper.orderByDesc(PlanDO::getId);
        queryWrapper.eq(Objects.nonNull(request.getCode()), PlanDO::getCode, request.getCode());
        queryWrapper.like(Objects.nonNull(request.getName()), PlanDO::getName, request.getName());
        queryWrapper.orderByDesc(PlanDO::getCreatedAt);

        page = planRepositoryService.page(request.getPlanType(), page, queryWrapper);
        PageResponse<PlanVO> pageResponse = PageResponse.build(page, PlanBuilder::build);
        setName(request.getPlanType(), pageResponse.getRecords());
        return pageResponse;
    }

    private void setName(PlanType planType, List<PlanVO> planVOS) {

        if (CollectionUtils.isEmpty(planVOS)) {
            return;
        }
        Set<Long> planIds = planVOS.stream().map(PlanVO::getId).collect(Collectors.toSet());
        Set<Long> belongOrgIds = planVOS.stream().map(PlanVO::getOrgId).collect(Collectors.toSet());


        Set<Long> orgIds = refOrgRepositoryService.getRefIds(planType, planIds);
        if (CollectionUtils.isNotEmpty(orgIds)) {
            belongOrgIds.addAll(orgIds);
        }
        Map<Long, String> orgMap = organizationComponent.findOrgMap(belongOrgIds);

        Set<Long> accountIds = refExecutorRepositoryService.getRefIds(planType, planIds);
        Map<Long, String> accountMap = accountService.getAccountMap(accountIds);

        Set<Long> deviceIds = refDeviceRepositoryService.getRefIds(planType, planIds);
        Map<Long, String> deviceMap = deviceService.findDeviceMap(deviceIds);
        planVOS.stream().forEach(o -> {
            o.setOrgName(orgMap.get(o.getOrgId()));
            o.setDevices(PlanVO.map2List(getSubMap(deviceMap, refDeviceRepositoryService.getRefIds(planType, o.getId()))));
            o.setExecutors(PlanVO.map2List(getSubMap(accountMap, refExecutorRepositoryService.getRefIds(planType, o.getId()))));
            o.setDepartments(PlanVO.map2List(getSubMap(orgMap, refOrgRepositoryService.getRefIds(planType, o.getId()))));
        });
    }

    private Map<Long, String> getSubMap(Map<Long, String> map, Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids) || Objects.isNull(map)) {
            return Maps.newHashMap();
        }
        Map<Long, String> subMap = Maps.newHashMap();
        for (Long id : ids) {
            subMap.put(id, map.get(id));
        }
        return subMap;
    }

    @Override
    public PlanVO detail(PlanType planType, Long id) {
        PlanVO planVO = PlanBuilder.build(planRepositoryService.getById(planType, id));
        if (Objects.isNull(planVO)) {
            return null;
        }
        setName(planType, Lists.newArrayList(planVO));
        return planVO;
    }

    /**
     * 执行计划
     */

    @Override
    public void execute(PlanType planType) {
        LambdaQueryWrapper<PlanDO> queryWrapper = planRepositoryService.queryWrapper(planType);
        queryWrapper.eq(PlanDO::getStatus, PlanStatusType.VALID.getCode());
        List<PlanDO> PlanDOS = planRepositoryService.list(planType, queryWrapper);

        for (PlanDO planDO : PlanDOS) {

            List<PlanTaskVO> taskVOS = planTaskService.getPlanBeforeTodayTask(planType, planDO.getId());

            if (!checkIsExpired(taskVOS, planDO)) {
                continue;
            }
            //1.未完成的任务改为已过期
            expiringTask(planType, taskVOS);
            //2.根据周期生成新的任务
            createTask(planType, planDO);
        }

    }

    private boolean checkIsExpired(List<PlanTaskVO> taskVOS, PlanDO planDO) {
        LocalDateTime planCreateTime = planDO.getCreatedAt();
        //判断是否已到第二天
        boolean isSecondDay = LocalDate.now().compareTo(planCreateTime.toLocalDate()) > 0;
        if (!isSecondDay) {
            return false;
        }
        if (CollectionUtils.isEmpty(taskVOS)) {
            return true;
        }

        LocalDate taskCreatedAt = taskVOS.get(0).getCreatedAt().toLocalDate();
        LocalDate taskDueTime = LocalDate.now();
        switch (CircleType.of(planDO.getCircleType())) {
            case DAY:
                taskDueTime = taskCreatedAt.plusDays(planDO.getCircle());
                break;
            case MONTH:
                taskDueTime = taskCreatedAt.plusMonths(planDO.getCircle());
                break;
        }
        boolean isDue = LocalDate.now().compareTo(taskDueTime) == 0;

        return isDue;
    }


    private void createTask(PlanType planType, PlanDO planDO) {
        List<Long> deviceIds = refDeviceRepositoryService.getRefIds(planType, planDO.getId());
        planTaskService.create(planType, planDO, deviceIds);
    }

    private void expiringTask(PlanType planType, List<PlanTaskVO> taskVOS) {
        if (CollectionUtils.isEmpty(taskVOS)) {
            return;
        }
        List<Long> taskIds = taskVOS.stream().map(PlanTaskVO::getId).collect(Collectors.toList());
        planTaskService.expired(planType, taskIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean abandon(PlanRequest request) {
        PlanDO planDO = planRepositoryService.getById(request.getPlanType(), request.getId());
        AssertUtils.isNotNull(planDO, PROJECT_PLAN_NOT_EXIST);

        planRepositoryService.abandon(request);
        planTaskService.update2delete(request);
        return true;
    }
}
