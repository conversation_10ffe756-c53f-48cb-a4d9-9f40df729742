package com.avatar.hospital.chaperone.request.repair;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:48
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormPageRequest extends PageRequest implements OperatorReq {

    /**
     * 编号
     */
    private String code;

    /**
     * 报修系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他,7-暖通系统，8-建筑系统，9-消防系统）
     */
    private Integer systemType;

    /**
     * 状态（1-未指派，2-已指派，3-未完成，4-待审核，5-已完成）
     *
     * @see com.avatar.hospital.chaperone.database.repair.enums.RepairFormStatus
     */
    private Integer status;

    /**
     * 操作用户
     */
    private Operator operatorUser;
    /**
     * @see com.avatar.hospital.chaperone.database.repair.enums.RepairFormBizType
     */
    private Integer bizType;
    /**
     * 关联id
     */
    private Set<Long> bizIds;
}
