package com.avatar.hospital.chaperone.annotation;

import com.avatar.hospital.chaperone.enums.ErrorCode;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface Idempotent {

    /**
     * key
     *
     * @return
     */
    String value();

    /**
     * 过期时间 默认3秒钟
     * @return
     */
    long expire() default 3;

    /**
     * 等待时间
     *
     * @return
     */
    long waitTime() default 5;

    /**
     * 提示信息
     * @return
     */
    ErrorCode errorCode() default ErrorCode.INSERT_ERROR;
}
