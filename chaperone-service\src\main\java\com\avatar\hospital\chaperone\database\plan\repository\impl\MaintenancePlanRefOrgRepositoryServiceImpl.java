package com.avatar.hospital.chaperone.database.plan.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanRefOrgDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanRefOrgDO;
import com.avatar.hospital.chaperone.database.plan.mapper.MaintenancePlanRefOrgMapper;
import com.avatar.hospital.chaperone.database.plan.repository.MaintenancePlanRefOrgRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 维保计划关联部门 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class MaintenancePlanRefOrgRepositoryServiceImpl extends ServiceImpl<MaintenancePlanRefOrgMapper, MaintenancePlanRefOrgDO> implements MaintenancePlanRefOrgRepositoryService {

    @Override
    public boolean update2delete(PlanRequest request) {

        LambdaUpdateWrapper<MaintenancePlanRefOrgDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MaintenancePlanRefOrgDO::getPlanId, request.getId());
        updateWrapper.set(MaintenancePlanRefOrgDO::getUpdateBy, request.getOperator());
        updateWrapper.set(MaintenancePlanRefOrgDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.setSql(" deleted = id");
        update(updateWrapper);
        return true;
    }

    @Override
    public List<Long> getRefIds(Long planId) {

        LambdaQueryWrapper<MaintenancePlanRefOrgDO> queryWrapper = queryWrapper();
        queryWrapper.eq(MaintenancePlanRefOrgDO::getPlanId, planId);
        queryWrapper.eq(MaintenancePlanRefOrgDO::getDeleted, DeletedEnum.NO.getStatus());
        List<MaintenancePlanRefOrgDO> refOrgDOS = this.list(queryWrapper);
        return refOrgDOS.stream().map(o -> o.getOrgId()).collect(Collectors.toList());
    }

    @Override
    public Set<Long> getRefIds(Set<Long> planIds) {
        LambdaQueryWrapper<MaintenancePlanRefOrgDO> queryWrapper = queryWrapper();
        queryWrapper.in(MaintenancePlanRefOrgDO::getPlanId, planIds);
        queryWrapper.eq(MaintenancePlanRefOrgDO::getDeleted, DeletedEnum.NO.getStatus());
        List<MaintenancePlanRefOrgDO> refOrgDOS = this.list(queryWrapper);
        return refOrgDOS.stream().map(o -> o.getOrgId()).collect(Collectors.toSet());

    }

    @Override
    public Set<Long> getPlanByOrg(Set<Long> orgIds) {
        LambdaQueryWrapper<MaintenancePlanRefOrgDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanRefOrgDO::getDeleted, DeletedEnum.NO.getStatus());
        queryWrapper.orderByDesc(PlanRefOrgDO::getId);
        queryWrapper.in(PlanRefOrgDO::getOrgId, orgIds);

        List<MaintenancePlanRefOrgDO> refOrgDOS = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(refOrgDOS)) {
            return Sets.newHashSet();
        }
        return refOrgDOS.stream().map(PlanRefOrgDO::getPlanId).collect(Collectors.toSet());
    }

    private LambdaQueryWrapper<MaintenancePlanRefOrgDO> queryWrapper() {
        LambdaQueryWrapper<MaintenancePlanRefOrgDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaintenancePlanRefOrgDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}

