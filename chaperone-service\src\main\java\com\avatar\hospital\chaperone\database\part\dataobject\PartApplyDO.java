package com.avatar.hospital.chaperone.database.part.dataobject;

import com.avatar.hospital.chaperone.database.part.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.part.enums.PartApplyStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 备件使用申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_spare_part_apply")
public class PartApplyDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 审批单编号
     */
    private String code;

    /**
     * 状态（1-待审核，2-允许使用，3-不允许使用）
     * @see PartApplyStatus
     */
    private Integer status;

    /**
     * 报修单反馈ID
     */
    private Long repairFormFeedbackId;
    /**
     * 报修单Code
     */
    private Long repairFormId;

    /**
     * 反馈情况说明
     */
    private String remark;

    /**
     * 审核说明
     */
    private String auditRemark;

    /**
     * 审核完成时间
     */
    private LocalDateTime auditCompletedTime;

    /**
     * 审核人员ID
     */
    private Long auditAccountId;


}
