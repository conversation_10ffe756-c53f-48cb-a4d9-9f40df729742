package com.avatar.hospital.chaperone.response.order;

import com.avatar.hospital.chaperone.database.order.dto.OrderConsumerlogExtraNursingInfoDTO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderConsumerLogResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 日期,格式yyyyMMdd
     */
    private Integer date;

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 关联套餐
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemId;

    /**
     * 关联套餐名称
     */
    private String itemName;

    /**
     * 总金额：分
     */
    private Integer totalPrice;

    /**
     * 物业分成比例
     */
    private Integer rateCertifiedProperty;

    /**
     * 物业金额
     */
    private Integer priceCertifiedProperty;

    /**
     * 医院分成比例
     */
    private Integer rateHospital;

    /**
     * 医院金额
     */
    private Integer priceHospital;

    /**
     * 护工分成比例
     */
    private Integer rateNursing;

    /**
     * 护工金额
     */
    private Integer priceNursing;

    /**
     * 版本， 有效版本 1 其他无效版本
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long version;

    /**
     * 护工ID
     */
    @Deprecated
    @JsonSerialize(using = ToStringSerializer.class)
    private Long nursingId;

    /**
     * 护工信息
     */
    List<OrderConsumerlogExtraNursingInfoDTO.DAT> nursingList;

}
