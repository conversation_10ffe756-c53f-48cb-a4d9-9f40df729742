package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderInvoiceDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.avatar.hospital.chaperone.database.order.mapper.OrderInvoiceMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderInvoiceRepositoryService;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 订单-发票; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class OrderInvoiceRepositoryServiceImpl extends ServiceImpl<OrderInvoiceMapper, OrderInvoiceDO> implements OrderInvoiceRepositoryService {

    @Override
    public Long add(OrderInvoiceDO invoice) {
        LambdaQueryWrapper<OrderInvoiceDO> query = queryWrapper();
        query.eq(OrderInvoiceDO::getBillId,invoice.getBillId());
        List<OrderInvoiceDO> list = list(query);
        OrderInvoiceDO dbOrderInvoiceDO  = CollUtils.isEmpty(list) ? null: list.get(0);
        Long id  = CollUtils.isEmpty(list) ? IdUtils.getId() : list.get(0).getId();
        invoice.setId(id);
        if (CollUtils.isEmpty(list)) {
            save(invoice);
        } else {
            invoice.setCreateBy(dbOrderInvoiceDO.getCreateBy());
            invoice.setCreatedAt(dbOrderInvoiceDO.getCreatedAt());
            updateById(invoice);
        }
        return id;
    }

    private LambdaQueryWrapper<OrderInvoiceDO> queryWrapper() {
        LambdaQueryWrapper<OrderInvoiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderInvoiceDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
