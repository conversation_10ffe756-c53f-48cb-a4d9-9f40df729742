package com.avatar.hospital.chaperone.service.code;

import com.avatar.hospital.chaperone.database.code.dataobject.ProjectCodeDO;
import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.database.code.repository.ProjectCodeRepositoryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 11:08
 */
@Component
public class ProjectCodeService {
    @Autowired
    ProjectCodeRepositoryService projectCodeRepositoryService;

    public String generateCode(CodeBizType codeBizType) {

        return projectCodeRepositoryService.generateCode(codeBizType);
    }

    public String generateCodePrefix(CodeBizType codeBizType) {
        LambdaQueryWrapper<ProjectCodeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectCodeDO::getBizType, codeBizType.getCode());
        return projectCodeRepositoryService.getOne(queryWrapper).getPrefix();
    }
}
