package com.avatar.hospital.chaperone.service.order.dto;

import com.avatar.hospital.chaperone.database.item.enums.ItemServerType;
import com.avatar.hospital.chaperone.database.nursing.enums.Gender;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderBillDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderNursingDO;
import com.avatar.hospital.chaperone.database.order.enums.*;
import com.avatar.hospital.chaperone.request.order.OrderBillModifyTradeRequest;
import com.avatar.hospital.chaperone.request.order.OrderCreateRequest;
import com.avatar.hospital.chaperone.request.order.OrderModifyDiscountRequest;
import com.avatar.hospital.chaperone.request.order.OrderModifyRateRequest;
import com.avatar.hospital.chaperone.response.baccount.OrganizationControlResponse;
import com.avatar.hospital.chaperone.response.item.ItemResponse;
import com.avatar.hospital.chaperone.response.order.OrderPriceCalculationResponse;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.avatar.hospital.chaperone.utils.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 订单同步实体
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-16 15:01
 **/
@Data
public class OrderChangeLogDTO {

    @Data
    public static abstract class AbsCompareDTO implements CompareDTO, Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 陪护单C端用户ID,后端取登录用户ID
         */
        @CompareAlias(ignore = true)
        private Long orderId;

        /**
         * 空对象标志
         */
        @CompareAlias(ignore = true)
        private Boolean empty = Boolean.FALSE;

        public void setEmptyTrue() {
            empty = Boolean.TRUE;
        }

        @Override
        public Boolean getEmpty() {
            return empty;
        }
    }

    /**
     * 创建
     */
    @Data
    public static class Create extends AbsCompareDTO {
        /**
         * 服务类型, 0 其他 1 住院陪护
         * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
         */
        @CompareAlias(value = "服务类型",clazz = ItemServerType.class)
        private Integer serverType;

        /**
         * 病人姓名
         */
        @CompareAlias(value = "病人姓名")
        private String patientName;

        /**
         * 病人性别,-1未知 0 女 1 男
         * @see Gender
         */
        @CompareAlias(value = "病人性别")
        private Integer patientGender;

        /**
         * 病人出生日期 yyyy-MM-dd
         */
        @CompareAlias(value = "病人出生日期")
        private String patientBirthday;

        /**
         * 家属联系电话
         */
        @CompareAlias(value = "家属联系电话")
        private String phone;

        /**
         * 病人病情描述
         */
        @CompareAlias(value = "病人病情描述")
        private String patientStateDesc;

        /**
         * 特殊要求
         */
        @CompareAlias(value = "特殊要求")
        private String specificDesc;

        /**
         * 机构ID(医院)
         */
        @CompareAlias(value = "机构ID")
        private Long orgId;

        /**
         * 科室
         */
        @CompareAlias(value = "科室")
        private String departments;

        /**
         * 床号
         */
        @CompareAlias(value = "床号")
        private String bedNo;

        /**
         * 详细地址
         */
        @CompareAlias(value = "详细地址")
        private String address;

        /**
         * 陪护开始日期，格式：yyyy-MM-dd
         */
        @CompareAlias(value = "陪护开始日期")
        private String startTime;

        /**
         * 陪护终止日期，格式：yyyy-MM-dd
         */
        @CompareAlias(value = "陪护终止日期")
        private String endTime;

        /**
         * 备注
         */
        @CompareAlias(value = "陪护单备注")
        private String remark;

        /**
         * 套餐ID列表
         */
        @CompareAlias(value = "套餐列表")
        private String itemIdList;


        /**
         * 陪护单状态,参考：order_status
         * @see com.avatar.hospital.chaperone.database.order.enums.OrderStatus
         */
        @CompareAlias(value = "陪护单状态",clazz = OrderStatus.class)
        private Integer orderStatus;

    }


    /**
     * 创建
     */
    @Data
    public static class Modify extends AbsCompareDTO {
        /**
         * 服务类型, 0 其他 1 住院陪护
         * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
         */
        @CompareAlias(value = "服务类型",clazz = ItemServerType.class)
        private Integer serverType;

        /**
         * 病人姓名
         */
        @CompareAlias(value = "病人姓名")
        private String patientName;

        /**
         * 病人性别,-1未知 0 女 1 男
         * @see Gender
         */
        @CompareAlias(value = "病人性别")
        private Integer patientGender;

        /**
         * 病人出生日期 yyyy-MM-dd
         */
        @CompareAlias(value = "病人出生日期")
        private String patientBirthday;

        /**
         * 家属联系电话
         */
        @CompareAlias(value = "家属联系电话")
        private String phone;

        /**
         * 病人病情描述
         */
        @CompareAlias(value = "病人病情描述")
        private String patientStateDesc;

        /**
         * 特殊要求
         */
        @CompareAlias(value = "特殊要求")
        private String specificDesc;

        /**
         * 机构ID(医院)
         */
        @CompareAlias(value = "机构ID")
        private Long orgId;

        /**
         * 科室
         */
        @CompareAlias(value = "科室")
        private String departments;

        /**
         * 床号
         */
        @CompareAlias(value = "床号")
        private String bedNo;

        /**
         * 详细地址
         */
        @CompareAlias(value = "详细地址")
        private String address;

        /**
         * 陪护开始日期，格式：yyyy-MM-dd
         */
        @CompareAlias(value = "陪护开始日期")
        private String startTime;

        /**
         * 陪护终止日期，格式：yyyy-MM-dd
         */
        @CompareAlias(value = "陪护终止日期")
        private String endTime;

        /**
         * 备注
         */
        @CompareAlias(value = "陪护单备注")
        private String remark;

    }

    @Data
    public static class Cancel extends AbsCompareDTO {

        /**
         * 陪护单状态,参考：order_status
         * @see com.avatar.hospital.chaperone.database.order.enums.OrderStatus
         */
        @CompareAlias(value = "陪护单状态",clazz = OrderStatus.class)
        private Integer orderStatus;

    }

    /**
     * 确认
     */
    @Data
    public static class Confirm extends AbsCompareDTO {

        /**
         * 陪护单状态,参考：order_status
         * @see com.avatar.hospital.chaperone.database.order.enums.OrderStatus
         */
        @CompareAlias(value = "陪护单状态",clazz = OrderStatus.class)
        private Integer orderStatus;

    }

    /**
     * 申请结算
     */
    @Data
    public static class ApplySettle extends AbsCompareDTO {

        /**
         * 陪护单状态,参考：order_apply_status
         * @see com.avatar.hospital.chaperone.database.order.enums.OrderStatus
         */
        @CompareAlias(value = "陪护单状态",clazz = OrderStatus.class)
        private Integer orderStatus;

    }

    @Data
    public static class ModifyDiscount extends AbsCompareDTO {

        /**
         * 折扣(99折)
         */
        @CompareAlias(value = "订单折扣",force = true)
        private String discount;

        /**
         * 折扣类型 0 不影响已产生费用 1 影响已产生费用
         * @see com.avatar.hospital.chaperone.database.order.enums.OrderDiscountType
         */
        @CompareAlias(value = "订单折扣更新方案",clazz = OrderDiscountType.class,force = true)
        private Integer discountType;

    }


    @Data
    public static class ModifyRate extends AbsCompareDTO {

        /**
         * 物业比例
         */
        @CompareAlias(value = "物业比例",force = true)
        private String rateCertifiedProperty;

        /**
         * 医院比例
         */
        @CompareAlias(value = "医院比例",force = true)
        private String rateHospital;

        /**
         * 护工比例
         */
        @CompareAlias(value = "护工比例",force = true)
        private String rateNursing;


        /**
         * 类型 0 不影响已产生费用 1 影响已产生费用
         */
        @CompareAlias(value = "费率更新方案",clazz = OrderDiscountType.class,force = true)
        private Integer rateType;
    }

    @Data
    public static class ModifyNursing extends AbsCompareDTO {

        @CompareAlias(value = "护工",force = true)
        private String nursingInfo;

        @CompareAlias(value = "生效时间",force = true)
        private String time;

    }

    @Data
    public static class ModifyItem extends AbsCompareDTO {

        @CompareAlias(value = "套餐预估总额",force = true)
        private String price;

        /**
         * 套餐ID列表
         */
        @CompareAlias(value = "套餐列表",force = true)
        private String itemIdList;

        /**
         * 陪护开始日期，格式：yyyy-MM-dd HH:mm:ss
         */
        @CompareAlias(value = "陪护开始日期",ignore = true)
        private String startTime;

        /**
         * 陪护终止日期，格式：yyyy-MM-dd HH:mm:ss
         */
        @CompareAlias(value = "陪护终止日期",ignore = true)
        private String endTime;
    }

    @Data
    public static class ModifyStatus extends AbsCompareDTO {

        @CompareAlias(value = "订单状态",force = true,clazz = OrderStatus.class)
        private Integer orderStatus;

    }

    @Data
    public static class ModifyBillInfo extends AbsCompareDTO {

        @CompareAlias(value = "账单类型",clazz = OrderBillType.class,force = true)
        private Integer billType;

        @CompareAlias(value = "账单子类型",clazz = OrderBillSubType.class,force = true)
        private Integer billSubType;

        @CompareAlias(value = "预付单金额",force = true)
        private String price;

    }

    @Data
    public static class ModifyBillCashPay extends AbsCompareDTO {

        @CompareAlias(value = "账单ID",force = true)
        private Long billId;

        @CompareAlias(value = "账单支付状态",force = true,clazz = OrderPayStatus.class)
        private Integer payStatus;
    }

    @Data
    public static class ModifySettle extends AbsCompareDTO {

        @CompareAlias(value = "订单状态",force = true,clazz = OrderStatus.class)
        private Integer orderStatus;

    }

    @Data
    public static class RejectSettleApply extends AbsCompareDTO {

        @CompareAlias(value = "订单状态",force = true,clazz = OrderStatus.class)
        private Integer orderStatus;

    }

    @Data
    public static class ItemData {
        /**
         * 套餐名称
         */
        private String name;
        /**
         * 价格
         */
        private Integer price;
        /**
         * 数量
         */
        private Integer number;
        public static final OrderChangeLogDTO.ItemData build(String name, Integer price, Integer number) {
            OrderChangeLogDTO.ItemData obj = new OrderChangeLogDTO.ItemData();
            obj.setName(name);
            obj.setPrice(price);
            obj.setNumber(number);
            return obj;
        }

        public static String toStr(List<ItemData> list) {
            StringBuilder sb = new StringBuilder();
            for (ItemData itemData : list) {
                sb.append(itemData.name)
                        .append("(")
                        .append(PriceUtil.fenToYuan(itemData.price))
                        .append("*")
                        .append(itemData.number)
                        .append(");");
            }
            return sb.toString();
        }
    }

    // -- create

    public final static OrderChangeLogDTO.Create buildByCreate() {
        OrderChangeLogDTO.Create logDTO = new OrderChangeLogDTO.Create();
        logDTO.setEmptyTrue();
        return logDTO;
    }

    public static OrderChangeLogDTO.Create buildByCreate(Long orderId,OrderCreateRequest request,
                                                         OrderPriceCalculationResponse calculationResponse,
                                                         OrganizationControlResponse organizationControl) {
        OrderStatus orderStatus = OrderStatus.initStatusByOrgControl(organizationControl);

        OrderChangeLogDTO.Create orderChangeLogDTO = new OrderChangeLogDTO.Create();
        orderChangeLogDTO.setOrderId(orderId);
        orderChangeLogDTO.setServerType(request.getServerType());
        orderChangeLogDTO.setPatientName(request.getPatientName());
        orderChangeLogDTO.setPatientGender(request.getPatientGender());
        orderChangeLogDTO.setPatientBirthday(request.getPatientBirthday());
        orderChangeLogDTO.setPhone(request.getPhone());
        orderChangeLogDTO.setPatientStateDesc(request.getPatientStateDesc());
        orderChangeLogDTO.setSpecificDesc(request.getSpecificDesc());
        orderChangeLogDTO.setOrgId(request.getOrgId());
        orderChangeLogDTO.setDepartments(request.getDepartments());
        orderChangeLogDTO.setBedNo(request.getBedNo());
        orderChangeLogDTO.setAddress(request.getAddress());
        orderChangeLogDTO.setStartTime(DateUtils.parseForStr(request.getStartTime()));
        orderChangeLogDTO.setEndTime(DateUtils.parseForStr(request.getEndTime()));
        orderChangeLogDTO.setRemark(request.getRemark());
        orderChangeLogDTO.setItemIdList(calculationResponse.toItemStr());
        orderChangeLogDTO.setOrderStatus(orderStatus.getStatus());
        return orderChangeLogDTO;
    }

    // -- modify
    public static Modify buildByModify(OrderDO order) {
        Modify orderChangeLogDTO = new Modify();
        orderChangeLogDTO.setOrderId(order.getId());
        orderChangeLogDTO.setServerType(order.getServerType());
        orderChangeLogDTO.setPatientName(order.getPatientName());
        orderChangeLogDTO.setPatientGender(order.getPatientGender());
        orderChangeLogDTO.setPatientBirthday(order.getPatientBirthday());
        orderChangeLogDTO.setPhone(order.getPhone());
        orderChangeLogDTO.setPatientStateDesc(order.getPatientStateDesc());
        orderChangeLogDTO.setSpecificDesc(order.getSpecificDesc());
        orderChangeLogDTO.setOrgId(order.getOrgId());
        orderChangeLogDTO.setDepartments(order.getDepartments());
        orderChangeLogDTO.setBedNo(order.getBedNo());
        orderChangeLogDTO.setAddress(order.getAddress());
        orderChangeLogDTO.setStartTime(DateUtils.parseForStr(order.getStartTime()));
        orderChangeLogDTO.setEndTime(DateUtils.parseForStr(order.getEndTime()));
        orderChangeLogDTO.setRemark(order.getRemark());
        return orderChangeLogDTO;
    }

    // cancel

    /**
     * 订单取消时候创建
     * @return
     */
    public static OrderChangeLogDTO.Cancel buildByCancel(OrderDO orderDO) {
        OrderChangeLogDTO.Cancel logDTO = new OrderChangeLogDTO.Cancel();
        logDTO.setOrderId(orderDO.getId());
        logDTO.setOrderStatus(orderDO.getOrderStatus());
        return logDTO;
    }

    /**
     * 订单取消时候创建
     * @return
     */
    public static OrderChangeLogDTO.Cancel buildByCancel(Long orderId) {
        OrderChangeLogDTO.Cancel logDTO = new OrderChangeLogDTO.Cancel();
        logDTO.setOrderId(orderId);
        logDTO.setOrderStatus(OrderStatus.CANCEL.getStatus());
        return logDTO;
    }

    // confirm

    /**
     * 订单确认时候创建
     * @return
     */
    public static OrderChangeLogDTO.Confirm buildByConfirm(OrderDO orderDO) {
        OrderChangeLogDTO.Confirm logDTO = new OrderChangeLogDTO.Confirm();
        logDTO.setOrderId(orderDO.getId());
        logDTO.setOrderStatus(orderDO.getOrderStatus());
        return logDTO;
    }


    // settle

    /**
     * 订单确认时候创建
     * @return
     */
    public static ApplySettle buildBySettle(OrderDO orderDO) {
        ApplySettle logDTO = new ApplySettle();
        logDTO.setOrderId(orderDO.getId());
        logDTO.setOrderStatus(orderDO.getOrderStatus());
        return logDTO;
    }

    /**
     * 订单确认时候创建
     * @return
     */
    public static ApplySettle buildBySettle(Long orderId) {
        ApplySettle logDTO = new ApplySettle();
        logDTO.setOrderId(orderId);
        logDTO.setOrderStatus(OrderStatus.APPLY_SETTLE.getStatus());
        return logDTO;
    }

    // modify discount

    public static OrderChangeLogDTO.ModifyDiscount buildByModifyDiscount(OrderDO orderDO) {
        OrderChangeLogDTO.ModifyDiscount logDTO = new OrderChangeLogDTO.ModifyDiscount();
        logDTO.setOrderId(orderDO.getId());
        logDTO.setDiscount(PriceUtil.fenToYuan(orderDO.getDiscount()) + "折");
        return logDTO;
    }

    public static OrderChangeLogDTO.ModifyDiscount buildByModifyDiscount(OrderModifyDiscountRequest request) {
        OrderChangeLogDTO.ModifyDiscount logDTO = new OrderChangeLogDTO.ModifyDiscount();
        logDTO.setOrderId(request.getOrderId());
        logDTO.setDiscount(PriceUtil.fenToYuan(request.getDiscount()) + "折");
        logDTO.setDiscountType(request.getType());
        return logDTO;
    }

    // modify rate

    public static OrderChangeLogDTO.ModifyRate buildByModifyRate(OrderDO orderDO) {
        OrderChangeLogDTO.ModifyRate logDTO = new OrderChangeLogDTO.ModifyRate();
        if (Objects.isNull(orderDO)) {
            logDTO.setEmptyTrue();
            return logDTO;
        }
        logDTO.setOrderId(orderDO.getId());
        logDTO.setRateCertifiedProperty(PriceUtil.fenToYuan(orderDO.getRateCertifiedProperty()) + "%");
        logDTO.setRateHospital(PriceUtil.fenToYuan(orderDO.getRateHospital()) + "%");
        logDTO.setRateNursing(PriceUtil.fenToYuan(orderDO.getRateNursing()) + "%");
        return logDTO;
    }

    public static OrderChangeLogDTO.ModifyRate buildByModifyRate(OrderModifyRateRequest request) {
        OrderChangeLogDTO.ModifyRate logDTO = new OrderChangeLogDTO.ModifyRate();
        logDTO.setOrderId(request.getOrderId());
        logDTO.setRateCertifiedProperty(PriceUtil.fenToYuan(request.getRateCertifiedProperty()) + "%");
        logDTO.setRateHospital(PriceUtil.fenToYuan(request.getRateHospital()) + "%");
        logDTO.setRateNursing(PriceUtil.fenToYuan(request.getRateNursing()) + "%");
        logDTO.setRateType(request.getType());
        return logDTO;
    }

    // modify nursing

    public static final String toStrForOrderNursingDOList(List<OrderNursingDO> list) {
        if (CollUtils.isEmpty(list)) {
            return StrUtils.EMPTY;
        }
        StringBuilder sb = new StringBuilder();
        for (OrderNursingDO nursingDO : list) {
            sb.append(nursingDO.getNursingName())
                    .append("(").append(nursingDO.getNursingId()).append(")")
                    .append(",");
        }
        return sb.toString();
    }

    public static OrderChangeLogDTO.ModifyNursing buildByModifyNursingBefore(Long orderId,List<OrderNursingDO> oldNursingDOList) {
        OrderChangeLogDTO.ModifyNursing logDTO = new OrderChangeLogDTO.ModifyNursing();
        if(CollUtils.isEmpty(oldNursingDOList)) {
            logDTO.setEmptyTrue();
            return logDTO;
        }

        logDTO.setOrderId(orderId);
        logDTO.setNursingInfo(toStrForOrderNursingDOList(oldNursingDOList));
        logDTO.setTime("当前生效");
        return logDTO;
    }



    public static OrderChangeLogDTO.ModifyNursing buildByModifyNursingAfter(Long orderId,List<OrderNursingDO>  nursingDO,Long time) {
        OrderChangeLogDTO.ModifyNursing logDTO = new OrderChangeLogDTO.ModifyNursing();
        if(Objects.isNull(nursingDO)) {
            logDTO.setEmptyTrue();
            return logDTO;
        }
        String txt = "立即生效";
        if (Objects.equals(-1,time)) {
            txt = "清除预设值";
        } else if (Objects.equals(0,time)) {
            txt = "立即生效";
        } else if (!Objects.equals(0,time)) {
            txt = DateUtils.toDateTimeForMilliStr(time);
        }

        logDTO.setOrderId(orderId);
        logDTO.setNursingInfo(toStrForOrderNursingDOList(nursingDO));
        logDTO.setTime(txt);
        return logDTO;
    }


    // modify item

    public static OrderChangeLogDTO.ModifyItem buildByModifyItem(OrderDO orderDO,
                                                                 OrderBillDO totalBill,
                                                                 List<OrderItemDO> newList,
                                                                 Map<Long, ItemResponse> itemMap) {
        List<ItemData> itemData = newList.stream()
                //.filter(item -> Objects.equals(DelUtils.NO_DELETED,item.getDeleted()))
                .map(item -> {
                    ItemResponse itemResponse = itemMap.get(item.getItemId());
                    String name = Objects.isNull(itemResponse) ? "" : itemResponse.getName();
                    return ItemData.build(name, item.getPrice(), item.getNumber());
                })
                .collect(Collectors.toList());

        OrderChangeLogDTO.ModifyItem logDTO = new OrderChangeLogDTO.ModifyItem();
        logDTO.setOrderId(orderDO.getId());
        logDTO.setStartTime(DateUtils.parseForStr(orderDO.getStartTime()));
        logDTO.setEndTime(DateUtils.parseForStr(orderDO.getEndTime()));
        logDTO.setPrice(PriceUtil.fenToYuan(totalBill.getPriceReceivable()));
        logDTO.setItemIdList(ItemData.toStr(itemData));
        return logDTO;
    }


    // modify status

    public static OrderChangeLogDTO.ModifyStatus buildByModifyStatus(OrderDO orderDO) {
        OrderChangeLogDTO.ModifyStatus logDTO = new OrderChangeLogDTO.ModifyStatus();
        logDTO.setOrderId(orderDO.getId());
        logDTO.setOrderStatus(orderDO.getOrderStatus());
        return logDTO;
    }



    // modify bill info

    public static OrderChangeLogDTO.ModifyBillInfo buildByModifyBillInfoBefore(OrderBillDO billDO,OrderBillDO repayBill) {
        OrderChangeLogDTO.ModifyBillInfo logDTO = new OrderChangeLogDTO.ModifyBillInfo();
        logDTO.setBillType(billDO.getBillType());
        logDTO.setBillSubType(billDO.getBillSubType());
        logDTO.setPrice("0");
        if(Objects.nonNull(repayBill)) {
            logDTO.setPrice(PriceUtil.fenToYuan(repayBill.getPriceReceivable()));
        }
        logDTO.setOrderId(billDO.getId());
        return logDTO;
    }

    public static OrderChangeLogDTO.ModifyBillInfo buildByModifyBillInfoAfter(OrderBillModifyTradeRequest request) {
        OrderChangeLogDTO.ModifyBillInfo logDTO = new OrderChangeLogDTO.ModifyBillInfo();
        logDTO.setBillType(request.getBillType());
        logDTO.setBillSubType(request.getBillSubType());
        logDTO.setPrice(PriceUtil.fenToYuan(request.getPrice()));
        logDTO.setOrderId(request.getOrderId());
        return logDTO;
    }


    // modify bill info

    public static OrderChangeLogDTO.ModifyBillCashPay buildByModifyBillCashPayBefore(Long orderId,Long billId) {
        OrderChangeLogDTO.ModifyBillCashPay logDTO = new OrderChangeLogDTO.ModifyBillCashPay();
        logDTO.setBillId(billId);
        logDTO.setPayStatus(OrderPayStatus.UNPAY.getStatus());
        logDTO.setOrderId(orderId);
        return logDTO;
    }

    public static OrderChangeLogDTO.ModifyBillCashPay buildByModifyBillCashPayAfter(Long orderId,Long billId) {
        OrderChangeLogDTO.ModifyBillCashPay logDTO = new OrderChangeLogDTO.ModifyBillCashPay();
        logDTO.setBillId(billId);
        logDTO.setPayStatus(OrderPayStatus.SUCCESS.getStatus());
        logDTO.setOrderId(orderId);
        return logDTO;
    }

    // commit settle

    public static OrderChangeLogDTO.ModifySettle buildByModifySettleBefore(Long orderId,Integer orderStatus) {
        OrderChangeLogDTO.ModifySettle logDTO = new OrderChangeLogDTO.ModifySettle();
        logDTO.setOrderStatus(orderStatus);
        logDTO.setOrderId(orderId);
        return logDTO;
    }

    public static OrderChangeLogDTO.ModifySettle buildByModifySettleAfter(Long orderId) {
        OrderChangeLogDTO.ModifySettle logDTO = new OrderChangeLogDTO.ModifySettle();
        logDTO.setOrderStatus(OrderStatus.FINISH.getStatus());
        logDTO.setOrderId(orderId);
        return logDTO;
    }


    // reject settle apply

    public static OrderChangeLogDTO.RejectSettleApply buildByRejectSettleApplyBefore(Long orderId,Integer orderStatus) {
        OrderChangeLogDTO.RejectSettleApply logDTO = new OrderChangeLogDTO.RejectSettleApply();
        logDTO.setOrderStatus(orderStatus);
        logDTO.setOrderId(orderId);
        return logDTO;
    }

    public static OrderChangeLogDTO.RejectSettleApply buildByRejectSettleApplyAfter(Long orderId) {
        OrderChangeLogDTO.RejectSettleApply logDTO = new OrderChangeLogDTO.RejectSettleApply();
        logDTO.setOrderStatus(OrderStatus.WORK.getStatus());
        logDTO.setOrderId(orderId);
        return logDTO;
    }
}
