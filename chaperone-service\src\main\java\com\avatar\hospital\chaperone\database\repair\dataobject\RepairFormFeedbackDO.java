package com.avatar.hospital.chaperone.database.repair.dataobject;

import com.avatar.hospital.chaperone.database.repair.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormFeedbackType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 报修单反馈
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_repair_form_feedback")
public class RepairFormFeedbackDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 备注
     */
    private String remark;

    /**
     * 反馈结果类型（1-完成任务,2-需协助,3-需要使用备件）
     * @see RepairFormFeedbackType
     */
    private Integer feedbackType;

    /**
     * 附件URL,数组
     */
    private String attachments;

    /**
     * 报修单ID
     */
    private Long repairFormId;


    /**
     * 创建人名称(冗余字段)
     */
    private String createByName;
}
