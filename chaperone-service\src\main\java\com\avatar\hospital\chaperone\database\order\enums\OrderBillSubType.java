package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * Description:
 *  账单子类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderBillSubType implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    NORMAL(0, "子账单默认类型"),
    MONTH(11, "月度"),
    SEASON(12, "季度"),
    YEAR(13, "年度"),
    ;

    private final Integer status;

    private final String describe;


    OrderBillSubType(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderBillSubType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderBillSubType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        OrderBillSubType e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    public static  final Map<Integer, String> toMap() {
        OrderBillSubType[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (OrderBillSubType value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }
}
