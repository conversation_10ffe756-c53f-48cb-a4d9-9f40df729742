package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderPresetValueDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 订单信息预设置; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
public interface OrderPresetValueRepositoryService extends IService<OrderPresetValueDO> {

    /**
     * 先查询 在更新
     * @param presetValue
     */
    void add(OrderPresetValueDO presetValue);

    /**
     *
     * @param orderId
     * @param status
     */
    void clean(Long orderId, Integer status);

    /**
     * 获取为处理数据
     * @param orderId
     * @return
     */
    OrderPresetValueDO getByOrderNursing(Long orderId);

    /**
     * 已使用
     * @param orderId
     * @param status
     */
    void use(Long orderId, Integer status);
}
