package com.avatar.hospital.chaperone.response.part;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:53
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PartApplyIdResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申请单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

}
