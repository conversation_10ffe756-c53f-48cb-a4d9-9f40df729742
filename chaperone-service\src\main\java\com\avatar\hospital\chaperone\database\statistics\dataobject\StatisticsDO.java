package com.avatar.hospital.chaperone.database.statistics.dataobject;

import com.avatar.hospital.chaperone.database.statistics.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.statistics.enums.StatisticsType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 统计-报表数据;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_statistics")
public class StatisticsDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 时间，202310
     */
      private Integer time;

      /**
     * 统计类型，参考:statistics_type
       * @see StatisticsType
     */
      private Integer statistics;

      /**
     * 统计值
     */
      private String value;


}
