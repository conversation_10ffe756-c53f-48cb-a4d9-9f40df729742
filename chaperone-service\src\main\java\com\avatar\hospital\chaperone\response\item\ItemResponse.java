package com.avatar.hospital.chaperone.response.item;

import com.avatar.hospital.chaperone.database.item.dataobject.ItemDO;
import com.avatar.hospital.chaperone.database.item.dataobject.ItemOrgDO;
import com.avatar.hospital.chaperone.database.item.enums.ItemDisplay;
import com.avatar.hospital.chaperone.database.item.enums.ItemStatus;
import com.avatar.hospital.chaperone.template.serialize.ToStringForLongListSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 15:47
 **/
@Data
public class ItemResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 套餐类型：参考，server_type
     * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
     */
    private Integer serverType;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 服务价格(单位：分/天)
     */
    private Integer price;

    /**
     * 状态(影响C端展示)：1 上架，0 下架
     * @see ItemStatus
     */
    private Integer status;

    /**
     * 护工星级 1 ~ 5 星
     */
    private Integer nursingStar;

    /**
     * 详细描述
     */
    private String remark;

    /**
     * 封面图
     */
    private String coverPicture;

    /**
     * 物业管理费比例
     */
    private Integer rateCertifiedProperty;

    /**
     * 院方管理费比例
     */
    private Integer rateHospital;

    /**
     * 护工费比例
     */
    private Integer rateNursing;

    /**
     * 机构ID
     */
    @JsonSerialize(using = ToStringForLongListSerializer.class)
    private List<Long> orgIdList;

    /**
     * 开始时间 0 ~ 23
     */
    private Integer chargingTime;


    /**
     * 是否外显示 1 是 0 不是
     * @see ItemDisplay
     */
    private Integer display;

    // build

    public static final ItemResponse buildById(Long itemId) {
        ItemResponse obj = new ItemResponse();
        obj.setId(itemId);
        return obj;
    }

    public static ItemResponse build(ItemDO itemDO, List<ItemOrgDO> itemOrgDOList) {
        ItemResponse obj = new ItemResponse();
        obj.setId(itemDO.getId());
        obj.setServerType(itemDO.getServerType());
        obj.setName(itemDO.getName());
        obj.setPrice(itemDO.getPrice());
        obj.setStatus(itemDO.getStatus());
        obj.setNursingStar(itemDO.getNursingStar());
        obj.setRemark(itemDO.getRemark());
        obj.setCoverPicture(itemDO.getCoverPicture());
        obj.setRateCertifiedProperty(itemDO.getRateCertifiedProperty());
        obj.setRateHospital(itemDO.getRateHospital());
        obj.setRateNursing(itemDO.getRateNursing());
        obj.setChargingTime(itemDO.getChargingTime());
        obj.setDisplay(itemDO.getDisplay());

        List<Long> orgIdList = itemOrgDOList.stream()
                .map(itemOrg -> itemOrg.getOrgId())
                .collect(Collectors.toList());
        obj.setOrgIdList(orgIdList);
        return obj;
    }

    public static ItemResponse buildByItemDO(ItemDO d) {
        ItemResponse itemResponse = new ItemResponse();
        itemResponse.setId(d.getId());
        itemResponse.setServerType(d.getServerType());
        itemResponse.setName(d.getName());
        itemResponse.setPrice(d.getPrice());
        itemResponse.setStatus(d.getStatus());
        itemResponse.setNursingStar(d.getNursingStar());
        itemResponse.setRemark(d.getRemark());
        itemResponse.setCoverPicture(d.getCoverPicture());
        itemResponse.setRateCertifiedProperty(d.getRateCertifiedProperty());
        itemResponse.setRateHospital(d.getRateHospital());
        itemResponse.setRateNursing(d.getRateNursing());
        itemResponse.setChargingTime(d.getChargingTime());
        itemResponse.setDisplay(d.getDisplay());
        return itemResponse;
    }
}
