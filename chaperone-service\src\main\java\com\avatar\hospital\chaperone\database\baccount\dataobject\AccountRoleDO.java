package com.avatar.hospital.chaperone.database.baccount.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户角色关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_account_role")
public class AccountRoleDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 角色ID
     */
    private Long roleId;

}
