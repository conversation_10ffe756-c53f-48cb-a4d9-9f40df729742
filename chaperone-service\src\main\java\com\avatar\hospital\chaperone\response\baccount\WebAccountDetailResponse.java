package com.avatar.hospital.chaperone.response.baccount;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/10
 */
@Data
public class WebAccountDetailResponse implements Serializable {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 用户头像地址
     */
    private String avatarUrl;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.AccountStatus
     */
    private Integer status;

    /**
     * 用户类型
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.AccountType
     */
    private Integer type;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginDate;

    /**
     * 密码最后更新时间
     */
    private LocalDateTime passwordUpdateDate;

    /**
     * 创建者ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createBy;

    /**
     * 更新者ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 用户组织机构集合
     */
    private List<WebAccountOrganizationResponse> organizationList;

    /**
     * 用户部门集合
     */
    private List<WebAccountOrganizationResponse> departmentList;

    /**
     * 用户角色集合
     */
    private List<WebAccountRoleResponse> roleList;


    @Data
    public static class WebAccountRoleResponse implements Serializable {

        /**
         * 角色ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 角色名称
         */
        private String name;

        /**
         * 角色权限字符串
         */
        private String roleKey;

        /**
         * 启用状态
         *
         * @see com.avatar.hospital.chaperone.database.baccount.enums.RoleStatus
         */
        private Integer status;

        /**
         * 备注
         */
        private String remark;
    }

    @Data
    public static class WebAccountOrganizationResponse implements Serializable {

        /**
         * 主键
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 名称
         */
        private String name;

        /**
         * 上级组织机构ID 顶级节点默认为null
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long parentId;

        /**
         * 层级
         *
         * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationLevel
         */
        private Integer level;

        /**
         * 启用状态
         *
         * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationStatus
         */
        private Integer status;
    }
}
