package com.avatar.hospital.chaperone.template.util;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public class DefaultUtils {

    public static final Long LONG_ZERO = 0L;

    public static final Integer INTEGER_ZERO = 0;

    public static Long ifNullDefault(Long number, Long defaultNumber) {
        return number == null ? defaultNumber : number;
    }

    public static Long ifNullOrZeroDefault(Long number, Long defaultNumber) {
        if (number == null || LONG_ZERO.equals(number)) {
            return defaultNumber;
        }
        return number;
    }

    public static Integer ifNullDefault(Integer number, Integer defaultNumber) {
        return number == null ? defaultNumber : number;
    }

    public static Integer ifNullOrZeroDefault(Integer number, Integer defaultNumber) {
        if (number == null || INTEGER_ZERO.equals(number)) {
            return defaultNumber;
        }
        return number;
    }

    public static String ifNullDefault(String str, String defaultNumber) {
        return str == null ? defaultNumber : str;
    }

    public static String ifEmptyDefault(String str, String defaultNumber) {
        if (StrUtils.hasText(str)) {
            return str;
        }
        return defaultNumber;
    }

    public static <P1, P2> Map<P1, P2> ifNullDefault(Map<P1, P2> map, Map<P1, P2> defaultMap) {
        return map == null ? defaultMap : map;
    }

    public static <P1> List<P1> ifNullDefault(List<P1> list, List<P1> defaultList) {
        return list == null ? defaultList : list;
    }

    public static <P1> Set<P1> ifNullDefault(Set<P1> set, Set<P1> defaultSet) {
        return set == null ? defaultSet : set;
    }

}
