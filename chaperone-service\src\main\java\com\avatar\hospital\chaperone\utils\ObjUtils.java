package com.avatar.hospital.chaperone.utils;

import com.alibaba.fastjson.JSONObject;

import java.util.Objects;
import java.util.function.Consumer;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-18 11:13
 **/
public class ObjUtils {


    /**
     * 对象set值
     * @param consumer
     * @param data
     * @param <T>
     */
    public static final <T> void settingNotNull(Consumer<T> consumer,T data) {
        if (Objects.nonNull(data)) {
            consumer.accept(data);
        }
    }

    /**
     * json 解析
     * @param text
     * @param clazz
     * @return
     * @param <T>
     */
    public static <T> T jsonParseObject(String text, Class<T> clazz) {
        if (Objects.isNull(text) || text.isEmpty()) {
            return null;
        }
        T obj = JSONObject.parseObject(text, clazz);
        return obj;
    }
}
