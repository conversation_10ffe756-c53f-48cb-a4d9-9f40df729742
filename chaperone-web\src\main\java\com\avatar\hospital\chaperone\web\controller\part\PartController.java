package com.avatar.hospital.chaperone.web.controller.part;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.request.part.PartBatchRequest;
import com.avatar.hospital.chaperone.request.part.QueryExportRequest;
import com.avatar.hospital.chaperone.request.part.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartBatchVO;
import com.avatar.hospital.chaperone.response.part.PartVO;
import com.avatar.hospital.chaperone.service.part.PartBatchService;
import com.avatar.hospital.chaperone.service.part.PartService;
import com.avatar.hospital.chaperone.service.part.consts.CacheKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.utils.ExportUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description 备件
 * @date 2023/10/27 15:46
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/project/part")
public class PartController {

    private final PartBatchService partBatchService;

    private final PartService partService;

    /**
     * 批次-创建
     *
     * @param request
     * @return
     */
    @Idempotent(value = CacheKey.PART_CREATE_KEY + " + #request.name")
    @PostMapping("create")
    public SingleResponse<Long> create(@RequestBody PartBatchRequest request) {
        return TemplateProcess.doProcess(log, () -> {

            return partBatchService.create(request);
        });
    }

    /**
     * 批次-更新

     *
     * @param request
     * @return
     */
    @Idempotent(value = CacheKey.PART_UPDATE_KEY + " + #request.id")
    @PostMapping("update")
    public SingleResponse<Long> update(@RequestBody PartBatchRequest request) {
        return TemplateProcess.doProcess(log, () -> {

            return partBatchService.update(request);
        });
    }


    /**
     * 查询
     *
     * @param request
     * @return
     */
    @GetMapping("paging")
    public SingleResponse<PageResponse<PartBatchVO>> paging(QueryRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return partBatchService.paging(request);
        });
    }

    /**
     * 查询-详情
     *
     * @param id
     * @return
     */
    @GetMapping("detail/{id}")
    public SingleResponse<PartBatchVO> getById(@PathVariable("id") Long id) {
        return TemplateProcess.doProcess(log, () -> {
            return partBatchService.detail(id);
        });
    }

    /**
     * 查询配件明细
     *
     * @param request
     * @return
     */
    @GetMapping("detail/paging/{id}")
    public SingleResponse<PageResponse<PartVO>> detailPaging(@PathVariable("id") Long id, QueryRequest request) {
        request.setBatchId(id);
        return TemplateProcess.doProcess(log, () -> {
            return partService.paging(request);
        });
    }

    /**
     * 导出配件明细
     *
     * @param request
     * @return
     */
    @PostMapping("detail/export/{id}")
    public void detailPaging(@PathVariable("id") Long id,
                             @RequestBody QueryExportRequest request,
                             HttpServletResponse response) {
        request.setBatchId(id);
        ExportUtil.export(response,request,partService::export);
    }
}
