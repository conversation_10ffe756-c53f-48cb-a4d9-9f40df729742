package com.avatar.hospital.chaperone.database.repair.dataobject;

import com.avatar.hospital.chaperone.database.repair.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 报修单任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_repair_form_task")
public class RepairFormTaskDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 报修单ID
     */
    private Long repairFormId;

    /**
     * 执行人员ID
     */
    private Long executorAccountId;

    /**
     * 派工人员ID
     */
    private Long assignerAccountId;


}
