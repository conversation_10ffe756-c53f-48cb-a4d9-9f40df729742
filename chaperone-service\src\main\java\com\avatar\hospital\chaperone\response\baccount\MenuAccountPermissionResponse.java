package com.avatar.hospital.chaperone.response.baccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.Set;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/30
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class MenuAccountPermissionResponse implements Serializable {

    /**
     * 用户权限字符串
     */
    private Set<String> permissions;

    /**
     * 构造空请求
     *
     * @return -
     */
    public static MenuAccountPermissionResponse buildEmpty() {
        MenuAccountPermissionResponse response = new MenuAccountPermissionResponse();
        response.setPermissions(Collections.emptySet());
        return response;
    }

}
