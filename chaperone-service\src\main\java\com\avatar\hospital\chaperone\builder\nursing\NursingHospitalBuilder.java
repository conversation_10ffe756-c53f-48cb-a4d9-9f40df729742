package com.avatar.hospital.chaperone.builder.nursing;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingHospitalDO;
import com.avatar.hospital.chaperone.request.nursing.NursingAddRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingUpdateRequest;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @author:sp0420
 * @Description:
 */
public class NursingHospitalBuilder {

    public static List<NursingHospitalDO> buildNursingHospitalDO(NursingAddRequest request, Set<Long> organizationIdSet, Long nursingId) {
        if (request == null || organizationIdSet.isEmpty() || nursingId == null) {
            return null;
        }
        List<NursingHospitalDO> list = new ArrayList<>();
        organizationIdSet.forEach(i -> {
            NursingHospitalDO nursingHospitalDO = new NursingHospitalDO();
            nursingHospitalDO.setNursingId(nursingId);
            nursingHospitalDO.setOrgId(i);
            nursingHospitalDO.setId(IdUtils.getId());
            nursingHospitalDO.setCreateBy(request.getCreateBy());
            nursingHospitalDO.setUpdateBy(request.getUpdateBy());
            nursingHospitalDO.setDeleted(DelUtils.NO_DELETED);
            nursingHospitalDO.setCreatedAt(LocalDateTime.now());
            nursingHospitalDO.setUpdatedAt(LocalDateTime.now());
            list.add(nursingHospitalDO);
        });

        return list;
    }

    public static List<NursingHospitalDO> buildNursingHospitalDO(NursingUpdateRequest request, Set<Long> organizationIdSet, Long nursingId) {
        if (request == null || organizationIdSet.isEmpty() || nursingId == null) {
            return null;
        }
        List<NursingHospitalDO> list = new ArrayList<>();
        organizationIdSet.forEach(i -> {
            NursingHospitalDO nursingHospitalDO = new NursingHospitalDO();
            nursingHospitalDO.setNursingId(nursingId);
            nursingHospitalDO.setOrgId(i);
            nursingHospitalDO.setId(IdUtils.getId());
            nursingHospitalDO.setCreateBy(request.getUpdateBy());
            nursingHospitalDO.setUpdateBy(request.getUpdateBy());
            nursingHospitalDO.setDeleted(DelUtils.NO_DELETED);
            nursingHospitalDO.setCreatedAt(LocalDateTime.now());
            nursingHospitalDO.setUpdatedAt(LocalDateTime.now());
            list.add(nursingHospitalDO);
        });

        return list;
    }
}
