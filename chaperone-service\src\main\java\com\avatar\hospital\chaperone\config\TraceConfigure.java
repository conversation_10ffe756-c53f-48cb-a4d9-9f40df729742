package com.avatar.hospital.chaperone.config;

import com.avatar.hospital.chaperone.filter.TraceFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Configuration
public class TraceConfigure {

    @Bean
    public FilterRegistrationBean<TraceFilter> registerTraceFilter() {
        FilterRegistrationBean<TraceFilter> bean = new FilterRegistrationBean<>();
        bean.setOrder(1);
        bean.setFilter(new TraceFilter());
        bean.addUrlPatterns("/*");
        return bean;
    }

}
