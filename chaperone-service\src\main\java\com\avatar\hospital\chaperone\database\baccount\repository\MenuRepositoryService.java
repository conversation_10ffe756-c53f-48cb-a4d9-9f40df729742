package com.avatar.hospital.chaperone.database.baccount.repository;

import com.avatar.hospital.chaperone.database.baccount.dataobject.MenuDO;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 菜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface MenuRepositoryService extends IService<MenuDO> {

    /**
     * 添加菜单权限
     *
     * @param menuDO -
     * @return -
     */
    Long add(MenuDO menuDO);

    /**
     * 增量更新菜单权限
     *
     * @param menuDO -
     * @return -
     */
    Boolean incrementUpdate(MenuDO menuDO);

    /**
     * 根据id批量删除
     *
     * @param ids      -
     * @param updateBy -
     * @return -
     */
    Boolean deleteByIds(Set<Long> ids, Long updateBy);

    /**
     * 根据ID查询
     *
     * @param id -
     * @return -
     */
    MenuDO findById(Long id);

    /**
     * 根据ID批量查询
     *
     * @param ids -
     * @return -
     */
    List<MenuDO> findByIds(Set<Long> ids);

    /**
     * 分页查询菜单权限
     *
     * @param pageIndex -
     * @param pageSize  -
     * @param menuDO    -
     * @return -
     */
    PageResponse<MenuDO> paging(Integer pageIndex, Integer pageSize, MenuDO menuDO);

    /**
     * 查询全部菜单权限
     *
     * @return -
     */
    List<MenuDO> listAll();

    /**
     * 根据 id&level&status 查询
     *
     * @param ids    -
     * @param types -
     * @param status -
     * @return -
     */
    List<MenuDO> findByIdsAndTypesAndStatus(Set<Long> ids, Set<Integer> types, Integer status);
}
