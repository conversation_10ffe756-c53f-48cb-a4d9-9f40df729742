package com.avatar.hospital.chaperone.database.nursing.dataobject;

import com.avatar.hospital.chaperone.database.nursing.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 护工-月度好评排名;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_nursing_top")
public class NursingTopDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 时间,月，202310
     */
      private Integer time;

      /**
     * 护工ID
     */
      private Long nursingId;

      /**
     * 排名
     */
      private Integer rank;


}
