package com.avatar.hospital.chaperone.properties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * Description: 用作固定微信模板消息
 *
 * <AUTHOR>
 * @since 2023/10/17
 */
@Data
@Slf4j
@ConfigurationProperties(prefix = WxTemplateProperties.PREFIX)
@Component
public class WxTemplateProperties implements Serializable {

    public static final String PREFIX = "wx-template";

    /**
     * 微信模板消息ID
     */
    private String messageId1;
}
