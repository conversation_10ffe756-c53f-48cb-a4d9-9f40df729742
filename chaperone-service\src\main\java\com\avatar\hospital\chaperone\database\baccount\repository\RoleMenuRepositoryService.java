package com.avatar.hospital.chaperone.database.baccount.repository;

import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleMenuDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色菜单关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface RoleMenuRepositoryService extends IService<RoleMenuDO> {

    /**
     * 根据roleId批量查询
     *
     * @param roleIds -
     * @return -
     */
    List<RoleMenuDO> findByRoleIds(Set<Long> roleIds);

    /**
     * 根据menuId批量查询
     *
     * @param menuIds -
     * @return -
     */
    List<RoleMenuDO> findByMenuIds(Set<Long> menuIds);

    /**
     * 根据roleId批量删除
     *
     * @param roleIds -
     * @return -
     */
    Boolean deleteByRoleIds(Set<Long> roleIds);

    /**
     * 根据menuId批量删除
     *
     * @param menuIds -
     * @return -
     */
    Boolean deleteByMenuIds(Set<Long> menuIds);
}
