<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.part.mapper.PartApplyRefPartBatchMapper">

    <select id="getStatistics" resultType="com.avatar.hospital.chaperone.database.part.dataobject.model.PartStatisticsModel">
        SELECT stockapply.`org_id` as 'org_id',
            stockapply.`investor` as 'org_name',
            stockapply.`investor_type` as 'investor_type',
            sum(apply.`quantity`)  as quantity,
            sum(batch.`price`)  as price
        FROM `t_project_spare_part_apply_ref_part_batch` apply
        LEFT JOIN `t_project_spare_part_batch` batch on apply.`spare_part_batch_id`= batch.`id`
        LEFT JOIN `t_project_spare_part_stock_apply_ref_part_batch` stockbatch on stockbatch.`spare_part_batch_id`= apply.`spare_part_batch_id`
        LEFT JOIN `t_project_spare_part_stock_apply` stockapply on stockapply.`id`= stockbatch.`stock_apply_id`
        WHERE apply.`deleted`= 0
        AND apply.`created_at` &gt;= #{start}
        AND apply.`created_at` &lt;= #{end}
        GROUP BY stockapply.`org_id`,
                 stockapply.`investor_type`
        ORDER BY stockapply.`investor_type` asc
    </select>
</mapper>