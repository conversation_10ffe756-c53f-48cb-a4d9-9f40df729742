package com.avatar.hospital.chaperone.utils;

import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-06 14:57
 **/
public class PriceUtil {

    /**
     * 分转元
     * @param price
     * @return
     */
    public final static String fenToYuan(Integer price) {
        if (Objects.isNull(price) || price <=0) {
            return "0";
        }
        Integer yuan = price / 100;
        Integer fen = price % 100;
        String fenStr = fen < 10 ? "0" + fen : fen.toString();
        return yuan + "." + fenStr;
    }

}
