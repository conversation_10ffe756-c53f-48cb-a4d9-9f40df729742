package com.avatar.hospital.chaperone.consumer.handler;


import com.avatar.hospital.chaperone.utils.AopLogUtils;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Order(value = 101)
@Slf4j
@Aspect
@Component
public class WebLogAspect {

    @Pointcut("execution(public * com.avatar.hospital.chaperone.consumer.controller..*.*(..))")
    public void log() {
    }

    /**
     * 环绕
     * @param proceedingJoinPoint
     * @return
     * @throws Throwable
     */
    @Around("log()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        return AopLogUtils.proceed("CONSUMER",proceedingJoinPoint);
    }

}
