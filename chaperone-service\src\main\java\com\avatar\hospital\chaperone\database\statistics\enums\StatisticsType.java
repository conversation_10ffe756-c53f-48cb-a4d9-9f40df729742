package com.avatar.hospital.chaperone.database.statistics.enums;

import lombok.Getter;

/**
 * Description:
 *  统计类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum StatisticsType {
    NONE("NONE", "未知"),
    ORDER_TOTAL_DAY("order_total_day", "本月陪护服务总时长（人天）"),
    ORDER_TOTAL_PRICE("order_total_price", "总金额"),
    ORDER_UNPAY_PRICE("order_unpay_price", "应收未收"),
    ORDER_STAR1("order_star1", "订单评价1星数量"),
    ORDER_STAR2("order_star2", "订单评价2星数量"),
    ORDER_STAR3("order_star3", "订单评价3星数量"),
    ORDER_STAR4("order_star4", "订单评价4星数量"),
    ORDER_STAR5("order_star5", "订单评价5星数量"),
    ;

    private final String status;

    private final String describe;


    StatisticsType(String status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static StatisticsType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (StatisticsType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
