package com.avatar.hospital.chaperone.database.nursing.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum Gender {
    NONE(-1, "未知"),
    GIRL(0, "女"),
    BOY(1, "男"),
    ;

    private final Integer status;

    private final String describe;


    Gender(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static Gender of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (Gender itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
