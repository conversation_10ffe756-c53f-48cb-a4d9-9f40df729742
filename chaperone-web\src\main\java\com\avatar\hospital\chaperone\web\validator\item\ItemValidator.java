package com.avatar.hospital.chaperone.web.validator.item;

import com.avatar.hospital.chaperone.database.item.enums.ItemServerType;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.item.ItemCreateRequest;
import com.avatar.hospital.chaperone.request.item.ItemModifyRequest;
import com.avatar.hospital.chaperone.request.item.ItemRequest;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 套餐校验
 * @author: sp0372
 * @create: 2023-10-13 10:38
 **/
public class ItemValidator {

    /**
     * 套餐创建校验
     * @param request
     */
    public static void createValidate(ItemCreateRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        // 分成比例相加为100
        // 枚举值是否存在
        if (Objects.isNull(request.getRemark())) {
            request.setRemark("");
        }
        if (StringUtils.isNotBlank(request.getRemark())
                && request.getRemark().length() >= 255) {
            throw BusinessException.buildBusinessException(ErrorCode.ITEM_REMARK_LENGTH);
        }
        ItemServerType serverType = ItemServerType.of(request.getServerType());
        if (Objects.nonNull(serverType.getOrgId())) {
            request.setOrgIdList(Arrays.asList(serverType.getOrgId()));
        }
    }

    public static void modifyValidate(ItemModifyRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getId(), ErrorCode.PARAMETER_ERROR);
        if (StringUtils.isNotBlank(request.getRemark())
                && request.getRemark().length() > 255) {
           throw BusinessException.buildBusinessException(ErrorCode.ITEM_REMARK_LENGTH);
        }
        ItemServerType serverType = ItemServerType.of(request.getServerType());
        if (Objects.nonNull(serverType.getOrgId())) {
            request.setOrgIdList(Arrays.asList(serverType.getOrgId()));
        }
        // 分成比例相加为100
        // 枚举值是否存在
    }

    public static void offValidate(ItemRequest request) {

    }

    public static void noValidate(ItemRequest request) {

    }
}
