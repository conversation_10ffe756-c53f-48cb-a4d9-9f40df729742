package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 16:47
 **/
@Data
public class OrderModifyItemRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作用户
     */
    private Operator operatorUser;


    /**
     * 订单ID
     */
    @NotNull
    private Long orderId;

    /**
     * 开始时间 yyyyMMddHH
     */
    @NotNull
    private Integer startTime;
    /**
     * 结束时间 yyyyMMddHH
     */
    @NotNull
    private Integer endTime;

    /**
     * 套餐ID
     */
    @NotEmpty
    private List<Long> itemIdList;
}
