package com.avatar.hospital.chaperone.request.baccount;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class MenuUpdateRequest implements Serializable {

    /**
     * 菜单权限ID
     */
    private Long id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 前端菜单英文名称
     */
    private String frontName;

    /**
     * 组件路径
     */
    private String componentUrl;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 菜单权限字符串
     */
    private String menuKey;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.MenuStatus
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新者ID
     */
    private Long updateBy;

}
