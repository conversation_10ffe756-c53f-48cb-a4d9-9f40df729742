package com.avatar.hospital.chaperone.builder.repair;

import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormFeedbackDO;
import com.avatar.hospital.chaperone.request.repair.RepairFormFeedbackCreateRequest;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-30 09:53
 **/
public class RepairFeedbackBuilder {
    public static RepairFormFeedbackDO createRepairFormFeedbackDO(RepairFormFeedbackCreateRequest request) {
        String name = request.getOperatorUser().getName();
        Long operator = request.getOperator();
        long id = IdUtils.getId();

        RepairFormFeedbackDO repairFormFeedbackDO = new RepairFormFeedbackDO();
        repairFormFeedbackDO.setId(id);
        repairFormFeedbackDO.setRemark(request.getRemark());
        repairFormFeedbackDO.setFeedbackType(request.getType());
        repairFormFeedbackDO.setAttachments(request.getAttachments());
        repairFormFeedbackDO.setRepairFormId(request.getRepairFormId());
        repairFormFeedbackDO.setCreateBy(operator);
        repairFormFeedbackDO.setCreateByName(name);
        repairFormFeedbackDO.setUpdateBy(operator);
        repairFormFeedbackDO.setDeleted(DelUtils.NO_DELETED);
        return repairFormFeedbackDO;
    }
}
