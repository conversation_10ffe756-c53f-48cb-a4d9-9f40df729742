package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * Description:
 *  账单类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderBillType implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    CYCLE(1, "周期账单"),
    PREPAY(2, "预付单"),
    COMPENSATE(3, "补偿账单"),
    SETTLE(11, "结算单"),
    ;

    private final Integer status;

    private final String describe;


    OrderBillType(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderBillType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderBillType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        OrderBillType e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    public static  final Map<Integer, String> toMap() {
        OrderBillType[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (OrderBillType value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

    /**
     * 是否是预付单
     * @return
     */
    public boolean isPrepay() {
        return PREPAY.equals(this);
    }

    /**
     * 是否是周期账单
     * @return
     */
    public boolean isCycle() {
        return CYCLE.equals(this);
    }

    /**
     * 是否是结算单
     * @return
     */
    public boolean isSettle() {
        return SETTLE.equals(this);
    }
}
