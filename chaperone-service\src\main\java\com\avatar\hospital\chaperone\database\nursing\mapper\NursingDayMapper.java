package com.avatar.hospital.chaperone.database.nursing.mapper;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDayDO;
import com.avatar.hospital.chaperone.response.nursing.NursingDayOrderResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 护工-考勤信息; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Mapper
public interface NursingDayMapper extends BaseMapper<NursingDayDO> {

    Long countByB(Map<String, Object> criteria);

    List<NursingDayPagingResponse> listByB(Map<String, Object> criteria);

    List<NursingDayOrderResponse> listByNursingOrderByB(@Param("nursingIds") List<Long> nursingIds);
}
