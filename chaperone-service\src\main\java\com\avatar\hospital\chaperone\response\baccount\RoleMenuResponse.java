package com.avatar.hospital.chaperone.response.baccount;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class RoleMenuResponse implements Serializable {

    /**
     * 菜单权限
     */
    private List<MenuData> menuList;


    @Data
    public static class MenuData {

        /**
         * 主键
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 菜单名称
         */
        private String name;

        /**
         * 前端菜单英文名称
         */
        private String frontName;

        /**
         * 上级菜单 顶级节点默认为null
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long parentId;

        /**
         * 菜单权限字符串
         */
        private String menuKey;

        /**
         * 菜单权限类型
         *
         * @see com.avatar.hospital.chaperone.database.baccount.enums.MenuType
         */
        private Integer type;

        /**
         * 启用状态
         *
         * @see com.avatar.hospital.chaperone.database.baccount.enums.MenuStatus
         */
        private Integer status;

        /**
         * 排序
         */
        private Integer sort;

        /**
         * 备注
         */
        private String remark;

    }


}
