package com.avatar.hospital.chaperone.consumer.controller.caccount;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.consumer.validator.caccount.LoginValidator;
import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.caccount.repository.ConsumerAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountLoginRequest;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountLoginResponse;
import com.avatar.hospital.chaperone.service.caccount.ConsumerLoginService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: C端用户登录接口
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/login")
public class ConsumerLoginController {

    private final ConsumerLoginService consumerLoginService;
    private final ConsumerAccountRepositoryService consumerAccountRepositoryService;

    /**
     * 微信小程序登录
     *
     * @param request -
     * @return -
     */
    @PostMapping(value = "")
    public SingleResponse<ConsumerAccountLoginResponse> login(@RequestBody ConsumerAccountLoginRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("LoginController login request:{}", JSON.toJSONString(request));
            LoginValidator.loginValidate(request);
            return consumerLoginService.login(request);
        });
    }

    /**
     * 微信小程序登录 测试
     *
     * @param phoneNumber -
     * @return -
     */
    @PostMapping(value = "/test")
    public SingleResponse<ConsumerAccountLoginResponse> testLogin(@RequestParam(value = "phoneNumber") String phoneNumber) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("LoginController testLogin request:{}", phoneNumber);
            AssertUtils.isNotNull(phoneNumber, ErrorCode.PARAMETER_ERROR);
            AccountDO accountDO = consumerAccountRepositoryService.findByPhoneNumber(phoneNumber);
            AssertUtils.isNotNull(accountDO, ErrorCode.CONSUMER_ACCOUNT_NOT_EXIST);
            // C端用户登录
            StpUtil.login(accountDO.getId());
            SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
            return ConsumerAccountLoginResponse.builder().tokenName(tokenInfo.getTokenName()).tokenValue(tokenInfo.getTokenValue()).build();
        });
    }

}
