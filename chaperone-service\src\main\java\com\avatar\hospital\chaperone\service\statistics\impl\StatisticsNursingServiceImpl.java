package com.avatar.hospital.chaperone.service.statistics.impl;

import com.avatar.hospital.chaperone.database.nursing.repository.NursingOrderRepositoryService;
import com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsEstimateModel;
import com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsNursingModel;
import com.avatar.hospital.chaperone.database.order.repository.OrderBillRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderConsumerlogRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderEstimateRepositoryService;
import com.avatar.hospital.chaperone.request.statistics.StatisticsRequest;
import com.avatar.hospital.chaperone.response.statistics.StatisticsEstimateResponse;
import com.avatar.hospital.chaperone.response.statistics.StatisticsPriceResponse;
import com.avatar.hospital.chaperone.response.statistics.StatisticsServerTimeResponse;
import com.avatar.hospital.chaperone.service.statistics.StatisticsNursingService;
import com.avatar.hospital.chaperone.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-20 11:42
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsNursingServiceImpl implements StatisticsNursingService {
    private final NursingOrderRepositoryService nursingOrderRepositoryService;
    private final OrderEstimateRepositoryService orderEstimateRepositoryService;
    private final OrderConsumerlogRepositoryService orderConsumerlogRepositoryService;
    private final OrderBillRepositoryService orderBillRepositoryService;

    @Override
    public StatisticsServerTimeResponse getServerTime(StatisticsRequest request) {
        LocalDateTime[] startEndOfYear = DateUtils.getStartEndOfYear(request.getYear());
        Integer yearNum = nursingOrderRepositoryService.countNum(startEndOfYear[0],startEndOfYear[1]);
        LocalDateTime[] startEndOfMonth = DateUtils.getStartEndOfMonth(request.getMonth());
        Integer monthNum = nursingOrderRepositoryService.countNum(startEndOfMonth[0],startEndOfMonth[1]);
        return StatisticsServerTimeResponse.build(yearNum,monthNum);
    }

    @Override
    public StatisticsEstimateResponse getEstimate(StatisticsRequest request) {
        LocalDateTime[] startEndOfYear = DateUtils.getStartEndOfYear(request.getYear());
        StatisticsEstimateModel statisticsStartForYear = orderEstimateRepositoryService.statisticsStar(startEndOfYear[0],startEndOfYear[1]);
        List<StatisticsNursingModel> statisticsNursingForYear = orderEstimateRepositoryService.statisticsNursing(startEndOfYear[0],startEndOfYear[1]);

        LocalDateTime[] startEndOfMonth = DateUtils.getStartEndOfMonth(request.getMonth());
        StatisticsEstimateModel statisticsStartForMonth = orderEstimateRepositoryService.statisticsStar(startEndOfMonth[0],startEndOfMonth[1]);
        List<StatisticsNursingModel> statisticsNursingForMonth = orderEstimateRepositoryService.statisticsNursing(startEndOfMonth[0],startEndOfMonth[1]);
        return StatisticsEstimateResponse.build(statisticsStartForYear,statisticsNursingForYear,
                                                statisticsStartForMonth,statisticsNursingForMonth);
    }

    @Override
    public StatisticsPriceResponse getPrice(StatisticsRequest request) {
        LocalDateTime[] startEndOfYear = DateUtils.getStartEndOfYear(request.getYear());
        Long totalYear = orderConsumerlogRepositoryService.statistics(startEndOfYear[0],startEndOfYear[1]);
        Long unpayYear = orderBillRepositoryService.unpay(startEndOfYear[0],startEndOfYear[1]);

        LocalDateTime[] startEndOfMonth = DateUtils.getStartEndOfMonth(request.getMonth());
        Long totalMonth = orderConsumerlogRepositoryService.statistics(startEndOfMonth[0],startEndOfMonth[1]);
        Long unpayMonth = orderBillRepositoryService.unpay(startEndOfMonth[0],startEndOfMonth[1]);
        return StatisticsPriceResponse.build(totalYear,unpayYear,totalMonth,unpayMonth);
    }
}
