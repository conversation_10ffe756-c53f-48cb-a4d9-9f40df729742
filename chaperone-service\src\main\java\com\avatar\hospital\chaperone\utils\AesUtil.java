package com.avatar.hospital.chaperone.utils;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 14:34
 **/
@Slf4j
public class AesUtil {

    static final int KEY_LENGTH_BYTE = 32;
    static final int TAG_LENGTH_BIT = 128;
    private final byte[] aesKey;

    public AesUtil(byte[] key) {
        if (key.length != KEY_LENGTH_BYTE) {
            throw new IllegalArgumentException("无效的ApiV3Key，长度必须为32个字节");
        }
        this.aesKey = key;
    }

    public String decryptToString(byte[] associatedData, byte[] nonce, String ciphertext)
            throws GeneralSecurityException, IOException {
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");

            SecretKeySpec key = new SecretKeySpec(aesKey, "AES");
            GCMParameterSpec spec = new GCMParameterSpec(TAG_LENGTH_BIT, nonce);

            cipher.init(Cipher.DECRYPT_MODE, key, spec);
            cipher.updateAAD(associatedData);

            return new String(cipher.doFinal(Base64.getDecoder().decode(ciphertext)), "utf-8");
        } catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
            throw new IllegalStateException(e);
        } catch (InvalidKeyException | InvalidAlgorithmParameterException e) {
            throw new IllegalArgumentException(e);
        }
    }

    @SneakyThrows
    public static void main(String[] args) {
        AesUtil aesUtil = new AesUtil("wJ65GOBWhqCXc406PJF6XQth7VqWZ18j".getBytes());
        String certificate = aesUtil.decryptToString("certificate".getBytes(), "f7eb2de5c4c0".getBytes(), "83VE1AaQSW3FecAj8gvS8ovcP0MbB7utLsxiQMB+wZbnNNPD5NwKrQjP3TYa4NGGAq5Jnx/KTnsD/BzaoEC3HBbEH/0dq8DVSCk90U4tGygbck38ap0lzu9hiK0il3zAu1Y50krXmb4AFSwmHhk3iV/ejLHghiEBcJZkv+rNtEDXdnoNpmu55K3zTm/GkTa8QDs0jRXoBNn4vKyhoDvcCdBosL+paKmD2mbnzpg4M86wmOEl/qUr5Chdw5WLSGTcZAJLap0byw1SLaRBNWtRRhK7rvUhWDOwP0mbrnLv0PKObvPDPwm3Rph54DG64FYhoNQIGEJHho0mqBAVL6a3wRnp1btvUHUc9sffYAWl5GmRoZIq08XHZlYtTA5ZIu/dz2L//t0+0SiYpBE2rUGizZaEUwHiyuzDKUWlW4B7SvbIj9v6ItdmOKFlD5Kke8tQwSpOUtEgP3qhT/rpPhpO+EnnEA47nEV5uLud45mUWePjpUHu+I+bmClOV2hRN+3K5yW2xs33bbEYirjIYuagTRQs2yqo+YW69TTpxko+uMWzFKVlkO6DzrGTYk9Majs7geC5SGNC6cLqfDrxwe1bGNedHQ7a8FfS3b/UY7H3+y7xaDyhRnTUjOKeqs/mZQiCZVDsbfVGG7ROXALUZp7mHgGOWg3qG10OHVWfhUPVsn75+oV1Z6mI5wPpYel2Pio/XaXidMhbnSodPiRwQ8W2ZGov6PIksaXoP7IoaEC5gGKlKk4aFstGZkIUQXbxq580eoPf3ejEetF7nzUBjGf3riJVMaxZaI7FtZqcAda18PO+X2KvOgYVW0xSkvzRAQhTWWsEBiYJbANMta86zhRdOlOZ3d144sNdNNzKD2TCCspyUsXB9n87e7QCGqDElb1SbvZeowNAqTM/KmY3jRKirJpxkkt+qSod8OwnS5Dt715C3jTsP/7+Kzjn3SKzZZu7CUOP2kDh7QcE+igueSp2hKr/yhid5FJpqPstFjmgVaWAVB3Savbo9TXM8bvCLFy974ZcMiRDOAM5n/yKGEDw1Ps/k59O3+irS+rqb+7a4cGrvpv5/ClJrJBsn0IJtjJLNat5N0dg9ZjhbKA/3M7yCW3I1QlXTrkleXvCrNnGP7N6xXKFyDfXaxNvD4JybVRgEEqRBT1qCoqGII4srhCQNzdHcwT7UC9Y2JBONSvA9/uhMD8iTyz7+/DYxBwfBAn6HfgsLq8q3p7EXnAAJNdntvBH0t8nEULoY33KNTMba9fRwEN2yfottoLHQxX2fZn5V6IUPZyUPj3FCCGKyWyE80FDRkdx5uMg8RvIy1m1HbJKfxagZs+8Y9qzxMfJcHPE9SbIsnjonMXJ90U/JVZ1THgVuUBI97J4YYRlkCm3SRgW+PDKxQAx9/h3aKsqFxHqqD8hiMN47M2Ms0mg26OjFwmUZlQ8Rjqvvpo2WeFy7sE2eXgRloRqiCBB9ECNWPtSb7hknYlhI1uoMOrmDpcG6ys48xjjKRes9gmSVS6HrK1HNx5Ou64FRMnIvHdmLq6hgg2ekee6T5k9SbyrckN+0dMqODxa38m+hOPgvmhLfe9MEWLcuH0p18sIgru5fNAUs5ZwuYZ55CbvbZYqmApQrbtfdXDDo9fFcdNEtmA6txl+u1vtb1Jv/F2wbHre3xOSkhN+2wkPocNvZvn0WomuOHjhO/zXmWXNo8F9rTS4aqpMw2hfrrqRN84uIVKXPw3koxBW+rX3mKxztGv4vvUqUg5FPvwt8UIMrM2bLzCNoc0Eh5nnhBzHRX3uwWYa16+YsU0Xd0pSLs5466famVY1qRvw5QWviimIlVvG0x2JpLMYMqMLA8IcFEl+MQnYOK+UmadJ29AoJhnn4eHKr/arNKU13Tt2nAv4S4j0uCbRYN7mhaQDKfxN5Nq235/G/HhywjtX");
        log.info(certificate);
    }
}