package com.avatar.hospital.chaperone.response.order;

import com.avatar.hospital.chaperone.database.order.enums.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderTotalBillResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总账单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 账单类型:参考，bill_type
     * @see OrderBillType
     */
    private Integer billType;

    /**
     * 账单子类型:参考，bill_sub_type
     * @see OrderBillSubType
     */
    private Integer billSubType;

    /**
     * 总共应收款金额，单位:分
     */
    private Integer priceReceivable;

    /**
     * 已收金额，单位:分
     */
    private Integer priceReceived;

    /**
     * 备注,账单说明
     */
    private String remark;

    /**
     * 预付单金额(单位分)
     */
    private Integer price;

    /**
     * 账单明细
     */
    private List<OrderBillItemResponse> itemList;
}
