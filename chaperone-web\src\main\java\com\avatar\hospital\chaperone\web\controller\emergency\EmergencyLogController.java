package com.avatar.hospital.chaperone.web.controller.emergency;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogAddRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogPagingRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogUpdateRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogAddResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogPagingResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogUpdateResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingAddResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingPagingResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingUpdateResponse;
import com.avatar.hospital.chaperone.service.emergency.EmergencyLogService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.web.validator.emergency.EmergencyLogValidator;
import com.avatar.hospital.chaperone.web.validator.nursing.NursingValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/emergency")
public class EmergencyLogController {

    private final EmergencyLogService emergencyLogService;


    /**
     * 创建
     */
    @PostMapping(value = "")
    public SingleResponse<EmergencyLogAddResponse> add(@RequestBody EmergencyLogAddRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("EmergencyLogController add request:{}", request);
            EmergencyLogValidator.addValidate(request);
            return emergencyLogService.add(request);
        });
    }

    /**
     * 修改
     */
    @PutMapping(value = "")
    public SingleResponse<EmergencyLogUpdateResponse> update(@RequestBody EmergencyLogUpdateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("EmergencyLogController update request:{}", request);
            EmergencyLogValidator.updateValidate(request);
            return emergencyLogService.update(request);
        });
    }


    /**
     * 分页
     */
    @GetMapping(value = "/paging")
    public SingleResponse<PageResponse<EmergencyLogPagingResponse>> paging(EmergencyLogPagingRequest request){
        return TemplateProcess.doProcess(log, () -> {
            log.info("EmergencyLogController paging request:{}", request);
            EmergencyLogValidator.pagingValidate(request);
            return emergencyLogService.paging(request);
        });
    }
}
