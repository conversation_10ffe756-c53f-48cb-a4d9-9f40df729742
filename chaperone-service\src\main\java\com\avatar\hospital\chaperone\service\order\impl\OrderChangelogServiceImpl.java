package com.avatar.hospital.chaperone.service.order.impl;

import com.avatar.hospital.chaperone.builder.order.OrderBuilder;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderChangelogDO;
import com.avatar.hospital.chaperone.database.order.repository.OrderChangelogRepositoryService;
import com.avatar.hospital.chaperone.request.order.OrderChangelogPageRequest;
import com.avatar.hospital.chaperone.request.order.OrderChangelogSaveRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderChangelogResponse;
import com.avatar.hospital.chaperone.response.order.OrderChangelogSaveResponse;
import com.avatar.hospital.chaperone.service.order.OrderChangelogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 15:24
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderChangelogServiceImpl implements OrderChangelogService {
    private final OrderChangelogRepositoryService orderChangelogRepositoryService;
    @Override
    public PageResponse<OrderChangelogResponse> paging(OrderChangelogPageRequest request) {
        Page<OrderChangelogDO> page = request.ofPage();
        LambdaQueryWrapper<OrderChangelogDO> queryWrapper = queryWrapper();
        queryWrapper.eq(Objects.nonNull(request.getOrderId()),OrderChangelogDO::getOrderId,request.getOrderId());
        queryWrapper.orderByDesc(OrderChangelogDO::getId);
        page = orderChangelogRepositoryService.page(page, queryWrapper);
        return PageResponse.build(page, OrderBuilder::toOrderChangelogResponse);
    }

    @Override
    public OrderChangelogSaveResponse add(OrderChangelogSaveRequest request) {
        boolean flag = orderChangelogRepositoryService.save(OrderBuilder.toOrderChangelogDO(request));
        return OrderChangelogSaveResponse.build(flag);
    }

    private LambdaQueryWrapper<OrderChangelogDO> queryWrapper() {
        return new LambdaQueryWrapper<>();
    }
}
