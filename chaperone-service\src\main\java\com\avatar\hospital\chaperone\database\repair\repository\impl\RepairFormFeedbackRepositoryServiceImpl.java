package com.avatar.hospital.chaperone.database.repair.repository.impl;

import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.repository.PartApplyRefPartBatchRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartApplyRepositoryService;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormFeedbackDO;
import com.avatar.hospital.chaperone.database.repair.mapper.RepairFormFeedbackMapper;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormFeedbackRepositoryService;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 报修单反馈 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
@RequiredArgsConstructor
public class RepairFormFeedbackRepositoryServiceImpl extends ServiceImpl<RepairFormFeedbackMapper, RepairFormFeedbackDO> implements RepairFormFeedbackRepositoryService {

    private final RepairFormRepositoryService repairFormRepositoryService;
    private final PartApplyRepositoryService partApplyRepositoryService;
    private final PartApplyRefPartBatchRepositoryService partApplyRefPartBatchRepositoryService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(RepairFormDO repairFormDO,RepairFormFeedbackDO feedback, PartApplyDO partApply, PartApplyRefPartBatchDO refPartBatchDO) {
        if (Objects.nonNull(repairFormDO)) {
            AssertUtils.isTrue(repairFormRepositoryService.updateById(repairFormDO), ErrorCode.INSERT_ERROR);
        }
        AssertUtils.isTrue(save(feedback), ErrorCode.INSERT_ERROR);
        if (Objects.nonNull(partApply)) {
            AssertUtils.isTrue(partApplyRepositoryService.save(partApply),ErrorCode.INSERT_ERROR);
        }
        if (Objects.nonNull(refPartBatchDO)) {
            AssertUtils.isTrue(partApplyRefPartBatchRepositoryService.save(refPartBatchDO),ErrorCode.INSERT_ERROR);
        }
    }

    @Override
    public List<RepairFormFeedbackDO> findByIdList(List<Long> feedbackIdList) {
        LambdaQueryWrapper<RepairFormFeedbackDO> queryWrapper = queryWrapper();
        queryWrapper.in(RepairFormFeedbackDO::getId,feedbackIdList);
        List<RepairFormFeedbackDO> list = list(queryWrapper);
        return list;
    }

    @Override
    public Map<Long, RepairFormFeedbackDO> getLastRepairFormFeedback(List<Long> formIdList) {
        if (CollUtils.isEmpty(formIdList)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<RepairFormFeedbackDO> queryWrapper = queryWrapper();
        queryWrapper.in(RepairFormFeedbackDO::getRepairFormId,formIdList);
        List<RepairFormFeedbackDO> list = list(queryWrapper);
        if (CollUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<Long, RepairFormFeedbackDO> collect = list.stream()
                .collect(Collectors.toMap(RepairFormFeedbackDO::getRepairFormId, Function.identity(),(v1, v2) -> v1.getId() > v2.getId() ? v1 : v2));
        return collect;
    }

    private LambdaQueryWrapper<RepairFormFeedbackDO> queryWrapper() {
        LambdaQueryWrapper<RepairFormFeedbackDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairFormFeedbackDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
