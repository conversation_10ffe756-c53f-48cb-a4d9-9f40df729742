package com.avatar.hospital.chaperone.request.device;

import com.avatar.hospital.chaperone.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author:sp0420
 * @Description:
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class DevicePagingRequest extends PageRequest {

    /**
     * 编号
     */
    private String code;

    /**
     * 所属院区
     */
    private Long orgId;

    /**
     * 状态（1-启用，2-停用，3-维修中）
     */
    private Integer status;

    /**
     * 名称
     */
    private String name;

    /**
     * 归属系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他,7-暖通系统，8-建筑系统，9-消防系统）
     */
    private Integer systemType;
}
