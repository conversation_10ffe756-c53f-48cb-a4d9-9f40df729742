package com.avatar.hospital.chaperone.database.repair.repository;

import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormTaskDO;
import com.avatar.hospital.chaperone.request.repair.RepairFormPageCRequest;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 报修单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface RepairFormRepositoryService extends IService<RepairFormDO> {

    /**
     * 添加
     * @param repairForm
     * @return
     */
    Long add(RepairFormDO repairForm);

    /**
     *
     * @param newRepairFormDO
     * @param repairFormTaskDOList
     * @return
     */
    Boolean assignExecutor(RepairFormDO newRepairFormDO, List<RepairFormTaskDO> repairFormTaskDOList);

    /**
     *
     * @param page
     * @param request
     * @return
     */
    Long countForC(Page<RepairFormDO> page, RepairFormPageCRequest request);

    /**
     *
     * @param page
     * @param request
     * @return
     */
    List<RepairFormDO> listForC(Page<RepairFormDO> page, RepairFormPageCRequest request);

    /**
     * 根据ID列表获取
     * @param repairFormId
     * @return
     */
    List<RepairFormDO> findAllByIdList(List<Long> repairFormId);

    /**
     * 获取年度报修及时性排行榜 前10
     * @param start
     * @param end
     * @return
     */
    List<RepairFormDO> getRepairTimelinessTop10(LocalDateTime start, LocalDateTime end);

    /**
     * 获取年度报修及时性排行榜 后10
     * @param start
     * @param end
     * @return
     */
    List<RepairFormDO> getRepairTimelinessBottom10(LocalDateTime start, LocalDateTime end);

    /**
     * 统计年度报修已完成数量,按系统类型
     * @param year
     * @return
     */
    Map<Integer, Integer> statisticsRepairStatusFinish(LocalDateTime start, LocalDateTime end);

    /**
     * 统计年度报修已完成数量,按系统类型
     * @param year
     * @return
     */
    Map<Integer, Integer> statisticsRepairStatusForYear(Integer year);
    /**
     * 统计年度报修进行中的数量,按系统类型
     * @param start
     * @param end
     * @return
     */
    Map<Integer, Integer> statisticsRepairStatusWork(LocalDateTime start, LocalDateTime end);

    /**
     * 统计月度报修已完成数量,按系统类型
     * @param month
     * @return
     */
    Map<Integer, Integer> statisticsRepairStatusForMonth(Integer month);

    Map<Long, RepairFormDO> getByIdList(List<Long> formIdList);
}
