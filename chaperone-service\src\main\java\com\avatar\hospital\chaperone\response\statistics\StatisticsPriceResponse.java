package com.avatar.hospital.chaperone.response.statistics;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 10:11
 **/
@Data
public class StatisticsPriceResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 年的数据
     */
    private DATA year;
    /**
     * 月的数据
     */
    private DATA month;


    @Data
    public static class DATA {
        /**
         * 总金额(单位分)
         */
        private Long totalPrice;

        /**
         * 未付款金额(单位分)
         */
        private Long unpay;

        public static final DATA build(Long totalPrice, Long unpay) {
            DATA obj = new DATA();
            obj.setTotalPrice(totalPrice);
            obj.setUnpay(unpay);
            return obj;
        }

    }

    public static final StatisticsPriceResponse build(Long totalPriceYear, Long unpayYear,
                                                      Long totalPriceMonth, Long unpayMonth) {
        StatisticsPriceResponse obj = new StatisticsPriceResponse();
        obj.setYear(DATA.build(totalPriceYear, unpayYear));
        obj.setMonth(DATA.build(totalPriceMonth, unpayMonth));
        return obj;
    }
}
