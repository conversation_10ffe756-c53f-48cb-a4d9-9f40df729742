package com.avatar.hospital.chaperone.database.part.dataobject;

import com.avatar.hospital.chaperone.database.part.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 备品备件明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_spare_part")
public class PartDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编号（批次编号+序列号）
     */
    private String code;

    /**
     * 批次ID
     */
    private Long sparePartBatchId;

    /**
     * 状态（0-待入库，1-可用，2-已使用）
     *
     * @see com.avatar.hospital.chaperone.database.part.enums.PartStatusType
     */
    private Integer status;

    /**
     * 入库申请单ID
     */
    private Long stockApplyId;

    /**
     * 入库时间
     */
    private LocalDateTime entryTime;

    /**
     * 备件使用申请单ID
     */
    private Long sparePartApplyId;

    /**
     * 出库时间
     */
    private LocalDateTime outTime;


}
