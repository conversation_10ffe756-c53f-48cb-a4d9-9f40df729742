package com.avatar.hospital.chaperone.request.plan;

import com.avatar.hospital.chaperone.annotation.serializer.LocalDateTimeDeserializer;
import com.avatar.hospital.chaperone.annotation.serializer.LocalDateTimeSerializer;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 14:14
 */
@Data
public class QueryRequest extends PageRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 884642518906736126L;
    private String code;
    private String name;
    /**
     * 计划id
     */
    private Long planId;

    /**
     * 计划类型
     */
    private PlanType planType;

    /**
     * 部门id
     */
    private Set<Long> orgIds;

    private Long accountId;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime stateTime;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    private Operator operatorUser;
}
