package com.avatar.hospital.chaperone.web.validator.baccount;

import com.avatar.hospital.chaperone.database.baccount.enums.OrganizationStatus;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.OrganizationAddRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationHospitalRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationTreeRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationUpdateRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
public class OrganizationValidator {

    private static final Integer DEFAULT_STATUS = OrganizationStatus.ENABLE.getStatus();

    private static final Integer DEFAULT_SORT = 100;

    public static void addValidate(OrganizationAddRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getName(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getParentId(), ErrorCode.PARAMETER_ERROR);
        if (request.getStatus() != null) {
            AssertUtils.isNotNull(OrganizationStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        }
        else {
            request.setStatus(DEFAULT_STATUS);
        }

        if (request.getSort() != null) {
            AssertUtils.isTrue(request.getSort() >= 0, ErrorCode.PARAMETER_ERROR);
        }
        else {
            request.setSort(DEFAULT_SORT);
        }

        // 设置创建人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setCreateBy(accountId);
        request.setUpdateBy(accountId);
    }

    public static void updateValidate(OrganizationUpdateRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getId(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getName(), ErrorCode.PARAMETER_ERROR);
        if (request.getStatus() != null) {
            AssertUtils.isNotNull(OrganizationStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        }
        if (request.getSort() != null) {
            AssertUtils.isTrue(request.getSort() >= 0, ErrorCode.PARAMETER_ERROR);
        }
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void deleteValidate(OrganizationDeleteRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.notEmpty(request.getIds(), ErrorCode.PARAMETER_ERROR);

        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void accountTreeValidate(OrganizationTreeRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getAccountId(), ErrorCode.PARAMETER_ERROR);
    }

    public static void hospitalOrganizationListValidate(OrganizationHospitalRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getAccountId(), ErrorCode.PARAMETER_ERROR);
    }
}
