package com.avatar.hospital.chaperone.web.validator.nursing;

import com.avatar.hospital.chaperone.database.baccount.enums.OrganizationStatus;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingStatus;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.nursing.NursingAddRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingUpdateRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;

/**
 * @author:sp0420
 * @Description:
 */
public class NursingValidator {
    private static final Integer DEFAULT_STATUS = NursingStatus.INVALID.getStatus();

    private static final Integer DEFAULT_SORT = 100;

    public static void addValidate(NursingAddRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getName(), ErrorCode.PARAMETER_ERROR);
        if (request.getStatus() != null) {
            AssertUtils.isNotNull(OrganizationStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        }
        else {
            request.setStatus(DEFAULT_STATUS);
        }

        // 设置创建人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setCreateBy(accountId);
        request.setUpdateBy(accountId);
    }

    public static void updateValidate(NursingUpdateRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getId(), ErrorCode.PARAMETER_ERROR);
        if (request.getStatus() != null) {
            AssertUtils.isNotNull(NursingStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        }
        else {
            request.setStatus(DEFAULT_STATUS);
        }

        // 设置创建人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void pagingValidate(NursingPagingRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageIndex(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageSize(), ErrorCode.PARAMETER_ERROR);
    }
}
