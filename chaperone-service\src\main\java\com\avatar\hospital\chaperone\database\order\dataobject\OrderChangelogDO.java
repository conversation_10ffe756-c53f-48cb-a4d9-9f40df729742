package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderLogEvent;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单-修改记录表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_order_changelog")
public class OrderChangelogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 订单ID
     */
      private Long orderId;

      /**
     * 时间
     */
      private String time;

      /**
     * 修改事件
       * @see OrderLogEvent
     */
      private String eventCode;

      /**
     * 修改前
     */
      private String beforeValue;

      /**
     * 修改后
     */
      private String afterValue;

  /**
   * 操作人姓名
   */
      private String operatorName;
  /**
   * 操作人手机号
   */
  private String operatorMobile;

  /**
   * 操作人来源 1 c端 2 b端 3 定时任务
   */
  private Integer operatorSource;

}
