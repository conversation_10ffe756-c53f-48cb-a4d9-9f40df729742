package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.database.order.enums.OrderLogEvent;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.utils.DateUtils;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderChangelogSaveRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单事件
     */
    private OrderLogEvent event;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 之前
     */
    private String before;

    /**
     * 之后
     */
    private String after;

    /**
     * 业务日期时间
     */
    private String time;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    /**
     * 构造请求
     * @param time
     * @param event
     * @param before
     * @param after
     * @return
     */
    public static final OrderChangelogSaveRequest build(Long orderId,String time,OrderLogEvent event,String before, String after, Operator operator) {
        OrderChangelogSaveRequest obj = new OrderChangelogSaveRequest();
        obj.setOrderId(orderId);
        obj.setEvent(event);
        obj.setBefore(before);
        obj.setAfter(after);
        obj.setTime(time);
        obj.setOperatorUser(operator);
        return obj;
    }
    public static final OrderChangelogSaveRequest build(Long orderId, Operator operator, OrderLogEvent event, String before, String after) {
        return build(orderId,DateUtils.dateTimeStr(),event,before,after,operator);
    }

}
