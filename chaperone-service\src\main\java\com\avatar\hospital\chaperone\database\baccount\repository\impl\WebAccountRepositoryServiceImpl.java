package com.avatar.hospital.chaperone.database.baccount.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountOrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountRoleDO;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.baccount.mapper.WebAccountMapper;
import com.avatar.hospital.chaperone.database.baccount.repository.AccountOrganizationRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.AccountRoleRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.WebAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.DefaultUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * B端用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WebAccountRepositoryServiceImpl extends ServiceImpl<WebAccountMapper, AccountDO> implements WebAccountRepositoryService {

    private final AccountOrganizationRepositoryService accountOrganizationRepositoryService;
    private final AccountRoleRepositoryService accountRoleRepositoryService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long add(AccountDO accountDO, List<AccountOrganizationDO> accountOrganizationList) {
        if (accountDO == null || CollectionUtils.isEmpty(accountOrganizationList)) {
            return null;
        }
        accountDO.setId(IdUtils.getId());
        if (!save(accountDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        accountOrganizationList.forEach(accountOrganization -> accountOrganization.setAccountId(accountDO.getId()));
        if (!accountOrganizationRepositoryService.saveBatch(accountOrganizationList)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        return accountDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean incrementUpdate(AccountDO accountDO, List<AccountOrganizationDO> accountOrganizationList) {
        if (accountDO == null || CollectionUtils.isEmpty(accountOrganizationList)) {
            return false;
        }
        LambdaUpdateWrapper<AccountDO> updateWrapper = updateWrapper();
        updateWrapper.set(accountDO.getNickname() != null, AccountDO::getNickname, accountDO.getNickname());
        updateWrapper.set(accountDO.getPhoneNumber() != null, AccountDO::getPhoneNumber, accountDO.getPhoneNumber());
        updateWrapper.set(accountDO.getAvatarUrl() != null, AccountDO::getAvatarUrl, accountDO.getAvatarUrl());
        updateWrapper.set(accountDO.getStatus() != null, AccountDO::getStatus, accountDO.getStatus());
        updateWrapper.set(accountDO.getType() != null, AccountDO::getType, accountDO.getType());
        updateWrapper.set(accountDO.getLastLoginIp() != null, AccountDO::getLastLoginIp, accountDO.getLastLoginIp());
        updateWrapper.set(accountDO.getLastLoginDate() != null, AccountDO::getLastLoginDate, accountDO.getLastLoginDate());
        updateWrapper.set(accountDO.getPasswordUpdateDate() != null, AccountDO::getPasswordUpdateDate, accountDO.getPasswordUpdateDate());
        updateWrapper.set(accountDO.getAdmin() != null, AccountDO::getAdmin, accountDO.getAdmin());
        updateWrapper.set(accountDO.getUpdateBy() != null, AccountDO::getUpdateBy, accountDO.getUpdateBy());

        updateWrapper.eq(AccountDO::getId, accountDO.getId());

        if (!update(updateWrapper)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        if (!Boolean.TRUE.equals(accountOrganizationRepositoryService.deleteByAccountIds(Sets.newHashSet(accountDO.getId())))) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        if (!accountOrganizationRepositoryService.saveBatch(accountOrganizationList)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        return true;
    }

    @Override
    public Boolean updateLoginInformation(Long accountId, String lastLoginIp, LocalDateTime lastLoginDate) {
        if (accountId == null) {
            return false;
        }
        LambdaUpdateWrapper<AccountDO> updateWrapper = updateWrapper();
        updateWrapper.set(lastLoginIp != null, AccountDO::getLastLoginIp, lastLoginIp);
        updateWrapper.set(lastLoginDate != null, AccountDO::getLastLoginDate, lastLoginDate);
        updateWrapper.eq(AccountDO::getId, accountId);
        return update(updateWrapper);
    }

    @Override
    public Boolean changePassword(Long accountId, String newPassword, Long updateBy) {
        if (accountId == null || newPassword == null || updateBy == null) {
            return false;
        }
        LambdaUpdateWrapper<AccountDO> updateWrapper = updateWrapper();
        updateWrapper.set(AccountDO::getPassword, newPassword);
        updateWrapper.set(AccountDO::getPasswordUpdateDate, LocalDateTime.now());
        updateWrapper.set(AccountDO::getUpdateBy, updateBy);
        updateWrapper.eq(AccountDO::getId, accountId);
        return update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByIds(Set<Long> ids, Long updateBy) {
        if (CollectionUtils.isEmpty(ids) || updateBy == null) {
            return true;
        }
        LambdaUpdateWrapper<AccountDO> updateWrapper = updateWrapper();
        updateWrapper.set(AccountDO::getDeleted, System.currentTimeMillis());
        updateWrapper.set(AccountDO::getUpdateBy, updateBy);
        if (ids.size() == 1) {
            updateWrapper.eq(AccountDO::getId, ids.toArray()[0]);
        }
        else {
            updateWrapper.in(AccountDO::getId, ids);
        }
        if (!update(updateWrapper)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        // 删除关联数据
        List<AccountOrganizationDO> accountOrganizationList = accountOrganizationRepositoryService.findByAccountIds(ids);
        if (CollectionUtils.isNotEmpty(accountOrganizationList)) {
            if (!Boolean.TRUE.equals(accountOrganizationRepositoryService.deleteByAccountIds(
                    accountOrganizationList.stream().map(AccountOrganizationDO::getAccountId).collect(Collectors.toSet())
            ))) {
                throw BusinessException.of(ErrorCode.UPDATE_ERROR);
            }
        }
        List<AccountRoleDO> accountRoleList = accountRoleRepositoryService.findByAccountIds(ids);
        if (CollectionUtils.isNotEmpty(accountRoleList)) {
            if (!Boolean.TRUE.equals(accountRoleRepositoryService.deleteByAccountIds(
                    accountRoleList.stream().map(AccountRoleDO::getAccountId).collect(Collectors.toSet())
            ))) {
                throw BusinessException.of(ErrorCode.UPDATE_ERROR);
            }
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean allocationRole(Long accountId, Set<Long> roleIds) {
        if (accountId == null || CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        List<AccountRoleDO> accountRoleList = accountRoleRepositoryService.findByAccountIds(Sets.newHashSet(accountId));
        if (CollectionUtils.isNotEmpty(accountRoleList)) {
            if (!Boolean.TRUE.equals(accountRoleRepositoryService.deleteByAccountIds(Sets.newHashSet(accountId)))) {
                throw BusinessException.of(ErrorCode.UPDATE_ERROR);
            }
        }

        List<AccountRoleDO> insertAccountRoleList = Lists.newArrayListWithCapacity(roleIds.size());
        roleIds.forEach(roleId -> {
            AccountRoleDO accountRoleDO = new AccountRoleDO();
            accountRoleDO.setAccountId(accountId);
            accountRoleDO.setRoleId(roleId);
            insertAccountRoleList.add(accountRoleDO);
        });

        if (!accountRoleRepositoryService.saveBatch(insertAccountRoleList)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        return true;
    }

    @Override
    public AccountDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<AccountDO> queryWrapper = queryWrapper();
        queryWrapper.eq(AccountDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<AccountDO> findByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AccountDO> queryWrapper = queryWrapper();
        if (ids.size() == 1) {
            queryWrapper.eq(AccountDO::getId, ids.toArray()[0]);
        }
        else {
            queryWrapper.in(AccountDO::getId, ids);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public AccountDO findByPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return null;
        }
        LambdaQueryWrapper<AccountDO> queryWrapper = queryWrapper();
        queryWrapper.eq(AccountDO::getPhoneNumber, phoneNumber);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public PageResponse<AccountDO> paging(Integer pageIndex, Integer pageSize, AccountDO accountDO) {
        int index = DefaultUtils.ifNullDefault(pageIndex, 1);
        int size = DefaultUtils.ifNullDefault(pageSize, 10);
        LambdaQueryWrapper<AccountDO> queryWrapper = queryWrapper();
        if (accountDO != null) {
            queryWrapper.eq(StringUtils.isNotBlank(accountDO.getPhoneNumber()), AccountDO::getPhoneNumber, accountDO.getPhoneNumber());
            queryWrapper.like(StringUtils.isNotBlank(accountDO.getNickname()), AccountDO::getNickname, accountDO.getNickname());
        }
        IPage<AccountDO> page = baseMapper.selectPage(new Page<>(index, size), queryWrapper);
        return PageResponse.build(page.getTotal(), page.getCurrent(), page.getSize(), page.getRecords());
    }

    @Override
    public Map<Long, String> findMapByIdList(List<Long> idList) {
        if (CollUtils.isEmpty(idList)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<AccountDO> queryWrapper = queryWrapper();
        queryWrapper.select(AccountDO::getId,AccountDO::getNickname);
        queryWrapper.in(AccountDO::getId,idList);
        List<AccountDO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream()
                .collect(Collectors.toMap(AccountDO::getId, AccountDO::getNickname));
    }

    @Override
    public String findNameById(Long id) {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AccountDO::getId,AccountDO::getNickname);
        queryWrapper.eq(AccountDO::getId,id);
        AccountDO accountDO = getOne(queryWrapper);
        if (Objects.isNull(accountDO)) {
            return null;
        }
        return accountDO.getNickname();
    }


    private LambdaQueryWrapper<AccountDO> queryWrapper() {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<AccountDO> updateWrapper() {
        LambdaUpdateWrapper<AccountDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AccountDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }

}
