package com.avatar.hospital.chaperone.database.baccount.repository;

import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface RoleRepositoryService extends IService<RoleDO> {

    /**
     * 添加角色
     *
     * @param roleDO -
     * @return -
     */
    Long add(RoleDO roleDO);

    /**
     * 增量更新角色
     *
     * @param roleDO -
     * @return -
     */
    Boolean incrementUpdate(RoleDO roleDO);

    /**
     * 根据id批量删除
     *
     * @param ids      -
     * @param updateBy -
     * @return -
     */
    Boolean deleteByIds(Set<Long> ids, Long updateBy);

    /**
     * 分配菜单权限
     *
     * @param roleId  -
     * @param menuIds -
     * @return -
     */
    Boolean allocationMenu(Long roleId, Set<Long> menuIds);

    /**
     * 根据ID查询
     *
     * @param id -
     * @return -
     */
    RoleDO findById(Long id);

    /**
     * 根据ID批量查询
     *
     * @param ids -
     * @return -
     */
    List<RoleDO> findByIds(Set<Long> ids);

    /**
     * 分页查询角色
     *
     * @param pageIndex -
     * @param pageSize  -
     * @param roleDO    -
     * @return -
     */
    PageResponse<RoleDO> paging(Integer pageIndex, Integer pageSize, RoleDO roleDO);

    /**
     * 根据name查询
     *
     * @param name -
     * @return -
     */
    RoleDO findByName(String name);

    /**
     * 根据roleKey查询
     *
     * @param roleKey -
     * @return -
     */
    RoleDO findByRoleKey(String roleKey);
}
