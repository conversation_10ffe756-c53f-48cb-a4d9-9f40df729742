package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 16:47
 **/
@Data
public class OrderModifyNursingRequest implements OperatorReq,Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    /**
     * 订单ID
     */
    @NotNull
    private Long orderId;


    @Deprecated
    @NotNull
    private Long nursingId;

    /**
     * 多个护工ID列表
     */
    private List<Long> nursingIdList;

    /**
     * 生效时间(时间戳) -1 清除之前预设置 0 表示立即 时间戳戳:指定时间戳戳
     */
    private Long presetTime;

    /**
     * 时间 yyyyMMddHH
     */
    private Integer time;
    /**
     * 是否立即
     * @return
     */
    public Boolean now() {
        return Objects.isNull(presetTime) || Objects.equals(0L,presetTime);
    }


    /**
     * 清除之前的预约
     * @return
     */
    public Boolean clean() {
        return Objects.nonNull(presetTime) && Objects.equals(-1L,presetTime);
    }
}
