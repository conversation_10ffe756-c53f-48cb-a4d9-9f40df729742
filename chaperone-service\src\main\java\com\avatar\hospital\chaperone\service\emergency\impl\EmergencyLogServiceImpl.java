package com.avatar.hospital.chaperone.service.emergency.impl;

import com.avatar.hospital.chaperone.builder.emergency.EmergencyLogBuilder;
import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.repository.OrganizationRepositoryService;
import com.avatar.hospital.chaperone.database.emergencylog.dataobject.EmergencyHandlingLogDO;
import com.avatar.hospital.chaperone.database.emergencylog.repository.EmergencyHandlingLogRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanDO;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogAddRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogPagingRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogAddResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogPagingResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogUpdateResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse;
import com.avatar.hospital.chaperone.service.emergency.EmergencyLogService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.template.util.DefaultUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class EmergencyLogServiceImpl implements EmergencyLogService {
    private final EmergencyHandlingLogRepositoryService emergencyHandlingLogRepositoryService;
    private final OrganizationRepositoryService organizationRepositoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EmergencyLogAddResponse add(EmergencyLogAddRequest request) {
        //新增日志信息
        Long id = emergencyHandlingLogRepositoryService.add(EmergencyLogBuilder.buildEmergencyLogDO(request));
        return EmergencyLogAddResponse.builder().id(id).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EmergencyLogUpdateResponse update(EmergencyLogUpdateRequest request) {
        EmergencyHandlingLogDO date = emergencyHandlingLogRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(date, ErrorCode.NURSING_NOT_EXIST);
        Boolean result = emergencyHandlingLogRepositoryService.incrementUpdate(EmergencyLogBuilder.buildEmergencyLogDO(request));
        return EmergencyLogUpdateResponse.builder().success(result).build();
    }

    @Override
    public PageResponse<EmergencyLogPagingResponse> paging(EmergencyLogPagingRequest request) {
        PageResponse<EmergencyHandlingLogDO> paging = emergencyHandlingLogRepositoryService.paging(request);

        //封装组织机构信息
        PageResponse<EmergencyLogPagingResponse> pageResponse = EmergencyLogBuilder.buildEmergencyLogDO(paging);
        if (Objects.nonNull(pageResponse) && !CollectionUtils.isEmpty(pageResponse.getRecords())) {
            List<EmergencyLogPagingResponse> records = pageResponse.getRecords();
            Set<Long> orgIdList = records.stream().map(EmergencyLogPagingResponse::getOrgId).collect(Collectors.toSet());
            List<OrganizationDO> organizationDOList = organizationRepositoryService.findByIds(orgIdList);
            if (!CollectionUtils.isEmpty(organizationDOList)){
                Map<Long, OrganizationDO> longOrganizationDOMap = organizationDOList.stream().collect(Collectors.toMap(OrganizationDO::getId, Function.identity()));
                records.forEach(i->{
                    if (Objects.nonNull(longOrganizationDOMap.get(i.getOrgId()))){
                        i.setOrgName(longOrganizationDOMap.get(i.getOrgId()).getName());
                    }
                });
            }
        }
        return pageResponse;
    }
}
