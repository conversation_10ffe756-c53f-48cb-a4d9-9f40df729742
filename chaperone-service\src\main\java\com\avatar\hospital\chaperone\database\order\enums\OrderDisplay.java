package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import lombok.Getter;

import java.util.Objects;

/**
 * Description:
 *  用户是否可见
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderDisplay implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    DISPLAY(1, "可见"),
    NO_DISPLAY(0, "不可见"),


    ;

    private final Integer status;

    private final String describe;


    OrderDisplay(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderDisplay of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderDisplay itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
    @Override
    public String convertDesc(Integer val) {
        OrderDisplay e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }
}
