package com.avatar.hospital.chaperone.response.order;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description: 订单简易信息
 * @author: sp0372
 * @create: 2023-10-11 16:55
 **/
@Data
public class CashIdResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    public static final CashIdResponse build(Long id) {
        CashIdResponse obj = new CashIdResponse();
        obj.setId(id);
        return obj;
    }

}
