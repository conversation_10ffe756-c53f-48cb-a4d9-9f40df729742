package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderBillDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单主表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderRepositoryService extends IService<OrderDO> {

    Long create(OrderDO orderDO, OrderBillDO orderBillDO, List<OrderItemDO> orderItemDO);

    /**
     * 查询所有需要结算的账单
     *      还未结束完成
     *      实际结束日期 已经过去(小于当前结束日期)
     * @return
     */
    List<OrderDO> findAllNeedSettle(Integer dateHour);

    void saveByModifyItem(OrderDO newOrder, OrderBillDO newTotalBill, List<OrderItemDO> list);

    /**
     * 通过 订单ID,账号ID获取
     * @param orderId
     * @return
     */
    OrderDO getByIdAndAccountId(Long orderId,Long accountId);

    /**
     * 获取所有订单信息
     * @param nursingId
     * @param orderId
     * @param startDate yyyyMMdd
     * @param endDate yyyyMMdd
     * @return
     */
    List<OrderDO> findAllByNursingId(Long nursingId, Long orderId, Integer startDate, Integer endDate);

    /**
     * 根据id列表查询订单信息
     * @param orderId
     * @return
     */
    List<OrderDO> findByIdList(List<Long> orderId);

    /**
     * 完善信息
     * @param orderEntity
     * @param totalBill
     * @param itemList
     */
    void commitCompleteInfo(OrderDO orderEntity, OrderBillDO totalBill, List<OrderItemDO> itemList);

    /**
     * 修改护工
     * @param totalBill
     * @param orderEntity
     */
    void modifyDiscount(OrderBillDO totalBill, OrderDO orderEntity);

    /**
     *
     * @param orderId
     * @return
     */
    OrderDO findById(Long orderId);

    /**
     * 获取没有陪护,
     *   且已经过结束时间的订单
     * @param dateHour
     * @return
     */
    List<OrderDO> findAllExpire(Integer dateHour);

    Map<Long, OrderDO> listRef(List<Long> orderIdList);

}
