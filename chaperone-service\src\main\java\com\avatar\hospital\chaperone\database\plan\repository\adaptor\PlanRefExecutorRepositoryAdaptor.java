package com.avatar.hospital.chaperone.database.plan.repository.adaptor;

import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.repository.MaintenancePlanRefExecutorRepositoryService;
import com.avatar.hospital.chaperone.database.plan.repository.PatrolPlanRefExecutorRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 巡检计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Component
public class PlanRefExecutorRepositoryAdaptor {

    @Autowired
    public PatrolPlanRefExecutorRepositoryService patrolPlanRefRepositoryService;
    @Autowired
    public MaintenancePlanRefExecutorRepositoryService maintenancePlanRefRepositoryService;

    public boolean saveBatch(PlanType planType, List planRefDOs) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRefRepositoryService.saveBatch(planRefDOs);

        }
        return maintenancePlanRefRepositoryService.saveBatch(planRefDOs);
    }

    public boolean update2delete(PlanRequest request) {

        if (PlanType.PATROL.equals(request.planType)) {
            return patrolPlanRefRepositoryService.update2delete(request);

        }
        return maintenancePlanRefRepositoryService.update2delete(request);
    }

    public Set<Long> getRefIds(PlanType planType, Set<Long> ids) {

        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRefRepositoryService.getRefIds(ids);

        }
        return maintenancePlanRefRepositoryService.getRefIds(ids);
    }

    public List<Long> getRefIds(PlanType planType, Long id) {

        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRefRepositoryService.getRefIds(id);
        }
        return maintenancePlanRefRepositoryService.getRefIds(id);
    }

    public List list(PlanType planType, LambdaQueryWrapper queryWrapper) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRefRepositoryService.list(queryWrapper);

        }
        return maintenancePlanRefRepositoryService.list(queryWrapper);
    }

    public Set<Long> getPlanByAccount(PlanType planType, Long accountId) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanRefRepositoryService.getPlanByAccount(accountId);

        }
        return maintenancePlanRefRepositoryService.getPlanByAccount(accountId);
    }

}
