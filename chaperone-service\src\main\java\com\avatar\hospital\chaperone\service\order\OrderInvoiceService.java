package com.avatar.hospital.chaperone.service.order;

import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderInvoiceIdResponse;
import com.avatar.hospital.chaperone.response.order.OrderInvoiceResponse;

import java.util.List;
import java.util.Map;

/**
 * @program: hospital-chaperone
 * @description: 订单发票信息
 * @author: sp0372
 * @create: 2023-10-11 16:57
 **/
public interface OrderInvoiceService {

    /**
     * 保存订单发票信息
     * @param request
     * @return
     */
    OrderInvoiceIdResponse create(OrderInvoiceCreateRequest request);



    /**
     * 保存订单发票信息
     * @param request
     * @return
     */
    PageResponse<OrderInvoiceResponse>  paging(OrderInvoicePageRequest request);

    // inner

    /**
     * 通过账单ID查询发票信息
     * @param billId
     * @return
     */
    OrderInvoiceResponse getByBillId(Long billId);


    /**
     * 通过账单ID查询发票信息
     * @param billId
     * @return
     */
    Map<Long,OrderInvoiceResponse> getByBillIdList(List<Long> billId);
}
