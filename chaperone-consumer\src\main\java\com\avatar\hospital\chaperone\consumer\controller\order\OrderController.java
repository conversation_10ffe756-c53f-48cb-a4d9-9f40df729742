package com.avatar.hospital.chaperone.consumer.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.consumer.utils.ConsumerAccountUtils;
import com.avatar.hospital.chaperone.consumer.validator.order.OrderValidator;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.OrderService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * C端陪护单-订单
 * @program: hospital-chaperone
 * @description: 订单|C端
 * @author: sp0372
 * @create: 2023-10-13 15:04
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/order/base")
public class OrderController {
    private final OrderService orderService;

    /**
     * 创建
     */
    @PostMapping("create")
    public SingleResponse<OrderIdResponse> create(@RequestBody OrderCCreateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.createValidator(request);
            OrderCreateRequest realRequest = new OrderCreateRequest();
            BeanUtils.copyProperties(request,realRequest);
            return orderService.create(realRequest);
        });
    }

    /**
     * 查询-分页
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<OrderResponse>> paging(@RequestBody OrderCPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.pagingValidator(request);
            return orderService.pagingForC(request);
        });
    }

    /**
     * 查询-分页full
     */
    @PostMapping("paging/full")
    public SingleResponse<PageResponse<OrderDetailResponse>> pagingFull(@RequestBody OrderCPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.pagingValidator(request);
            return orderService.pagingForCFull(request);
        });
    }

    /**
     * 查询-详情
     */
    @PostMapping("detail")
    public SingleResponse<OrderDetailResponse> detail(@RequestBody OrderRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderService.getById(request);
        });
    }

    /**
     * 价格计算
     */
    @PostMapping("calculation")
    public SingleResponse<OrderPriceCalculationResponse> priceCalculation(@RequestBody OrderPriceCalculationRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderService.priceCalculation(request);
        });
    }

    /**
     * 取消
     */
    @PostMapping("cancel")
    public SingleResponse<OrderIdResponse> cancel(@RequestBody OrderRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderService.cancel(request);
        });
    }


    /**
     * 确认
     */
    @PostMapping("confirm")
    public SingleResponse<OrderIdResponse> confirm(@RequestBody OrderRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderService.confirm(request);
        });
    }


    /**
     * 结算申请
     */
    @PostMapping("settle")
    public SingleResponse<OrderIdResponse> settle(@RequestBody OrderRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderService.applySettle(request);
        });
    }

}
