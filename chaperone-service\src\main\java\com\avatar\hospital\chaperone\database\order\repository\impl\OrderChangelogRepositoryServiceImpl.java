package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderChangelogDO;
import com.avatar.hospital.chaperone.database.order.mapper.OrderChangelogMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderChangelogRepositoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 订单-修改记录表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class OrderChangelogRepositoryServiceImpl extends ServiceImpl<OrderChangelogMapper, OrderChangelogDO> implements OrderChangelogRepositoryService {

}
