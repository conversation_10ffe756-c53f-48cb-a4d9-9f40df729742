package com.avatar.hospital.chaperone.database.nursing.enums;

import lombok.Getter;

/**
 * @author:sp0420
 * @Description:
 */
@Getter
public enum NursingLogStatus {
    NONE(-1,"无状态"),
    REPLACE(0, "替班"),
    LEAVE(1, "请假"),
    CANCEL_LEAVE(2, "取消请假"),
    ;

    private final Integer status;

    private final String describe;


    NursingLogStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static NursingLogStatus of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (NursingLogStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
