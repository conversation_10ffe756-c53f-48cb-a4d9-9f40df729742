package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 现金记录表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Getter
@Setter
  @TableName("t_cash_log")
public class CashLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 类型，1 增加，2提取
     */
      private Integer type;

  /**
   * 金额类型 1 现金 3 支付宝
   */
  private Integer cashSource;

      /**
     * 金额 单位分
     */
      private Integer price;

      /**
     * 业务类型，1 账单现金提取 0 人工提取
     */
      private Integer bizType;

      /**
     * 业务ID(账单编号)
     */
      private Long bizId;

      /**
     * 提取人ID
     */
      private Long personId;

      /**
     * 提取人姓名
     */
      private String personName;


}
