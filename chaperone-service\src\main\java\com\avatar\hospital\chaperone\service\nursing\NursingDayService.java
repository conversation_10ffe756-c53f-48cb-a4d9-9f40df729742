package com.avatar.hospital.chaperone.service.nursing;

import com.avatar.hospital.chaperone.request.nursing.NursingDayExportPageRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingDayPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingLeaveRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingSubstituteRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse;
import com.avatar.hospital.chaperone.service.nursing.dto.NursingDayExportDTO;
import com.avatar.hospital.chaperone.utils.ExportParamDTO;

import java.util.List;

/**
 * @program: hospital-chaperone
 * @description: 护工考勤接口
 * @author: sp0372
 * @create: 2023-10-11 17:23
 **/
public interface NursingDayService {

    PageResponse<NursingDayPagingResponse> paging(NursingDayPagingRequest request);

    Boolean leave(NursingLeaveRequest request);

    Boolean cancelLeave(NursingLeaveRequest request);

    Boolean substitute(NursingSubstituteRequest request);

    List<NursingDayExportDTO> export(NursingDayExportPageRequest request);

    // C端
    /**
     * 查询护工排班接口
     *     根据订单
     */

    // B端

    /**
     * 通过日期查询所有护工的排班情况下
     */

    /**
     * 提前按订单维度生成护工排班数据
     */
}
