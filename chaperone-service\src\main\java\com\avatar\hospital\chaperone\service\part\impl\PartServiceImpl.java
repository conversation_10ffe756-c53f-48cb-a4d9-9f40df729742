package com.avatar.hospital.chaperone.service.part.impl;

import com.avatar.hospital.chaperone.builder.part.PartBatchBuilder;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.repository.PartRepositoryService;
import com.avatar.hospital.chaperone.request.part.PartBatchRequest;
import com.avatar.hospital.chaperone.request.part.QueryExportRequest;
import com.avatar.hospital.chaperone.request.part.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.service.part.dto.PartExportDTO;
import com.avatar.hospital.chaperone.response.part.PartVO;
import com.avatar.hospital.chaperone.service.part.PartService;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 备件服务
 * @date 2023/10/27 15:09
 */
@Service
public class PartServiceImpl implements PartService {
    @Autowired
    PartRepositoryService partRepositoryService;

    @Override
    public boolean update2delete(PartBatchRequest partBatchRequest) {

        LambdaUpdateWrapper<PartDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PartDO::getSparePartBatchId, partBatchRequest.getId());
        updateWrapper.set(PartDO::getUpdateBy, partBatchRequest.getOperator());
        updateWrapper.set(PartDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.setSql(" deleted = id");
        partRepositoryService.update(updateWrapper);
        return true;
    }

    @Override
    public PageResponse<PartVO> paging(QueryRequest request) {
        if (!CollUtils.isEmpty(request.getOutTime())) {
            request.setOutTimeStart(DateUtils.parseForIntD(request.getOutTime().get(0)));
            request.setOutTimeEnd(DateUtils.parseForIntD(request.getOutTime().get(1)));
        }
        Page<PartDO> page = request.ofPage();
        LambdaQueryWrapper<PartDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(PartDO::getId);
        queryWrapper.eq(Objects.nonNull(request.getBatchId()), PartDO::getSparePartBatchId, request.getBatchId());
        queryWrapper.eq(Objects.nonNull(request.getCode()), PartDO::getCode, request.getCode());
        queryWrapper.eq(Objects.nonNull(request.getStatus()), PartDO::getStatus, request.getStatus());
        queryWrapper.ge(Objects.nonNull(request.getOutTimeStart()), PartDO::getOutTime, request.getOutTimeStart());
        queryWrapper.le(Objects.nonNull(request.getOutTimeEnd()), PartDO::getOutTime, request.getOutTimeEnd());
        page = partRepositoryService.page(page, queryWrapper);

        PageResponse<PartVO> pageResponse = PageResponse.build(page, PartBatchBuilder::buildVO);
        PartBatchBuilder.setPartValue(pageResponse.getRecords());
        PartBatchBuilder.setPartStockValue(pageResponse.getRecords());
        return pageResponse;
    }

    @Override
    public List<PartExportDTO> export(QueryExportRequest request) {
        QueryRequest req = toQueryRequest(request);
        PageResponse<PartVO> panging = paging(req);
        List<PartExportDTO> list = toPartExportVOList(panging.getRecords());
        return list;
    }

    private List<PartExportDTO> toPartExportVOList(List<PartVO> records) {
        List<PartExportDTO> list = records.stream()
                .map(part -> toPartExportDTO(part))
                .collect(Collectors.toList());
        return list;
    }

    private PartExportDTO toPartExportDTO(PartVO part) {
        PartExportDTO partExportDTO = new PartExportDTO();
        partExportDTO.setCode(part.getCode());
        partExportDTO.setBatchCode(part.getBatchCode());
        partExportDTO.setName(part.getName());
        partExportDTO.setPrice(part.getPrice());
        partExportDTO.setStatus(part.getStatus());
        partExportDTO.setEntryTime(part.getEntryTime());
        partExportDTO.setStockApplyId(part.getStockApplyId());
        partExportDTO.setInvestorType(part.getInvestorType());
        partExportDTO.setInvestor(part.getInvestor());
        return partExportDTO;
    }


    private QueryRequest toQueryRequest(QueryExportRequest request) {
        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setCode(request.getCode());
        queryRequest.setName(request.getName());
        queryRequest.setBatchId(request.getBatchId());
        queryRequest.setStatus(request.getStatus());
        queryRequest.setOutTimeStart(request.getOutTimeStart());
        queryRequest.setOutTimeEnd(request.getOutTimeEnd());
        queryRequest.setPageIndex(request.getPageIndex());
        queryRequest.setPageSize(request.getPageSize());
        queryRequest.setOutTime(request.getOutTime());
        return queryRequest;
    }

    @Override
    public Boolean entryStock(LocalDateTime entryTime, Long stockApplyId, PartBatchDO partBatchDO, PartStockApplyRefPartBatchDO partBatch, Long operator) {

        partRepositoryService.saveBatch(PartBatchBuilder.build(stockApplyId, entryTime, partBatchDO, partBatch, operator));

        return true;
    }

    private LambdaQueryWrapper<PartDO> queryWrapper() {
        LambdaQueryWrapper<PartDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PartDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<PartDO> updateWrapper() {
        LambdaUpdateWrapper<PartDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PartDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
