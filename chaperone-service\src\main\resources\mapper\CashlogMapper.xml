<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.order.mapper.CashLogMapper">

    <select id="sumByType" resultType="java.lang.Integer">
        select sum(price) from t_cash_log
        where deleted  = 0
        and type = #{type}
    </select>

    <select id="sumByTypeAndSource" resultType="java.lang.Integer">
        select sum(price) from t_cash_log
        where deleted  = 0
        and type = #{type}
        and cash_source = #{cashSource}
    </select>
</mapper>