package com.avatar.hospital.chaperone.response.statistics;

import com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 10:12
 **/
@Data
public class StatisticsFaultTopResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 年度维修次数前10
     */
    private List<DAT> yearTop10;
    /**
     * 年度维修次数后10
     */
    private List<DAT> yearBottom10;
    /**
     * 月度维修次数前10
     */
    private List<DAT> monthTop10;
    /**
     * 月度维修次数后10
     */
    private List<DAT> monthBottom10;

    @Data
    public static class DAT implements Serializable {
        /**
         * 设备ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 设备code
         */
        private String code;

        /**
         * 设备名称
         */
        private String name;

        /**
         * 报修数量
         */
        private Integer num;

        public DAT(Long id, String code, String name, Integer num) {
            this.id = id;
            this.code = code;
            this.name = name;
            this.num = num;
        }

        public static final List<DAT> of(List<ProjectDeviceDO> list) {
            return list.stream()
                    .map(item -> new DAT(item.getId(), item.getCode(), item.getName(), item.getStatus()))
                    .collect(Collectors.toList());
        }
    }
}
