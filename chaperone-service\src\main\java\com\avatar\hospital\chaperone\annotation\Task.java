package com.avatar.hospital.chaperone.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface Task {
    /**
     * 任务code
     * @return
     */
    String value();

    /**
     * 描述
     * @return
     */
    String description();
}
