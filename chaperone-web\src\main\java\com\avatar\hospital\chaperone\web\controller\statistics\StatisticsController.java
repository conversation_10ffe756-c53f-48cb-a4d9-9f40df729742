package com.avatar.hospital.chaperone.web.controller.statistics;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Cache;
import com.avatar.hospital.chaperone.request.statistics.StatisticsRequest;
import com.avatar.hospital.chaperone.response.statistics.*;
import com.avatar.hospital.chaperone.service.statistics.StatisticsNursingService;
import com.avatar.hospital.chaperone.service.statistics.StatisticsService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 统计
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 16:12
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/statistics")
public class StatisticsController {
    private final StatisticsService statisticsService;
    private final StatisticsNursingService statisticsNursingService;

    /**
     * 部件使用统计
     * @param request
     * @return
     */
    @GetMapping("/part-use")
    @Cache("'part-use' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsPartUseResponse> getPartUse(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsService.getPartUse(request));
    }

    /**
     * 应急处理统计
     * @param request
     * @return
     */
    @GetMapping("/emergency-handling")
    @Cache("'emergency-handling' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsEmergencyHandlingResponse> getEmergencyHandling(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsService.getEmergencyHandling(request));
    }
    /**
     * 任务统计
     * @param request
     * @return
     */
    @GetMapping("/task")
    @Cache("'task' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsTaskResponse> getTask(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsService.getTask(request));
    }

    /**
     * 处置及时性排行榜（前10工单，后10工单）
     * @param request
     * @return
     */
    @GetMapping("/repair-timeliness-top")
    @Cache("'repair-timeliness-top' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsRepairTimelinessTopResponse> getRepairTimelinessTop(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsService.getRepairTimelinessTop(request));
    }

    /**
     * 处置状态统计
     * @param request
     * @return
     */
    @GetMapping("/repair-status")
    @Cache("'repair-status' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsRepairStatusResponse> getRepairStatus(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsService.getRepairStatus(request));
    }

    /**
     * 故障排行榜
     * @param request
     * @return
     */
    @GetMapping("/fault-top")
    @Cache("'fault-top' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsFaultTopResponse> getFaultTop(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsService.getFaultTop(request));
    }

    /**
     * 故障类型统计
     * @param request
     * @return
     */
    @GetMapping("/fault-type")
    @Cache("'fault-type' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsFaultTypeResponse> getFaultType(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsService.getFaultType(request));
    }


    /**
     * 订单-服务时长统计
     * @param request
     * @return
     */
    @GetMapping("/server-time")
    @Cache("'server-time' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsServerTimeResponse> getServerTime(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsNursingService.getServerTime(request));
    }


    /**
     * 订单-评价统计
     * @param request
     * @return
     */
    @GetMapping("/estimate")
    @Cache("'estimate' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsEstimateResponse> getEstimate(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsNursingService.getEstimate(request));
    }

    /**
     * 订单-配置费用统计
     * @param request
     * @return
     */
    @GetMapping("/price")
    @Cache("'price' + #request.year + '_' + #request.month")
    public SingleResponse<StatisticsPriceResponse> getPrice(StatisticsRequest request){
        return TemplateProcess.doProcess(log,() -> statisticsNursingService.getPrice(request));
    }

}
