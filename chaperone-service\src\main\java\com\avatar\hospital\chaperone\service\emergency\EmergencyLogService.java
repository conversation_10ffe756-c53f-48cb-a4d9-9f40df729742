package com.avatar.hospital.chaperone.service.emergency;

import com.avatar.hospital.chaperone.request.emergency.EmergencyLogAddRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogPagingRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogAddResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogPagingResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogUpdateResponse;

public interface EmergencyLogService {

    EmergencyLogAddResponse add(EmergencyLogAddRequest request);

    EmergencyLogUpdateResponse update(EmergencyLogUpdateRequest request);

    PageResponse<EmergencyLogPagingResponse> paging(EmergencyLogPagingRequest request);
}
