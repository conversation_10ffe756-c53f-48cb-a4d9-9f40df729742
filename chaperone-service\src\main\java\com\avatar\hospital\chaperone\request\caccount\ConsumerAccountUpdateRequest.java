package com.avatar.hospital.chaperone.request.caccount;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/19
 */
@Data
public class ConsumerAccountUpdateRequest implements Serializable {

    /**
     * 用户id
     */
    private Long accountId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户手机号
     */
    private String phoneNumber;

    /**
     * 用户名称
     */
    private String nickName;

    /**
     * 性别
     *
     * @see com.avatar.hospital.chaperone.database.caccount.enums.AccountSex
     */
    private Integer sex;

    /**
     * 出生日期 yyyy-MM-dd
     */
    private String birthday;


    /**
     * 入院日期 yyyy-MM-dd
     */
    private String admissionTime;

}
