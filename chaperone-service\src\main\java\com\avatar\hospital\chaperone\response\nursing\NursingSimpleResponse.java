package com.avatar.hospital.chaperone.response.nursing;

import com.avatar.hospital.chaperone.database.nursing.enums.Gender;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingSimpleResponse implements Serializable {
    /**
     * 护工ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别 -1 未知 0 女 1 男
     *
     * @see Gender
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 服务年限
     */
    private Integer workYear;

    /**
     * 状态 0 审核中 1 生效 2 失效
     *
     * @see com.avatar.hospital.chaperone.database.nursing.enums.NursingStatus
     */
    private Integer status;

    /**
     * 护工星级 1 ~ 5 星
     */
    private Integer nursingStar;

    /**
     * 生效时间 0 立即生效 时间戳:预设时间
     */
    private Long time;

    /**
     * 手机号 非必填
     */
    private String mobile;
}
