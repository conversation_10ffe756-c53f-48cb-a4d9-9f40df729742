package com.avatar.hospital.chaperone.database.baccount.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountOrganizationDO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * B端用户和组织机构关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface AccountOrganizationRepositoryService extends IService<AccountOrganizationDO> {

    /**
     * 根据accountId查询
     *
     * @param accountId -
     * @return -
     */
    List<AccountOrganizationDO> findByAccountId(Long accountId);

    /**
     * 根据accountId批量查询
     *
     * @param accountIds -
     * @return -
     */
    List<AccountOrganizationDO> findByAccountIds(Set<Long> accountIds);

    /**
     * 根据organizationId查询
     *
     * @param organizationIds -
     * @return -
     */
    List<AccountOrganizationDO> findByOrganizationIds(Set<Long> organizationIds);

    /**
     * 根据organizationId删除
     *
     * @param organizationIds -
     * @return -
     */
    Boolean deleteByOrganizationIds(Set<Long> organizationIds);

    /**
     * 根据accountId删除
     *
     * @param accountIds -
     * @return -
     */
    Boolean deleteByAccountIds(Set<Long> accountIds);
}
