package com.avatar.hospital.chaperone.web.handler;


import com.avatar.hospital.chaperone.response.baccount.WebAccountBasicResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountDetailResponse;
import com.avatar.hospital.chaperone.service.baccount.WebAccountService;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Order(value = 100)
@Slf4j
@Aspect
@Component
public class WebOperatorAspect {

    @Autowired
    private WebAccountService webAccountService;
    @Pointcut("execution(public * com.avatar.hospital.chaperone.web.controller..*.*(..))")
    public void log() {
    }

    /**
     * 环绕
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Before("log()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg instanceof OperatorReq) {
                // 赋值
                Long currentAccountId = WebAccountUtils.getCurrentAccountId();
                if (Objects.nonNull(currentAccountId)) {
                    WebAccountBasicResponse detail = webAccountService.basicInfo(currentAccountId);
                    if (Objects.nonNull(detail)) {
                        Operator operator = new Operator();
                        operator.setId(detail.getId());
                        operator.setMobile(detail.getPhoneNumber());
                        operator.setName(detail.getNickname());
                        operator.setSource(2);
                        ((OperatorReq) arg).setOperatorUser(operator);
                    }
                }
            }
        }
    }

}
