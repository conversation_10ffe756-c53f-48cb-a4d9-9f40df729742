package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单-套餐关联表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_order_item")
public class OrderItemDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 订单ID
     */
      private Long orderId;

      /**
     * 套餐ID
     */
      private Long itemId;

  /**
   * 套餐名字
   */
  private String itemName;

      /**
     * 天数/数量
     */
      private Integer number;

      /**
     * 单价
     */
      private Integer price;

      /**
     * 总金额，单位分
     */
      private Integer totalPrice;

}
