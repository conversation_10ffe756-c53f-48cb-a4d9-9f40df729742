package com.avatar.hospital.chaperone.consumer.validator.caccount;

import com.avatar.hospital.chaperone.consumer.utils.ConsumerAccountUtils;
import com.avatar.hospital.chaperone.database.caccount.enums.AccountSex;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountUpdateRequest;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Slf4j
public class ConsumerAccountValidator {

    public static void updateValidate(ConsumerAccountUpdateRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        if (request.getSex() != null) {
            AssertUtils.isNotNull(AccountSex.of(request.getSex()), ErrorCode.CONSUMER_ACCOUNT_SEX_ERROR);
        }
        if (StringUtils.isNotBlank(request.getBirthday())) {
            try {
                DateUtils.dataStrToLocalDate(request.getBirthday(), DateUtils.DAY_PATTERN);
            } catch (Exception e) {
                log.error("ConsumerAccountValidator updateValidate birthday error:{}", Throwables.getStackTraceAsString(e));
                throw BusinessException.of(ErrorCode.CONSUMER_ACCOUNT_BIRTHDAY_ERROR);
            }
        }
        Long accountId = ConsumerAccountUtils.getCurrentAccountIdAndThrow();
        request.setAccountId(accountId);
    }

    public static void detailValidate(Long accountId) {
        AssertUtils.isNotNull(accountId, ErrorCode.PARAMETER_ERROR);
        // 数据校验
        Long currentAccountId = ConsumerAccountUtils.getCurrentAccountIdAndThrow();
        AssertUtils.isTrue(currentAccountId.equals(accountId), ErrorCode.DATA_AUTHORITY_ERROR);
    }
}
