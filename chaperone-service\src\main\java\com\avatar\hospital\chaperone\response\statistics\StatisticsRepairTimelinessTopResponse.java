package com.avatar.hospital.chaperone.response.statistics;

import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 10:12
 **/
@Data
public class StatisticsRepairTimelinessTopResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 年度排行榜 前10
     */
    private List<DATA> yearTop10;
    /**
     * 年度排行榜 后10
     */
    private List<DATA> yearBottom10;

    /**
     * 月度排行榜 前10
     */
    private List<DATA> monthTop10;

    /**
     * 月度排行榜 后10
     */
    private List<DATA> monthBottom10;

    // 内部类

    /**
     * 单个数据
     */
    @Data
    public static class DATA implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 报修单ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 保修单编号
         */
        private String code;

        /**
         * 报修系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他,7-暖通系统，8-建筑系统，9-消防系统）
         */
        private Integer systemType;

        /**
         * 完成时间
         */
        @DateTimeFormat
        private LocalDateTime completedTime;

        /**
         * 创建时间
         */
        @DateTimeFormat
        private LocalDateTime createAt;

        /**
         * 处理时间 秒
         */
        private Long processTime;

        public final static List<DATA> list(List<RepairFormDO> list) {
            return list.stream().map(item -> {
                DATA data = new DATA();
                data.setId(item.getId());
                data.setCode(item.getCode());
                data.setSystemType(item.getSystemType());
                data.setCompletedTime(item.getCompletedTime());
                data.setCreateAt(item.getCreatedAt());
                data.setProcessTime(item.getProcessTime());
                return data;
            }).collect(Collectors.toList());
        }
    }
}
