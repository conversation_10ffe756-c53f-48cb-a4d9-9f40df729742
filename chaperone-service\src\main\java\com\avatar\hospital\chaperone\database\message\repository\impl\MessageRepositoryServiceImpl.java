package com.avatar.hospital.chaperone.database.message.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.message.dataobject.MessageDO;
import com.avatar.hospital.chaperone.database.message.mapper.MessageMapper;
import com.avatar.hospital.chaperone.database.message.repository.MessageRepositoryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 消息-C端; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Slf4j
@Service
public class MessageRepositoryServiceImpl extends ServiceImpl<MessageMapper, MessageDO> implements MessageRepositoryService {

    @Override
    public Long add(MessageDO messageDO) {
        if (messageDO == null) {
            return null;
        }
        if (!save(messageDO)) {
            return null;
        }
        return messageDO.getId();
    }

    @Override
    public Boolean updateMessageRecord(Long messageId, Integer sendFlag, String result, String errorMessage) {
        if (messageId == null) {
            return false;
        }
        LambdaUpdateWrapper<MessageDO> updateWrapper = updateWrapper();
        updateWrapper.set(MessageDO::getSendFlag, sendFlag);
        updateWrapper.set(MessageDO::getSendResult, result);
        updateWrapper.set(MessageDO::getErrorMessage, errorMessage);

        updateWrapper.eq(MessageDO::getId, messageId);
        return update(updateWrapper);
    }

    @Override
    public MessageDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<MessageDO> queryWrapper = queryWrapper();
        queryWrapper.eq(MessageDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    private LambdaQueryWrapper<MessageDO> queryWrapper() {
        LambdaQueryWrapper<MessageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<MessageDO> updateWrapper() {
        LambdaUpdateWrapper<MessageDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MessageDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
