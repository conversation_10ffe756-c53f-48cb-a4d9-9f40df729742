package com.avatar.hospital.chaperone.service.baccount;

import com.avatar.hospital.chaperone.request.baccount.*;
import com.avatar.hospital.chaperone.response.baccount.*;


/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
public interface OrganizationControlService {

    /**
     * 获取组织机构配置
     *
     * @param request -
     * @return -
     */
    OrganizationControlResponse getByOrgId(OrganizationControlRequest request);
}
