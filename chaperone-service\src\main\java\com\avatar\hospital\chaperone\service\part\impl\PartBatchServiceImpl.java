package com.avatar.hospital.chaperone.service.part.impl;

import com.avatar.hospital.chaperone.builder.part.PartBatchBuilder;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.enums.PartBatchStatusType;
import com.avatar.hospital.chaperone.database.part.repository.PartBatchRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartRepositoryService;
import com.avatar.hospital.chaperone.request.part.PartBatchForCPageRequest;
import com.avatar.hospital.chaperone.request.part.PartBatchRequest;
import com.avatar.hospital.chaperone.request.part.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartBatchVO;
import com.avatar.hospital.chaperone.service.part.PartBatchService;
import com.avatar.hospital.chaperone.service.part.PartService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;

import static com.avatar.hospital.chaperone.enums.ErrorCode.PROJECT_PART_BATCH_STATUS_NOT_VALID;
import static com.avatar.hospital.chaperone.enums.ErrorCode.PROJECT_PART_NOT_EXIST;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 13:20
 */
@Service
public class PartBatchServiceImpl implements PartBatchService {
    @Autowired
    PartBatchRepositoryService partBatchRepositoryService;
    @Autowired
    PartRepositoryService partRepositoryService;
    @Autowired
    PartService partService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long create(PartBatchRequest request) {
        PartBatchDO partBatchDO = PartBatchBuilder.build(request);
        partBatchRepositoryService.save(partBatchDO);
        //partRepositoryService.saveBatch(PartBatchBuilder.build(partBatchDO));
        return partBatchDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long update(PartBatchRequest request) {

    /*    PartBatchDO partBatchDO = partBatchRepositoryService.getById(request.getId());
        AssertUtils.isNotNull(partBatchDO, PROJECT_PART_NOT_EXIST);
        //AssertUtils.isTrue(PartBatchStatusType.NON_STOCK.getCode().equals(partBatchDO.getStatus()), PROJECT_PART_BATCH_STATUS_NOT_VALID);

        partBatchDO.setUpdatedAt(LocalDateTime.now());
        partBatchDO.setUpdateBy(request.getOperator());
        if (Objects.nonNull(request.getQuantity())) {
            partBatchDO.setQuantity(request.getQuantity());
        }
*/
        LambdaUpdateWrapper<PartBatchDO> update = updateWrapper();
        update.eq(PartBatchDO::getId, request.getId());
        update.set(Objects.nonNull(request.getName()), PartBatchDO::getName, request.getName());
        //update.set(Objects.nonNull(request.getQuantity()), PartBatchDO::getQuantity, request.getQuantity());
        update.set(Objects.nonNull(request.getPrice()), PartBatchDO::getPrice, request.getPrice());
        update.set(Objects.nonNull(request.getUnit()), PartBatchDO::getUnit, request.getUnit());
        update.set(Objects.nonNull(request.getRemark()), PartBatchDO::getRemark, request.getRemark());

        update.set(PartBatchDO::getUpdateBy, request.getOperator());
        update.set(PartBatchDO::getUpdatedAt, LocalDateTime.now());

        partBatchRepositoryService.update(update);

        //partService.update2delete(request);
        //partRepositoryService.saveBatch(PartBatchBuilder.build(partBatchDO));
        return request.getId();
    }

    @Override
    public PageResponse<PartBatchVO> paging(QueryRequest request) {
        Page<PartBatchDO> page = request.ofPage();
        LambdaQueryWrapper<PartBatchDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(PartBatchDO::getId);
        queryWrapper.eq(Objects.nonNull(request.getCode()), PartBatchDO::getCode, request.getCode());
        queryWrapper.eq(Objects.nonNull(request.getStatus()), PartBatchDO::getStatus, request.getStatus());
        queryWrapper.like(Objects.nonNull(request.getName()), PartBatchDO::getName, request.getName());
        page = partBatchRepositoryService.page(page, queryWrapper);

        return PageResponse.build(page, PartBatchBuilder::buildVO);
    }

    @Override
    public PageResponse<PartBatchVO> pagingForC(PartBatchForCPageRequest request) {
        Page<PartBatchDO> page = request.ofPage();
        LambdaQueryWrapper<PartBatchDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(PartBatchDO::getId);
        queryWrapper.like(Objects.nonNull(request.getName()), PartBatchDO::getName, request.getName());
        queryWrapper.eq(PartBatchDO::getStatus, PartBatchStatusType.STOCK.getCode());
        page = partBatchRepositoryService.page(page, queryWrapper);

        return PageResponse.build(page, PartBatchBuilder::buildVO);
    }

    @Override
    public PartBatchVO detail(Long id) {

        return PartBatchBuilder.buildVO(partBatchRepositoryService.getById(id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean entryStock(LocalDateTime entryTime, Long stockApplyId, PartStockApplyRefPartBatchDO partBatch, Long operator) {
        PartBatchDO partBatchDO = partBatchRepositoryService.getById(partBatch.getSparePartBatchId());
        LambdaUpdateWrapper<PartBatchDO> update = updateWrapper();
        update.eq(PartBatchDO::getId, partBatch.getSparePartBatchId());

        update.set(PartBatchDO::getStatus, PartBatchStatusType.STOCK.getCode());
        update.setSql("balance = balance+" + partBatch.getQuantity());
        update.setSql("quantity = quantity+" + partBatch.getQuantity());
        update.set(PartBatchDO::getUpdateBy, operator);
        update.set(PartBatchDO::getUpdatedAt, LocalDateTime.now());
        partBatchRepositoryService.update(update);
        partService.entryStock(entryTime, stockApplyId, partBatchDO, partBatch, operator);
        return true;
    }

    private LambdaQueryWrapper<PartBatchDO> queryWrapper() {
        LambdaQueryWrapper<PartBatchDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PartBatchDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<PartBatchDO> updateWrapper() {
        LambdaUpdateWrapper<PartBatchDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PartBatchDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
