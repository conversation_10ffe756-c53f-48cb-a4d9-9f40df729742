package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.database.order.enums.OrderEstimateSource;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:05
 **/
@Data
public class OrderEstimateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 来源
     */
    private Integer source;

    public static final OrderEstimateRequest buildForC(Long orderId) {
        OrderEstimateRequest obj = new OrderEstimateRequest();
        obj.setOrderId(orderId);
        obj.setSource(OrderEstimateSource.USER.getStatus());
        return obj;
    }


}
