package com.avatar.hospital.chaperone.response.part;

import com.avatar.hospital.chaperone.annotation.serializer.LocalDateTimeSerializer;
import com.avatar.hospital.chaperone.response.BaseVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 13:15
 */
@Data
public class PartVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = -5481496257106547961L;
    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 批次编号
     */
    private String batchCode;

    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;
    /**
     * 单价
     */
    private Integer price;

    /**
     * 批次ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sparePartBatchId;

    /**
     * 状态（0-待入库，1-可用，2-已使用）
     *
     * @see com.avatar.hospital.chaperone.database.part.enums.PartStatusType
     */
    private Integer status;

    /**
     * 入库申请单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stockApplyId;
    /**
     * 入库申请单編号
     */
    private String stockApplyCode;

    /**
     * 入库时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime entryTime;
    /**
     * 出资方类型（1-物业，2-医院）
     */
    private Integer investorType;

    /**
     * 出资方机构ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long investorOrgId;

    /**
     * 出资方机构名称
     */
    private String investor;

    /**
     * 备件使用申请单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sparePartApplyId;

    /**
     * 出库时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime outTime;

}
