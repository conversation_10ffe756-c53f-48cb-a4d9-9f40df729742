package com.avatar.hospital.chaperone.database.code.dataobject;

import com.avatar.hospital.chaperone.database.code.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 业务编号生成表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_code")
public class ProjectCodeDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 业务类型（1-设备，2-备件批次，3-备件入库审批单，4-巡检计划，5-维保计划，6-报修单,7-备件使用申请单）
     */
    private Integer bizType;

    /**
     * 说明（编号：prefix+日期+auto_id）
     */
    private String remark;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * 序列号ID
     */
    private Long autoId;


}
