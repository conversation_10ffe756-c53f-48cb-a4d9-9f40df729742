package com.avatar.hospital.chaperone.database.nursing.repository;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDetailResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingPagingResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 护工-记录护工基本信息; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface NursingRepositoryService extends IService<NursingDO> {

    /**
     * 新增护工
     *
     * @param nursingDO
     * @return Long
     */
    Long add(NursingDO nursingDO);

    /**
     * 更新护工
     *
     * @param nursingDO
     * @return Boolean
     */
    Boolean incrementUpdate(NursingDO nursingDO);

    /**
     * 根据id批量删除
     *
     * @param ids      -
     * @param updateBy -
     * @return -
     */
    Boolean deleteByIds(Set<Long> ids, Long updateBy);

    /**
     * 查询详情
     *
     * @param id
     * @return NursingDO
     */
    NursingDO findById(Long id);

    NursingDetailResponse findByBId(Long id);

    /**
     * 分页
     *
     * @param pageIndex
     * @param pageSize
     * @param nursingDO
     * @return PageResponse<NursingDO>
     */
    PageResponse<NursingDO> paging(Integer pageIndex, Integer pageSize, NursingDO nursingDO);

    /**
     * 数量
     *
     * @param buildNursingDO
     * @return
     */
    Long countByB(NursingDO buildNursingDO);

    /**
     * 查询
     *
     * @param request
     * @param pageIndex
     * @param pageSize
     * @return
     */
    List<NursingPagingResponse> listByB(NursingDO request, Integer pageIndex, Integer pageSize);

    /**
     * 返回所有未删除的护工
     * @param nursingId
     * @return
     */
    List<NursingDO> findAllSimple(Long nursingId);

    List<NursingDO> findByIdList(List<Long> nursingIdList);

    /**
     * 根据护工名称查询
     * @param nursingName
     * @return
     */
    NursingDO findByNursingName(String nursingName);
}
