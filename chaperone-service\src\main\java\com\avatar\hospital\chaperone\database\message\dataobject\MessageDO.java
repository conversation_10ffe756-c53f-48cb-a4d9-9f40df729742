package com.avatar.hospital.chaperone.database.message.dataobject;

import com.avatar.hospital.chaperone.database.message.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 消息-C端;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_message_c")
public class MessageDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 三方平台ID
     */
    private String openId;

    /**
     * 消息渠道 1 微信
     *
     * @see com.avatar.hospital.chaperone.database.message.enums.MessageChannel
     */
    private Integer channel;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * @see com.avatar.hospital.chaperone.database.message.enums.MessageSendFlag
     */
    private Integer sendFlag;

    /**
     * 发送结果回执
     */
    private String sendResult;

    /**
     * 发送异常信息
     */
    private String errorMessage;

}
