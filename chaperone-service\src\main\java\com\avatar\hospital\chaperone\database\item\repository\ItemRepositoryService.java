package com.avatar.hospital.chaperone.database.item.repository;

import com.avatar.hospital.chaperone.database.item.dataobject.ItemDO;
import com.avatar.hospital.chaperone.database.item.dataobject.ItemOrgDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 套餐表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface ItemRepositoryService extends IService<ItemDO> {

    /**
     * 创建套餐数据
     * @param itemDOByCreate
     * @param itemOrgDOByCreate
     * @return
     */
    Long create(ItemDO itemDOByCreate, List<ItemOrgDO> itemOrgDOByCreate);

    /**
     * 创建套餐数据
     * @param itemDO
     * @param itemOrgDO
     * @return
     */
    Long modify(ItemDO itemDO, List<ItemOrgDO> itemOrgDO);

    /**
     * 统计套餐数量
     * @param orgId
     * @param serverType
     * @return
     */
    Long countByC(Long orgId, Integer serverType);

    /**
     * 返回列表
     * @param orgId
     * @param serverType
     * @param offset
     * @param pageSize
     * @return
     */
    List<ItemDO> listByC(Long orgId, Integer serverType, Integer offset, Integer pageSize);
}
