server:
  port: 8080
spring:
  application:
    name: chaperone-web
  datasource:
    url: jdbc:mysql://${db.mysql}:3306/hospital_chaperone?useSSL=false&useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: hospital_chaperone
    password: gG8lM0lE5z
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      maxActive: 20
      initialSize: 1
      maxWait: 60000
      minIdle: 1
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxOpenPreparedStatements: 20
      connection-init-sqls: set names utf8mb4;

  redis:
    # Redis数据库索引（默认为0）
    database: 0
    # Redis服务器地址
    host: ${db.redis}
    # Redis服务器连接端口
    port: ${db.redis-port}
    # Redis服务器连接密码（默认为空）
    #username: hospital_chaperone
    #password: gG8lM0lE5z
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: ${spring.application.name}-token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 604800
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true

redisson:
  host: ${db.redis}
  port: ${db.redis-port}
  database: 10
  username: ""
  password: ""

uid:
  timeBits: 28             # 时间位, 默认:28
  workerBits: 22           # 机器位, 默认:22
  seqBits: 13               # 序列号, 默认:13
  epochStr: "2023-08-01"   # 初始时间, 与uid的使用寿命密切相关, 请尽可能设置为最近的时间以提高uid的使用寿命, 默认:"2020-03-27"
  type: standard # UidGenerator类型, standard表示标准版, cached表示使用了ringbuffer, 默认为standard
  db:
    switch: rds #通过"mongo"和"rds"来切换 worker id 生成器的数据源，默认为rds


wx:
  miniapp:
    appid: wx7e9d335f84f7007a #微信小程序的appid
    secret: 00595355d5264039d336dfc2501ed50b #微信小程序的Secret
    configStorage:
      type: RedisTemplate
      keyPrefix: wx-java-wa
  mp:
    appid: wx0fa7a8de5054500e #微信公众号的appid
    secret: 692a56408e9b8b467f59d52642e06035 #微信公众号的Secret
    configStorage:
      type: RedisTemplate
      keyPrefix: wx-java-wx
  pay:
    appId: "wxa0360fe3b2279192" #微信公众号的appid
    mchId: 1656539250 #商户id
    mchKey: zhejiangriseholdinggrouppay13579
    #keyPath: classpath:cert/apiclient_cert_1533433711.p12
    #apiV3Key: wJ65GOBWhqCXc406PJF6XQth7VqWZ18j #V3密钥
    #certSerialNo: 3B3D1DBA3A9E59A065B21DE0AF0F96CA6D741CC6
    certSerialNo: 3C456C7350309A2C22D3261A5896878DB0C0417E
    privateKeyPath: classpath:cert/apiclient_key.pem #apiclient_key.pem证书文件的绝对路径或者以classpath:开头的类路径
    privateCertPath: classpath:cert/apiclient_wx_cert.pem #apiclient_cert.pem证书文件的绝对路径或者以classpath:开头的类路径
    notifyUrl: 'https://comsumer-backend.rxwysystem.com/api/v1/consumer/notify/wx' #回调地址

oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  bucketName: yjkjsystem-hz
  accessKeyId: LTAI5tH562nskdQEaYJgqXQa
  secretAccessKey: ******************************

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 这个是生产环境的

wx-template-message-biz:
  orderPayTemplateId: 'ItjjzopGdCyrTz_mdJBnczFlmycCkjX1W_4ZkIYuBZk'