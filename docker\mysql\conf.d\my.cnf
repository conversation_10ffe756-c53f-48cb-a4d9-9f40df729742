[mysqld]
# 基础配置
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_general_ci
default-time-zone='+8:00'

# 连接配置
max_connections=1000
max_connect_errors=1000
wait_timeout=28800
interactive_timeout=28800

# 缓存配置
innodb_buffer_pool_size=512M
innodb_log_file_size=128M
innodb_log_buffer_size=16M
innodb_flush_log_at_trx_commit=2

# 查询缓存
query_cache_type=1
query_cache_size=64M
query_cache_limit=2M

# 慢查询日志
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# 二进制日志
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

# 安全配置
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
