package com.avatar.hospital.chaperone.database.part.repository;

import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.request.part.PartStockApplyRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 备件入库审批单关联备件批次 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PartStockApplyRefPartBatchRepositoryService extends IService<PartStockApplyRefPartBatchDO> {


    boolean update2delete(PartStockApplyRequest request);

    List<Long> getPartBatchIds(Long stockApplyId);

    List<PartStockApplyRefPartBatchDO> getPartBatchBy(Long stockApplyId);
}
