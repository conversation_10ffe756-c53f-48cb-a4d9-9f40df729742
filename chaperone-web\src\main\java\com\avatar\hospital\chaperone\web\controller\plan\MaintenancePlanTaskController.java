package com.avatar.hospital.chaperone.web.controller.plan;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.component.baccount.OrganizationComponent;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.request.plan.QueryRequest;
import com.avatar.hospital.chaperone.request.plan.TaskExecuteRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.plan.PlanTaskVO;
import com.avatar.hospital.chaperone.service.plan.PlanTaskService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 维保计划任务
 * @date 2023/10/27 15:46
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/web/project/plan/maintenance/task")
public class MaintenancePlanTaskController {
    @Autowired
    private PlanTaskService planTaskService;
    @Autowired
    private OrganizationComponent organizationComponent;


    /**
     * 查询
     *
     * @param request
     * @return
     */
    @GetMapping("paging")
    public SingleResponse<PageResponse<PlanTaskVO>> paging(QueryRequest request) {
        request.setPlanType(PlanType.MAINTENANCE);
        return TemplateProcess.doProcess(log, () -> {
            return planTaskService.paging(request);
        });
    }

    /**
     * 我的任务
     *
     * @param request
     * @return
     */
    @GetMapping("mine")
    public SingleResponse<PageResponse<PlanTaskVO>> mine(QueryRequest request) {
        request.setPlanType(PlanType.MAINTENANCE);
        Long accountId = request.getOperatorUser().getId();
        request.setAccountId(accountId);
        request.setOrgIds(organizationComponent.findDepartmentOrganizationList(accountId));
        return TemplateProcess.doProcess(log, () -> {
            return planTaskService.paging(request);
        });
    }

    /**
     * 任务执行
     *
     * @param request
     * @return
     */
    @PutMapping("execute")
    public SingleResponse<Boolean> execute(@RequestBody TaskExecuteRequest request) {
        request.setPlanType(PlanType.MAINTENANCE);
        return TemplateProcess.doProcess(log, () -> {
            return planTaskService.execute(request);
        });
    }

}
