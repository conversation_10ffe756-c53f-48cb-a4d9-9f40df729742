<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.order.mapper.OrderBillMapper">

    <select id="findAllTotal" resultType="java.lang.Long">
        select bill.order_id
        from t_order_bill bill
        left join t_order o on bill.order_id  = o.id
        where bill.parent_bill_id = 0
        and bill.deleted = 0
        and o.order_status  &gt;= 30
        and bill.bill_type = #{type}
        and bill.bill_sub_type = #{subType}
    </select>

    <select id="readypay" resultType="java.lang.Long">
        SELECT SUM(price_receivable) as price
        FROM `t_order_bill` bill
        LEFT JOIN `t_order_pay` pay on bill.`id`= pay.`bill_id`
        WHERE bill.`deleted`= 0
        and `parent_bill_id` != 0
        and pay.`deleted`= 0
        and pay.pay_status= 2
        and bill.`created_at` <![CDATA[ >= ]]> #{start}
        and bill.`created_at` <![CDATA[ < ]]> #{end}
    </select>
</mapper>