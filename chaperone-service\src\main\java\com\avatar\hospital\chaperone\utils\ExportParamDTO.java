package com.avatar.hospital.chaperone.utils;

/**
 * @program: hospital-chaperone
 * @description: 导出上下文参数
 * @author: sp0372
 * @create: 2023-10-24 11:15
 **/
public interface ExportParamDTO<T> {

    /**
     * 获取页码
     * @return
     */
    Integer getPageIndex();

    /**
     * 获取没页大小
     * @return
     */
    Integer getPageSize();
    /**
     * 页数 + 1
     * @return
     */
    Integer pageIndexAdd1();

    /**
     * 获取导出文件名称
     * @return
     */
    String getFileName();

    /**
     * 导出模板实体类
     * @return
     */
    Class<T> getClazz();

}
