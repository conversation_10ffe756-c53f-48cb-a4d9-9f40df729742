package com.avatar.hospital.chaperone.response.repair;

import com.avatar.hospital.chaperone.template.serialize.ToStringLocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:51
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormCResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 报修系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他,7-暖通系统，8-建筑系统，9-消防系统）
     */
    private Integer systemType;

    /**
     * 状态（1-未指派，2-已指派，3-未完成，4-待审核，5-已完成）
     */
    private Integer status;

    /**
     * 创建人员
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 报修时间
     */
    @JsonSerialize(using = ToStringLocalDateTimeSerializer.class)
    private LocalDateTime createAt;

    /**
     * 设备ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long deviceId;
    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 执行人员名称
     */
    private List<String> executorAccountName;

    /**
     * 紧急程度
     *
     * @see com.avatar.hospital.chaperone.database.repair.enums.RepairFormUrgencyDegreeType
     */
    private Integer urgencyDegreeType;

    /**
     * 报修位置
     */
    private String location;
}
