package com.avatar.hospital.chaperone.request.repair;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:49
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormAuthRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 报修单ID
     */
    @NotNull
    private Long id;

    /**
     * 操作 0 驳回 1 通过
     */
    @NotNull
    private Integer operate;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    /**
     * 审核通过
     * @return
     */
    public Boolean isPass() {
        return Objects.equals(1,operate);
    }

    /**
     * 拒接
     * @return
     */
    public Boolean isReject() {
        return Objects.equals(0,operate);
    }
}
