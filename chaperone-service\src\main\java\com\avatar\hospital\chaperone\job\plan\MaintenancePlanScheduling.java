package com.avatar.hospital.chaperone.job.plan;

import com.avatar.hospital.chaperone.annotation.Task;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.job.ScheduledHelper;
import com.avatar.hospital.chaperone.job.ScheduledType;
import com.avatar.hospital.chaperone.service.plan.PlanService;
import com.avatar.hospital.chaperone.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 13:20
 */
@Task(value = "MaintenancePlanScheduling", description = "维保计划任务描述")
@Slf4j
@Component
public class MaintenancePlanScheduling {
    @Autowired
    private  ScheduledHelper scheduledHelper;
    @Autowired
    private  PlanService planService;

    /**
     * 根据计划周期配置,每小时检测是否需要生成一条记录
     * 每天执行一次
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void planTask() {
        Integer hour = DateUtils.hour();
        Integer date = DateUtils.dateInt();
        scheduledHelper.exec(ScheduledType.MAINTENANCE_PLAN_TASK_DAY, () -> execute(date, hour));
    }

    /**
     * 计划任务记录
     *
     * @return
     */
    @Task(value = "maintenance-plan-task", description = "维保计划-任务生成")
    public Boolean execute(Integer date, Integer hour) {
        planService.execute(PlanType.MAINTENANCE);
        return true;
    }

}
