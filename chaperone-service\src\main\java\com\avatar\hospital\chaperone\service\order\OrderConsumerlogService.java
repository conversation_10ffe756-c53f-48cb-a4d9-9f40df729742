package com.avatar.hospital.chaperone.service.order;

import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.dto.OrderConsumerlogExportDTO;

import java.util.List;

/**
 * @program: hospital-chaperone
 * @description: 订单消费明细
 * @author: sp0372
 * @create: 2023-10-11 16:57
 **/
public interface OrderConsumerlogService {
    // C端
    /**
     * C端订单 消费明细查询
     * @param request
     * @return
     */
    PageResponse<OrderConsumerLogCResponse> pagingForC(OrderConsumerlogCPageRequest request);



    // B端

    /**
     * B端消费明细分页查询
     */
    PageResponse<OrderConsumerLogResponse> paging(OrderConsumerPageRequest request);

    /**
     * 按订单,生成一条消费明细
     *    通过明细计算分层
     */
    OrderConsumerLogCreateResponse create(OrderConsumeCreateRequest request);

    /**
     * 生成消费明细,在订单确认的时候
     */
    OrderConsumerLogCreateResponse createByOrderConform(OrderConsumeCreateRequest request);

    /**
     * 按订单,重新计算消费明细
     *    B端重新设置折扣,费率会执行
     */
    OrderConsumerLogCreateResponse resetCalculation(OrderConsumeResetCalculationRequest request);

    /**
     * 通知某订单指定范围内消费金额
     * @param request
     * @return
     */
    OrderConsumeSumPriceResponse sumPrice(OrderConsumeSumPriceRequest request);

    /**
     * 导出数据查询
     * @param request
     * @return
     */
    List<OrderConsumerlogExportDTO> exportPaging(OrderConsumerExportPageRequest request);

    /**
     * 涉及的护士列表
     * @param orderId
     * @param startDate yyyyMMdd
     * @param endDate yyyyMMdd
     * @return
     */
    List<OrderNursingSimpleResponse> nursingList(Long orderId, Integer startDate, Integer endDate);
}
