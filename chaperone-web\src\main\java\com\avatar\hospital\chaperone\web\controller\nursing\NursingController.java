package com.avatar.hospital.chaperone.web.controller.nursing;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.nursing.NursingAddRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuDetailResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingAddResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDetailResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingPagingResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingUpdateResponse;
import com.avatar.hospital.chaperone.service.nursing.NursingService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import com.avatar.hospital.chaperone.web.validator.nursing.NursingValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/nursing")
public class NursingController {

    private final NursingService nursingService;

    /**
     * 创建
     */
    @PostMapping(value = "")
    public SingleResponse<NursingAddResponse> add(@RequestBody NursingAddRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("NursingController add request:{}", request);
            NursingValidator.addValidate(request);
            return nursingService.add(request);
        });
    }

    /**
     * 修改
     */
    @PutMapping(value = "")
    public SingleResponse<NursingUpdateResponse> update(@RequestBody NursingUpdateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("NursingController update request:{}", request);
            NursingValidator.updateValidate(request);
            return nursingService.update(request);
        });
    }


    /**
     * 分页
     */
    @GetMapping(value = "/paging")
    public SingleResponse<PageResponse<NursingPagingResponse>> paging(NursingPagingRequest request){
        return TemplateProcess.doProcess(log, () -> {
            log.info("NursingController paging request:{}", request);
            NursingValidator.pagingValidate(request);
            return nursingService.paging(request);
        });
    }

    /**
     * 详情
     */
    @GetMapping(value = "/{id}")
    public SingleResponse<NursingDetailResponse> detail(@PathVariable("id") Long nursingId) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("NursingController detail request:{}", nursingId);
            AssertUtils.isNotNull(nursingId, ErrorCode.PARAMETER_ERROR);
            return nursingService.detail(nursingId);
        });
    }

    /**
     * 删除
     */
    @DeleteMapping("/{id}")
    public SingleResponse<Boolean> delete(@PathVariable("id") Long nursingId){
        return TemplateProcess.doProcess(log,()->{
            Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
            log.info("NursingController delete request id:{} deletedId:{}",nursingId,accountId);
            AssertUtils.isNotNull(nursingId, ErrorCode.PARAMETER_ERROR);
            AssertUtils.isNotNull(accountId, ErrorCode.PARAMETER_ERROR);
            return nursingService.delete(nursingId,accountId);
        });
    }

    /**
     * 护工状态修改
     */

    /**
     * 护工星级设置
     */


}
