package com.avatar.hospital.chaperone.database.item.dataobject;

import com.avatar.hospital.chaperone.database.item.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 套餐-机构;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_item_org")
public class ItemOrgDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 套餐ID
     */
      private Long itemId;

      /**
     * 机构ID
     */
      private Long orgId;

}
