package com.avatar.hospital.chaperone.service.statistics.impl;

import com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO;
import com.avatar.hospital.chaperone.database.device.repository.ProjectDeviceRepositoryService;
import com.avatar.hospital.chaperone.database.emergencylog.repository.EmergencyHandlingLogRepositoryService;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartStatisticsModel;
import com.avatar.hospital.chaperone.database.part.repository.PartApplyRefPartBatchRepositoryService;
import com.avatar.hospital.chaperone.database.plan.repository.MaintenancePlanTaskRepositoryService;
import com.avatar.hospital.chaperone.database.plan.repository.PatrolPlanTaskRepositoryService;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormRepositoryService;
import com.avatar.hospital.chaperone.request.statistics.StatisticsRequest;
import com.avatar.hospital.chaperone.response.statistics.*;
import com.avatar.hospital.chaperone.service.statistics.StatisticsService;
import com.avatar.hospital.chaperone.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 10:15
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsServiceImpl implements StatisticsService {
    private final EmergencyHandlingLogRepositoryService emergencyHandlingLogRepositoryService;
    private final RepairFormRepositoryService repairFormRepositoryService;
    private final ProjectDeviceRepositoryService projectDeviceRepositoryService;
    private final PatrolPlanTaskRepositoryService patrolPlanTaskRepositoryService;
    private final MaintenancePlanTaskRepositoryService maintenancePlanTaskRepositoryService;
    private final PartApplyRefPartBatchRepositoryService partApplyRefPartBatchRepositoryService;
    @Override
    public StatisticsPartUseResponse getPartUse(StatisticsRequest request) {
        LocalDateTime[] startEndOfYear = DateUtils.getStartEndOfYear(request.getYear());
        List<PartStatisticsModel> yearList = partApplyRefPartBatchRepositoryService.getStatistics(startEndOfYear[0],startEndOfYear[1]);
        LocalDateTime[] startEndOfMonth = DateUtils.getStartEndOfYear(request.getMonth());
        List<PartStatisticsModel> monthList = partApplyRefPartBatchRepositoryService.getStatistics(startEndOfMonth[0],startEndOfMonth[1]);

        StatisticsPartUseResponse response = new StatisticsPartUseResponse();
        response.setYearList(yearList);
        response.setMonthList(monthList);
        return response;
    }

    @Override
    public StatisticsEmergencyHandlingResponse getEmergencyHandling(StatisticsRequest request) {
        Integer yearNum = emergencyHandlingLogRepositoryService.countForYear(request.getYear());
        Integer monthNum = emergencyHandlingLogRepositoryService.countForMonth(request.getMonth());

        // 封装结果
        StatisticsEmergencyHandlingResponse response = new StatisticsEmergencyHandlingResponse();
        response.setYear(yearNum);
        response.setMonth(monthNum);
        return response;
    }

    @Override
    public StatisticsTaskResponse getTask(StatisticsRequest request) {
        Map<Integer,Integer> patrolForYear = patrolPlanTaskRepositoryService.getStatisticsForYear(request.getYear());
        Map<Integer,Integer> patrolForMonth = patrolPlanTaskRepositoryService.getStatisticsForMonth(request.getMonth());
        Map<Integer,Integer> maintenanceForYear = maintenancePlanTaskRepositoryService.getStatisticsForYear(request.getYear());
        Map<Integer,Integer> maintenanceForMonth = maintenancePlanTaskRepositoryService.getStatisticsForMonth(request.getMonth());

        StatisticsTaskResponse response = new StatisticsTaskResponse();
        response.setPatrolForYear(patrolForYear);
        response.setMaintenanceForYear(maintenanceForYear);
        response.setPatrolForMonth(patrolForMonth);
        response.setMaintenanceForMonth(maintenanceForMonth);
        return response;
    }

    @Override
    public StatisticsRepairTimelinessTopResponse getRepairTimelinessTop(StatisticsRequest request) {
        LocalDateTime[] startEndOfYear = DateUtils.getStartEndOfYear(request.getYear());
        LocalDateTime[] startEndOfMonth = DateUtils.getStartEndOfMonth(request.getMonth());
        List<RepairFormDO> top10ForYear = repairFormRepositoryService.getRepairTimelinessTop10(startEndOfYear[0],startEndOfYear[1]);
        List<RepairFormDO> top10ForMonth = repairFormRepositoryService.getRepairTimelinessTop10(startEndOfMonth[0],startEndOfMonth[1]);
        List<RepairFormDO> bottom10ForYear = repairFormRepositoryService.getRepairTimelinessBottom10(startEndOfYear[0],startEndOfYear[1]);
        List<RepairFormDO> bottom10ForMonth = repairFormRepositoryService.getRepairTimelinessBottom10(startEndOfMonth[0],startEndOfMonth[1]);

        // 封装结果
        StatisticsRepairTimelinessTopResponse response = new StatisticsRepairTimelinessTopResponse();
        response.setYearTop10(StatisticsRepairTimelinessTopResponse.DATA.list(top10ForYear));
        response.setYearBottom10(StatisticsRepairTimelinessTopResponse.DATA.list(bottom10ForYear));
        response.setMonthTop10(StatisticsRepairTimelinessTopResponse.DATA.list(top10ForMonth));
        response.setMonthBottom10(StatisticsRepairTimelinessTopResponse.DATA.list(bottom10ForMonth));
        return response;
    }

    @Override
    public StatisticsRepairStatusResponse getRepairStatus(StatisticsRequest request) {
        LocalDateTime[] startEndOfYear = DateUtils.getStartEndOfYear(request.getYear());
        LocalDateTime[] startEndOfMonth = DateUtils.getStartEndOfMonth(request.getMonth());
        Map<Integer,Integer> yearTypeCountFinishRef = repairFormRepositoryService.statisticsRepairStatusFinish(startEndOfYear[0],startEndOfYear[1]);
        Map<Integer,Integer> yearTypeCountWorkingRef = repairFormRepositoryService.statisticsRepairStatusWork(startEndOfYear[0],startEndOfYear[1]);
        Map<Integer,Integer> monthTypeCountFinishRef = repairFormRepositoryService.statisticsRepairStatusFinish(startEndOfMonth[0],startEndOfMonth[1]);
        Map<Integer,Integer> monthTypeCountWorkingRef = repairFormRepositoryService.statisticsRepairStatusWork(startEndOfMonth[0],startEndOfMonth[1]);

        Map<Integer, StatisticsRepairStatusResponse.DAT> data = StatisticsRepairStatusResponse.DAT.of(yearTypeCountFinishRef, yearTypeCountWorkingRef,
                monthTypeCountFinishRef, monthTypeCountWorkingRef);
        StatisticsRepairStatusResponse response = new StatisticsRepairStatusResponse();
        response.setData(data);
        return response;
    }

    @Override
    public StatisticsFaultTopResponse getFaultTop(StatisticsRequest request) {
        LocalDateTime[] startEndOfYear = DateUtils.getStartEndOfYear(request.getYear());
        List<ProjectDeviceDO> yearTop10 = projectDeviceRepositoryService.fault10(startEndOfYear[0], startEndOfYear[1], 0);
        List<ProjectDeviceDO> yearBottom10 = projectDeviceRepositoryService.fault10(startEndOfYear[0], startEndOfYear[1], 1);

        LocalDateTime[] startEndOfMonth = DateUtils.getStartEndOfMonth(request.getMonth());
        List<ProjectDeviceDO> monthTop10 = projectDeviceRepositoryService.fault10(startEndOfMonth[0], startEndOfMonth[1], 0);
        List<ProjectDeviceDO> monthBottom10 = projectDeviceRepositoryService.fault10(startEndOfMonth[0], startEndOfMonth[1], 1);

        StatisticsFaultTopResponse response = new StatisticsFaultTopResponse();
        response.setYearTop10(StatisticsFaultTopResponse.DAT.of(yearTop10));
        response.setYearBottom10(StatisticsFaultTopResponse.DAT.of(yearBottom10));
        response.setMonthTop10(StatisticsFaultTopResponse.DAT.of(monthTop10));
        response.setMonthBottom10(StatisticsFaultTopResponse.DAT.of(monthBottom10));
        return response;
    }

    @Override
    public StatisticsFaultTypeResponse getFaultType(StatisticsRequest request) {
        Map<Integer,Integer> yearTypeCountFinishRef = repairFormRepositoryService.statisticsRepairStatusForYear(request.getYear());
        Map<Integer,Integer> monthTypeCountFinishRef = repairFormRepositoryService.statisticsRepairStatusForMonth(request.getMonth());

        Map<Integer, StatisticsFaultTypeResponse.DAT> data =
                StatisticsFaultTypeResponse.DAT.of(yearTypeCountFinishRef,monthTypeCountFinishRef);

        StatisticsFaultTypeResponse response = new StatisticsFaultTypeResponse();
        response.setData(data);
        return response;
    }

    public static void main(String[] args) {
        String s = UUID.randomUUID().toString();
        s= s.replace("-","");
        System.out.printf(s);
    }
}
