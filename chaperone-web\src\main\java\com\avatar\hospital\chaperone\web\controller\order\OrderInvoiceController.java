package com.avatar.hospital.chaperone.web.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.order.OrderInvoicePageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderInvoiceResponse;
import com.avatar.hospital.chaperone.service.order.OrderInvoiceService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * B端陪护单-发票
 * @program: hospital-chaperone
 * @description: 套餐
 * @author: sp0372
 * @create: 2023-10-13 10:07
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/order/invoice")
public class OrderInvoiceController {
    private final OrderInvoiceService orderEstimateService;

    /**
     * 查询
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<OrderInvoiceResponse>> paging(@RequestBody OrderInvoicePageRequest request) {
        return TemplateProcess.doProcess(log, () -> orderEstimateService.paging(request));
    }

}
