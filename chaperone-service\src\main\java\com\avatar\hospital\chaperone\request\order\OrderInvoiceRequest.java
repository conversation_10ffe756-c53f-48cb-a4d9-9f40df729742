package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.template.model.Operator;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderInvoiceRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账单ID
     */
    @NotNull
    private Long billId;


    /**
     * 操作用户
     */
    private Operator operatorUser;
}
