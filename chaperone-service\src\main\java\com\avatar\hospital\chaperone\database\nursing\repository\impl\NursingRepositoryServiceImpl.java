package com.avatar.hospital.chaperone.database.nursing.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountRoleDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingHospitalDO;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingStatus;
import com.avatar.hospital.chaperone.database.nursing.mapper.NursingMapper;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingHospitalRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.nursing.NursingPagingRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationHospitalResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDetailResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingHospitalResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingPagingResponse;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.DefaultUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 护工-记录护工基本信息; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class NursingRepositoryServiceImpl extends ServiceImpl<NursingMapper, NursingDO> implements NursingRepositoryService {

    private final NursingHospitalRepositoryService nursingHospitalRepositoryService;

    @Override
    public Long add(NursingDO nursingDO) {
        if (nursingDO == null) {
            return null;
        }
        nursingDO.setId(IdUtils.getId());
        if (!save(nursingDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        return nursingDO.getId();
    }

    @Override
    public Boolean incrementUpdate(NursingDO nursingDO) {
        if (nursingDO == null) {
            return false;
        }
        LambdaUpdateWrapper<NursingDO> updateWrapper = updateWrapper();
        updateWrapper.set(nursingDO.getName() != null, NursingDO::getName, nursingDO.getName());
        updateWrapper.set(nursingDO.getGender() != null, NursingDO::getGender, nursingDO.getGender());
        updateWrapper.set(nursingDO.getAge() != null, NursingDO::getAge, nursingDO.getAge());
        updateWrapper.set(nursingDO.getWorkYear() != null, NursingDO::getWorkYear, nursingDO.getWorkYear());
        updateWrapper.set(nursingDO.getSpecialty() != null, NursingDO::getSpecialty, nursingDO.getSpecialty());
        updateWrapper.set(nursingDO.getRemark() != null, NursingDO::getRemark, nursingDO.getRemark());
        updateWrapper.set(nursingDO.getSettleWay() != null, NursingDO::getSettleWay, nursingDO.getSettleWay());
        updateWrapper.set(nursingDO.getSettleTime() != null, NursingDO::getSettleTime, nursingDO.getSettleTime());
        updateWrapper.set(nursingDO.getSponsor() != null, NursingDO::getSponsor, nursingDO.getSponsor());
        updateWrapper.set(nursingDO.getSponsorPrice() != null, NursingDO::getSponsorPrice, nursingDO.getSponsorPrice());
        updateWrapper.set(nursingDO.getIdCardPic() != null, NursingDO::getIdCardPic, nursingDO.getIdCardPic());
        updateWrapper.set(nursingDO.getCertificationPic() != null, NursingDO::getCertificationPic, nursingDO.getCertificationPic());
        updateWrapper.set(nursingDO.getStatus() != null, NursingDO::getStatus, nursingDO.getStatus());
        updateWrapper.set(nursingDO.getNursingStar() != null, NursingDO::getNursingStar, nursingDO.getNursingStar());
        updateWrapper.set(nursingDO.getMobile() != null, NursingDO::getMobile, nursingDO.getMobile());
        updateWrapper.eq(NursingDO::getId, nursingDO.getId());

        return update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByIds(Set<Long> ids, Long updateBy) {
        if (CollectionUtils.isEmpty(ids) || updateBy == null) {
            return true;
        }
        LambdaUpdateWrapper<NursingDO> updateWrapper = updateWrapper();
        updateWrapper.set(NursingDO::getDeleted, System.currentTimeMillis());
        updateWrapper.set(NursingDO::getUpdateBy, updateBy);
        if (ids.size() == 1) {
            updateWrapper.eq(NursingDO::getId, ids.toArray()[0]);
        } else {
            updateWrapper.in(NursingDO::getId, ids);
        }
        if (!update(updateWrapper)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        //删除关联数据（删除护工，医院底下查不到护工，但是考勤等历史记录仍然保留）
        List<NursingHospitalDO> byNursingIds = nursingHospitalRepositoryService.findByNursingIds(ids);
        if (CollectionUtils.isNotEmpty(byNursingIds)) {
            if (!Boolean.TRUE.equals(nursingHospitalRepositoryService.deleteByNursingIds(
                    byNursingIds.stream().map(NursingHospitalDO::getNursingId).collect(Collectors.toSet())
            ))) {
                throw BusinessException.of(ErrorCode.UPDATE_ERROR);
            }
        }
        return true;
    }

    @Override
    public NursingDetailResponse findByBId(Long id) {
        if (id == null || id < 0) {
            return null;
        }
//        LambdaQueryWrapper<NursingDO> queryWrapper = queryWrapper();
//        queryWrapper.eq(NursingDO::getId, id);
        return baseMapper.findByBId(id);
    }

    @Override
    public NursingDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<NursingDO> queryWrapper = queryWrapper();
        queryWrapper.eq(NursingDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public PageResponse<NursingDO> paging(Integer pageIndex, Integer pageSize, NursingDO nursingDO) {
        int index = DefaultUtils.ifNullDefault(pageIndex, 1);
        int size = DefaultUtils.ifNullDefault(pageSize, 10);
        LambdaQueryWrapper<NursingDO> queryWrapper = queryWrapper();
        if (nursingDO != null) {
            queryWrapper.eq(nursingDO.getId() != null, NursingDO::getId, nursingDO.getId());
            queryWrapper.eq(nursingDO.getName() != null, NursingDO::getName, nursingDO.getName());
        }
        Page<NursingDO> page = baseMapper.selectPage(new Page<>(index, size), queryWrapper);
        return PageResponse.build(page.getTotal(), page.getCurrent(), page.getSize(), page.getRecords());
    }

    @Override
    public Long countByB(NursingDO request) {
        if (Objects.isNull(request)) {
            return null;
        }
        Map<String, Object> criteria = new HashMap<>();
        if (Objects.nonNull(request.getName())) {
            criteria.put("name", request.getName());
        }
        if (Objects.nonNull(request.getStatus())){
            criteria.put("status",request.getStatus());
        }
        return baseMapper.countByB(criteria);
    }

    @Override
    public List<NursingPagingResponse> listByB(NursingDO request, Integer pageIndex, Integer pageSize) {
        if (Objects.isNull(request)) {

        }
        Map<String, Object> criteria = new HashMap<>();
        if (Objects.nonNull(pageIndex)) {
            criteria.put("offset", pageIndex);
        }
        if (Objects.nonNull(pageSize)) {
            criteria.put("limit", pageSize);
        }
        if (Objects.nonNull(request.getName())) {
            criteria.put("name", request.getName());
        }
        if (Objects.nonNull(request.getStatus())){
            criteria.put("status",request.getStatus());
        }
        List<NursingPagingResponse> list = baseMapper.listByB(criteria);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<Long> collect = list.stream().map(NursingPagingResponse::getId).collect(Collectors.toList());
        List<NursingHospitalResponse> listHospitalByB = baseMapper.listHospitalByB(collect);
        if (CollectionUtils.isNotEmpty(listHospitalByB)) {
            for (NursingPagingResponse a : list) {
                List<OrganizationHospitalResponse> hospitalResponseList = new ArrayList<>();
                for (NursingHospitalResponse b : listHospitalByB) {
                    OrganizationHospitalResponse response = new OrganizationHospitalResponse();
                    if (a.getId().equals(b.getNursingId())){
                        response.setId(b.getId());
                        response.setName(b.getName());
                        hospitalResponseList.add(response);
                    }
                }
                a.setHospitalList(hospitalResponseList);
            }
        }

        return list;
    }

    @Override
    public List<NursingDO> findAllSimple(Long nursingId) {
        LambdaQueryWrapper<NursingDO> queryWrapper = queryWrapper();
        queryWrapper.select(NursingDO::getId,NursingDO::getName);
        queryWrapper.eq(Objects.nonNull(nursingId),NursingDO::getId,nursingId);
        queryWrapper.eq(NursingDO::getStatus, NursingStatus.NORMAL.getStatus());
        queryWrapper.orderByDesc(NursingDO::getId);
        return list(queryWrapper);
    }

    @Override
    public List<NursingDO> findByIdList(List<Long> nursingIdList) {
        LambdaQueryWrapper<NursingDO> queryWrapper = queryWrapper();
        queryWrapper.select(NursingDO::getId,NursingDO::getName);
        queryWrapper.in(Objects.nonNull(nursingIdList),NursingDO::getId,nursingIdList);
        queryWrapper.eq(NursingDO::getStatus, NursingStatus.NORMAL.getStatus());
        queryWrapper.orderByDesc(NursingDO::getId);
        return list(queryWrapper);
    }

    @Override
    public NursingDO findByNursingName(String nursingName) {
        LambdaQueryWrapper<NursingDO> queryWrapper = queryWrapper();
        queryWrapper.eq(NursingDO::getName, nursingName);
        List<NursingDO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    private LambdaQueryWrapper<NursingDO> queryWrapper() {
        LambdaQueryWrapper<NursingDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NursingDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<NursingDO> updateWrapper() {
        LambdaUpdateWrapper<NursingDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(NursingDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
