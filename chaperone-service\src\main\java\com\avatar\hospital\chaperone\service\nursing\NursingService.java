package com.avatar.hospital.chaperone.service.nursing;

import com.avatar.hospital.chaperone.request.nursing.NursingAddRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.nursing.*;

import java.util.List;
import java.util.Map;

/**
 * @program: hospital-chaperone
 * @description: 护工查询接口
 * @author: sp0372
 * @create: 2023-10-11 17:23
 **/
public interface NursingService {

    NursingAddResponse add(NursingAddRequest request);

    NursingUpdateResponse update(NursingUpdateRequest request);

    PageResponse<NursingPagingResponse> paging(NursingPagingRequest request);

    NursingDetailResponse detail(Long nursingId);

    NursingSimpleResponse get(Long nursingId);

    /**
     * 只会返回有效状态的护工
     * @param nursingIdList
     * @return
     */
    Map<Long,NursingSimpleResponse> listMapRef(List<Long> nursingIdList);

    Boolean delete(Long nursingId,Long deletedId);

    List<NursingSimpleResponse> list(List<Long> nursingIdList);

    // C端
    /**
     * 查询护工单挑记录
     */

    // B端

    /**
     * 护工基本资料修改
     */

    /**
     * 护工星级修改
     */

    /**
     * 护工状态修改
     *    待审核 -> 启用 -> 停用 -> 启用
     */   /**
     * 护工状态修改
     *    待审核 -> 启用 -> 停用 -> 启用
     */

    /**
     * 查询护工排班
     *   护工ID查询护工的排班
     */
}
