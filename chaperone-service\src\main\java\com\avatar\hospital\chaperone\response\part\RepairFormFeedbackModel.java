package com.avatar.hospital.chaperone.response.part;

import com.avatar.hospital.chaperone.template.serialize.ToStringAttachmentsSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 反馈单
 * @author: sp0372
 * @create: 2023-10-27 13:15
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormFeedbackModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 报修单反馈ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 报修单反馈 人员ID
     */
    private Long createBy;


    /**
     * 报修单反馈 人员名称
     */
    private String createByName;

    /**
     * 报修单反馈 时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createAt;

    /**
     * 反馈结果类型: 1 完成任务 2 需要协助 3 需要使用备件
     */
    @NotNull
    private Integer type;

    /**
     * 反馈情况说明
     */
    private String remark;

    /**
     * 附件URL,数组
     */
    @JsonSerialize(using = ToStringAttachmentsSerializer.class)
    private String attachments;
}
