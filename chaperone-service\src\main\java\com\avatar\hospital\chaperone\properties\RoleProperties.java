package com.avatar.hospital.chaperone.properties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/27
 */
@Data
@Slf4j
@ConfigurationProperties(prefix = RoleProperties.PREFIX)
@Component
public class RoleProperties implements Serializable {

    public static final String PREFIX = "role";

    /**
     * 查看所有医院层级列表的角色权限字符串
     */
    private String hospitalListRoleKey;

    /**
     * 查看所有部门层级列表的角色权限字符串
     */
    private String departmentListRoleKey;

    /**
     * 查看组织机构树的角色权限字符串
     */
    private String organizationTreeRoleKey;

    /**
     * 知识库创建文件夹权限字符串
     */
    private String mkdirRoleKey;

}
