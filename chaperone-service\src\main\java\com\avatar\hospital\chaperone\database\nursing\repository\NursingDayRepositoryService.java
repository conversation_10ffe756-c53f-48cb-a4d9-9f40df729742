package com.avatar.hospital.chaperone.database.nursing.repository;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDayDO;
import com.avatar.hospital.chaperone.request.nursing.NursingDayPagingRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingOrderDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 护工-考勤信息; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface NursingDayRepositoryService extends IService<NursingDayDO> {
    /**
     *
     * @param nursingDayDOlist
     * @param nursingOrderDOlist
     */
    void saveByAdd(List<NursingDayDO> nursingDayDOlist, List<NursingOrderDO> nursingOrderDOlist);

    /**
     * 查询排班数量
     *
     * @param request
     * @return
     */
    Long countByB(NursingDayPagingRequest request);

    /**
     * 查询排班列表
     *
     * @param request
     * @return
     */
    List<NursingDayPagingResponse> listByB(NursingDayPagingRequest request);

    /**
     * 根据护工id和日期查询排班
     *
     * @param nursingId
     * @param date
     * @return
     */
    NursingDayDO findByNursingIdAndDate(Long nursingId, Integer date);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    NursingDayDO findById(Long id);

    /**
     * 新增排班
     *
     * @param nursingDayDO
     * @return
     */
    Long add(NursingDayDO nursingDayDO);

    /**
     * 更新排班
     *
     * @param nursingDayDO
     * @return
     */
    Boolean incrementUpdate(NursingDayDO nursingDayDO);

    /**
     *
     * @param nursingId
     * @param startDate
     * @param endDate
     * @return  <date,nursingDay>
     */
    Map<Integer, NursingDayDO> findAllByNursingId(Long nursingId, Integer startDate, Integer endDate);


    /**
     * 更新排班
     *
     * @param nursingDayDO
     * @return
     */
    Boolean incrementByNursingUpdate(NursingDayDO nursingDayDO);

}
