package com.avatar.hospital.chaperone.database.plan.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description 0-未完成，1-已完成，2-已过期）
 * @date 2023/10/26 19:55
 */
@Getter
public enum TaskStatusType {
    NONE(-1, "未知"),
    NON_COMPLETED(0, "未完成"),
    COMPLETED(1, "已完成"),
    EXPIRED(2, "已过期"),

    ;

    private final Integer code;

    private final String describe;


    TaskStatusType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static TaskStatusType of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (TaskStatusType type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
