package com.avatar.hospital.chaperone.builder.part;


import com.avatar.hospital.chaperone.database.part.dataobject.PartDO;
import com.avatar.hospital.chaperone.database.part.enums.PartStatusType;

import java.time.LocalDateTime;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:58
 **/
public class PartBuilder {
    public static  PartDO use(PartDO part,
                              LocalDateTime operateTime,
                              Long applyId,
                              Long operator) {
        PartDO updateEntity = new PartDO();
        updateEntity.setId(part.getId());
        updateEntity.setSparePartApplyId(applyId);
        updateEntity.setStatus(PartStatusType.USED.getCode());
        updateEntity.setOutTime(operateTime);
        updateEntity.setUpdateBy(operator);
        updateEntity.setUpdatedAt(operateTime);
        return updateEntity;
    }
}
