package com.avatar.hospital.chaperone.consumer.controller.order;

import com.avatar.hospital.chaperone.service.order.OrderBillService;
import com.google.common.base.Throwables;
import io.netty.util.CharsetUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-23 15:57
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/notify")
public class OrderNotifyController {
    /**
     * 通知微信处理成功
     */
    private static String wxSuccessXml = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";

    /**
     * 通知微信处理失败
     */
    private static String wxFailureXml = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[ERROR]]></return_msg></xml>";

    private final OrderBillService orderBillService;

    /**
     * 微信支付回调
     */
    @RequestMapping("/wx")
    public void notifyWx(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("OrderNotifyController 微信支付回调");
        StringBuilder sb = new StringBuilder();
        BufferedReader bufferedReader = null;
        try {
            bufferedReader = new BufferedReader(new InputStreamReader(request.getInputStream(), CharsetUtil.UTF_8));
            for (String str = bufferedReader.readLine(); str != null; str = bufferedReader.readLine()) {
                sb.append(str);
            }
        } catch (Exception ex) {
            log.warn("OrderNotifyController 微信支付回调 parse fail:{}", Throwables.getStackTraceAsString(ex));
            response.getWriter().write(wxFailureXml);
        } finally {
            if (bufferedReader != null) {
                bufferedReader.close();
            }
        }
        String xml = sb.toString();
        log.info("OrderNotifyController 微信支付回调 notifyWx start >> json:{}", xml);
        orderBillService.payUpdateByNotifyWx(xml);
        response.getWriter().write(wxSuccessXml);
    }
}
