package com.avatar.hospital.chaperone.response.plan;

import com.avatar.hospital.chaperone.annotation.serializer.LocalDateTimeSerializer;
import com.avatar.hospital.chaperone.response.BaseVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 17:28
 */
@Data
public class PlanTaskVO extends BaseVO implements Serializable {

    /**
     * 任务id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 任务编号（字母+日期）
     */
    private String code;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 状态（0-未完成，1-已完成，2-已过期）
     *
     * @see com.avatar.hospital.chaperone.database.plan.enums.TaskStatusType
     */
    private Integer status;

    /**
     * 巡检计划ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long planId;
    /**
     * 设备ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long deviceId;
    /**
     * 设备ID
     */
    private String deviceName;
    /**
     * 是否需要报修
     */
    private Boolean isRepair;

    /**
     * 备注
     */
    private String remark;

    /**
     * 完成时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime completedTime;

    /**
     * 计划编号
     */
    private String planCode;
    /**
     * 计划备注
     */
    private String planRemark;
    /**
     * 所属院区ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;
    /**
     * 所属院区名称
     */
    private String orgName;
    /**
     * 关联人员
     */
    private List<PlanVO.Value> executors;
    /**
     * 关联部门
     */
    private List<PlanVO.Value> departments;

    /**
     * 保修单编号
     */
    private String repairFormCode;
    /**
     * 保修单ID
     */
    private Long repairFormId;

}
