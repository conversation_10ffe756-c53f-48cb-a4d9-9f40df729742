package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.request.ExportPageRequest;
import com.avatar.hospital.chaperone.service.order.dto.OrderConsumerlogExportDTO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import java.util.List;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderConsumerExportPageRequest extends ExportPageRequest implements OperatorReq {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     *  开始时间 yyyyMMdd(包括) , 结束时间 yyyyMMdd(包括)
     */
    private List<String> date;

    /**
     * 开始时间 yyyyMMdd(包括)
     */
    private String startTime;

    /**
     * 结束时间 yyyyMMdd(包括)
     */
    private String endTime;

    /**
     * 是否是有效记录 不传(全部) 1 有效 其他 无效记录
     */
    private Long version;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    @Override
    public Class getClazz() {
        return OrderConsumerlogExportDTO.class;
    }

    @Override
    public String getFileName() {
        return "每日分成";
    }
}
