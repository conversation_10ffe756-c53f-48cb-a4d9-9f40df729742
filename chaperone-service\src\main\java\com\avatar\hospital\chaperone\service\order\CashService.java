package com.avatar.hospital.chaperone.service.order;

import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.CashBalanceResponse;
import com.avatar.hospital.chaperone.response.order.CashIdResponse;
import com.avatar.hospital.chaperone.response.order.CashResponse;

/**
 * @program: hospital-chaperone
 * @description: 订单查询接口
 * @author: sp0372
 * @create: 2023-10-11 16:41
 **/
public interface CashService {

    // B端

    /**
     * 查询现金记录
     */
    PageResponse<CashResponse> paging(CashPageRequest request);



    /**
     * 现金提取请求
     */
    CashIdResponse extract(CashExtractRequest request);


    /**
     * 现金存入
     */
    CashIdResponse deposit(CashDepositRequest request);

    /**
     * 现金余额
     */
    CashBalanceResponse balance(CashBalanceRequest request);
}
