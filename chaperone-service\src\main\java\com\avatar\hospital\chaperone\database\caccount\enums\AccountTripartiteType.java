package com.avatar.hospital.chaperone.database.caccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum AccountTripartiteType {

    /**
     * 微信
     */
    WECHAT(1, "微信"),

    ;

    private final Integer type;

    private final String describe;


    AccountTripartiteType(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }

    public static AccountTripartiteType of(Integer type) {
        if (type == null) {
            return null;
        }
        for (AccountTripartiteType accountTripartiteType : values()) {
            if (type.equals(accountTripartiteType.getType())) {
                return accountTripartiteType;
            }
        }
        return null;
    }
}
