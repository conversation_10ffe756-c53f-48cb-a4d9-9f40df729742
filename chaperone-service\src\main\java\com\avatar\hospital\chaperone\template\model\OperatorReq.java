package com.avatar.hospital.chaperone.template.model;


import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 操作人
 * @author: sp0372
 * @create: 2023-10-23 15:00
 **/
public interface OperatorReq {

    /**
     * 获取操作用户
     * @return
     */
    Operator getOperatorUser();

    /**
     * 操作用户赋值
     * @param operator
     */
    void setOperatorUser(Operator operator);

    /**
     * 获取operatorId
     * @return
     */
    default Long getOperator() {
        Operator operatorUser = getOperatorUser();
        if (Objects.isNull(operatorUser)) {
            return null;
        }
        return operatorUser.getId();
    }
}
