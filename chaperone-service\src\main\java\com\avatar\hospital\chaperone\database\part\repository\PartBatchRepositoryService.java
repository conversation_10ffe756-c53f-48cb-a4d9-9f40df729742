package com.avatar.hospital.chaperone.database.part.repository;

import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 备品备件批次 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PartBatchRepositoryService extends IService<PartBatchDO> {

    /**
     * 更新数量
     * @param id
     * @param quality 使用数量
     * @return
     */
    Boolean updateBalance(Long id,
                          Integer quality);
}
