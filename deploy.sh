#!/bin/bash

# 医院陪护系统 Docker 部署脚本
# 作者: 系统管理员
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装，请先安装 Maven"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 构建应用
build_application() {
    log_info "开始构建应用..."
    
    # 清理并编译
    mvn clean package -DskipTests
    
    if [ $? -eq 0 ]; then
        log_info "应用构建成功"
    else
        log_error "应用构建失败"
        exit 1
    fi
}

# 构建Docker镜像
build_images() {
    log_info "开始构建 Docker 镜像..."
    
    # 构建后台管理系统镜像
    log_debug "构建后台管理系统镜像..."
    docker build -f Dockerfile.web -t hospital-chaperone-web:latest .
    
    # 构建客户端系统镜像
    log_debug "构建客户端系统镜像..."
    docker build -f Dockerfile.consumer -t hospital-chaperone-consumer:latest .
    
    log_info "Docker 镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 创建网络（如果不存在）
    docker network create chaperone-network 2>/dev/null || true
    
    # 启动服务
    docker-compose up -d
    
    log_info "服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 等待服务启动
    sleep 30
    
    # 检查MySQL
    if docker-compose ps mysql | grep -q "Up"; then
        log_info "MySQL 服务运行正常"
    else
        log_error "MySQL 服务启动失败"
    fi
    
    # 检查Redis
    if docker-compose ps redis | grep -q "Up"; then
        log_info "Redis 服务运行正常"
    else
        log_error "Redis 服务启动失败"
    fi
    
    # 检查后台管理系统
    if curl -f http://localhost:8081/actuator/health &>/dev/null; then
        log_info "后台管理系统健康检查通过"
    else
        log_warn "后台管理系统健康检查失败，请检查日志"
    fi
    
    # 检查客户端系统
    if curl -f http://localhost:8080/actuator/health &>/dev/null; then
        log_info "客户端系统健康检查通过"
    else
        log_warn "客户端系统健康检查失败，请检查日志"
    fi
}

# 显示服务信息
show_services_info() {
    log_info "服务信息:"
    echo "=================================="
    echo "后台管理系统: http://localhost:8081"
    echo "客户端系统:   http://localhost:8080"
    echo "MySQL:       localhost:3306"
    echo "Redis:       localhost:6379"
    echo "Nginx:       http://localhost:80"
    echo "=================================="
    echo ""
    echo "查看日志命令:"
    echo "docker-compose logs -f [service_name]"
    echo ""
    echo "停止服务命令:"
    echo "docker-compose down"
}

# 主函数
main() {
    log_info "开始部署医院陪护系统..."
    
    # 检查参数
    case "${1:-deploy}" in
        "build")
            check_dependencies
            build_application
            build_images
            ;;
        "start")
            start_services
            check_services
            show_services_info
            ;;
        "deploy")
            check_dependencies
            build_application
            build_images
            start_services
            check_services
            show_services_info
            ;;
        "stop")
            log_info "停止服务..."
            docker-compose down
            log_info "服务已停止"
            ;;
        "restart")
            log_info "重启服务..."
            docker-compose restart
            check_services
            show_services_info
            ;;
        "logs")
            docker-compose logs -f ${2:-}
            ;;
        "status")
            docker-compose ps
            ;;
        *)
            echo "用法: $0 {deploy|build|start|stop|restart|logs|status}"
            echo ""
            echo "命令说明:"
            echo "  deploy  - 完整部署（构建+启动）"
            echo "  build   - 仅构建应用和镜像"
            echo "  start   - 仅启动服务"
            echo "  stop    - 停止服务"
            echo "  restart - 重启服务"
            echo "  logs    - 查看日志"
            echo "  status  - 查看服务状态"
            exit 1
            ;;
    esac
    
    log_info "操作完成"
}

# 执行主函数
main "$@"
