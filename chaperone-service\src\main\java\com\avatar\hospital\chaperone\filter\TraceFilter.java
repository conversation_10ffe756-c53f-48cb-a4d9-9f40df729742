package com.avatar.hospital.chaperone.filter;


import com.avatar.hospital.chaperone.utils.TraceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
public class TraceFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger(TraceFilter.class);

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            TraceUtils.setTraceId(request, response);
            chain.doFilter(request, response);
        } catch (Throwable throwable) {
            log.error("[TraceFilter] doFilter is error", throwable);
            throw throwable;
        } finally {
            TraceUtils.clear();
        }
    }


}
