package com.avatar.hospital.chaperone.web.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.order.OrderBillModifyTradeRequest;
import com.avatar.hospital.chaperone.request.order.OrderChangelogPageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderBillIdResponse;
import com.avatar.hospital.chaperone.response.order.OrderChangelogResponse;
import com.avatar.hospital.chaperone.service.order.*;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * B端陪护单-修改记录
 * @program: hospital-chaperone
 * @description: 套餐
 * @author: sp0372
 * @create: 2023-10-13 10:07
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/order/changelog")
public class OrderChangeLogController {
    private final OrderChangelogService orderChangelogService;


    /**
     * 查询-修改记录
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<OrderChangelogResponse>> paging(@RequestBody OrderChangelogPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderChangelogService.paging(request);
        });
    }

}
