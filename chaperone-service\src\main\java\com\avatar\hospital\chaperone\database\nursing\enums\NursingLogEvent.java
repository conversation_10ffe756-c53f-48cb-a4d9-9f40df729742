package com.avatar.hospital.chaperone.database.nursing.enums;

import com.avatar.hospital.chaperone.database.order.enums.OrderLogEvent;
import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.avatar.hospital.chaperone.service.nursing.dto.NursingDayLogDTO;
import com.avatar.hospital.chaperone.service.order.dto.OrderChangeLogDTO;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum NursingLogEvent implements IEnumConvert<String> {
    NONE("NONE", "未知", NursingDayLogDTO.AbsCompareDTO.class),
    NURSING_DAY_ADD("NURSING_DAY_ADD","排班新增", NursingDayLogDTO.Create.class),
    ;

    private final String status;

    private final String describe;

    private final Class clazz;


    NursingLogEvent(String status, String describe,Class clazz) {
        this.status = status;
        this.describe = describe;
        this.clazz = clazz;
    }

    public static NursingLogEvent of(String status) {
        if (Objects.isNull(status)) {
            return NONE;
        }
        for (NursingLogEvent itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(String val) {
        NursingLogEvent e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }
}
