package com.avatar.hospital.chaperone.database.repair.mapper;

import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 报修单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface RepairFormMapper extends BaseMapper<RepairFormDO> {

    Long countForC(@Param("status") Integer status,
                   @Param("operator") Long operator);

    List<RepairFormDO> listForC(@Param("status") Integer status,
                                @Param("operator") Long operator,
                                @Param("offset") Long offset,
                                @Param("size") Long size);
}
