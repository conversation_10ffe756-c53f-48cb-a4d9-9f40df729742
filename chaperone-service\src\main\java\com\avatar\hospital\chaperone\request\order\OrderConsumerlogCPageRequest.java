package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:00
 **/
@Data
public class OrderConsumerlogCPageRequest extends PageRequest implements OperatorReq {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 查询月份 yyyyMM
     */
    private Integer month;

    /**
     * 开始时间 yyyyMMdd(包括)
     */
    private Integer startTime;

    /**
     * 结束日期 yyyyMMdd(包括)
     */
    private Integer endTime;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
