package com.avatar.hospital.chaperone.utils;

import java.util.UUID;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/18
 */
public class FileUtils {

    /**
     * 获取文件后缀名称
     *
     * @param fileName 文件名称
     * @return -
     */
    public static String getFileExtName(String fileName) {
        if (fileName == null) {
            return null;
        }
        int index = fileName.lastIndexOf(".");
        if (index == -1) {
            return "";
        }
        return fileName.substring(index + 1);
    }


    /**
     * 根据文件后缀生成随机文件名称
     *
     * @param fileExtName 文件后缀名
     * @return 文件名称
     */
    public static String getRandomFileName(String fileExtName) {
        String resultFileName = UUID.randomUUID().toString().replaceAll("-", "").toLowerCase();
        if (fileExtName == null) {
            return resultFileName;
        }
        return resultFileName + "." + fileExtName;
    }

}
