package com.avatar.hospital.chaperone.database.order.enums;

import lombok.Getter;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-01 11:47
 **/
@Getter
public enum OrderPresetValueStatus {

    CLEAN(2, "清除"),

    NO(0, "未处理"),
    YES(1, "已处理"),
    ;

    private final Integer status;

    private final String describe;

    OrderPresetValueStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }
}
