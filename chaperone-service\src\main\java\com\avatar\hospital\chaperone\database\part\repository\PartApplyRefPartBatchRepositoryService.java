package com.avatar.hospital.chaperone.database.part.repository;

import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartStatisticsModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 备件使用申请单关联备件批次 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PartApplyRefPartBatchRepositoryService extends IService<PartApplyRefPartBatchDO> {

    List<PartApplyRefPartBatchDO> getByApplyIdList(List<Long> applyIdList);

    PartApplyRefPartBatchDO getByApplyId(Long applyId);

    List<PartStatisticsModel> getStatistics(LocalDateTime start, LocalDateTime end);
}
