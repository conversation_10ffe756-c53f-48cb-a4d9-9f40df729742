package com.avatar.hospital.chaperone.database.order.mapper;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单-套餐关联表; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderItemMapper extends BaseMapper<OrderItemDO> {
    /**
     * 消费涉及的订单商品数据
     * @return
     */
    List<OrderItemDO> listByConsumerLog(@Param("itemIdList")List<Long> itemIdList,
                                        @Param("dateHour")Integer dateHour);
}
