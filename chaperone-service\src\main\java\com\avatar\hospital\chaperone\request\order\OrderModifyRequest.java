package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.database.nursing.enums.Gender;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import com.avatar.hospital.chaperone.template.serialize.ToDateTimeHForStringDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:10
 **/
@Data
public class OrderModifyRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @NotNull
    private Long id;


    /**
     * 服务类型, 0 其他 1 住院陪护
     * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
     */
    private Integer serverType;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 病人性别,-1未知 0 女 1 男
     * @see Gender
     */
    private Integer patientGender;

    /**
     * 病人出生日期 yyyy-MM-dd
     */
    private String patientBirthday;

    /**
     * 家属联系电话
     */
    private String phone;

    /**
     * 病人病情描述
     */
    private String patientStateDesc;

    /**
     * 特殊要求
     */
    private String specificDesc;

    /**
     * 机构ID(医院)
     */
    private Long orgId;

    /**
     * 科室
     */
    private String departments;

    /**
     * 床号
     */
    private String bedNo;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 陪护开始日期，格式：时间戳/毫秒
     */
    @JsonDeserialize(using = ToDateTimeHForStringDeserializer.class)
    private Integer startTime;

    /**
     * 陪护终止日期，格式：时间戳/毫秒
     */
    @JsonDeserialize(using = ToDateTimeHForStringDeserializer.class)
    private Integer endTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 套餐ID列表
     */
    private List<Long> itemIdList;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
