package com.avatar.hospital.chaperone.request;

import com.avatar.hospital.chaperone.utils.ExportParamDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public abstract class ExportPageRequest implements ExportParamDTO, Serializable {

    private static final long serialVersionUID = -1537431942438821886L;

    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * 页数
     */
    private Integer pageSize = 500;

    public Integer getPageIndex() {
        if (Objects.isNull(pageIndex) || pageIndex <= 0 ) {
            pageIndex = 1;
        }
        return pageIndex;
    }

    public Integer getPageSize() {
        return 500;
    }


    public <T> Page<T> ofPage() {
        Page<T> page = PageDTO.of(getPageIndex(), getPageSize());
        return page;
    }

    public Integer offset() {
        return (getPageIndex() - 1) * getPageSize();
    }

    public Long getPageIndexLong() {
        return getPageIndex().longValue();
    }

    public Long getPageSizeLong() {
        return getPageSize().longValue();
    }

    @Override
    public Integer pageIndexAdd1() {
        return pageIndex ++;
    }

    @Override
    public String getFileName() {
        return "数据导出";
    }
}
