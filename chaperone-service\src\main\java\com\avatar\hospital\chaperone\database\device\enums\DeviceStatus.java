package com.avatar.hospital.chaperone.database.device.enums;

import com.avatar.hospital.chaperone.database.nursing.enums.NursingStatus;
import lombok.Getter;

@Getter
public enum DeviceStatus {
    //1-启用，2-停用，3-维修中
    NONE(-1, "未知"),

    /**
     * 启用
     */
    ENABLE(1, "启用"),

    /**
     * 停用
     */
    DISABLE(2, "停用"),

    /**
     * 维修中
     */
    MAINTENANCE(3,"维修中")
    ;

    private final Integer status;

    private final String describe;


    DeviceStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static DeviceStatus of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (DeviceStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
