package com.avatar.hospital.chaperone.response.item;

import com.avatar.hospital.chaperone.database.item.dataobject.ItemDO;
import com.avatar.hospital.chaperone.database.item.dataobject.ItemOrgDO;
import com.avatar.hospital.chaperone.database.item.enums.ItemStatus;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 15:47
 **/
@Data
public class ItemCResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 套餐类型：参考，server_type
     * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
     */
    private Integer serverType;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 服务价格(单位：分/天)
     */
    private Integer price;

    /**
     * 状态(影响C端展示)：1 上架，0 下架
     * @see ItemStatus
     */
    private Integer status;

    /**
     * 护工星级 1 ~ 5 星
     */
    private Integer nursingStar;

    /**
     * 详细描述
     */
    private String remark;

    /**
     * 封面图
     */
    private String coverPicture;


    /**
     * 生效时间(小时) 0 ~ 23
     */
    private Integer chargingTime;

    // build

    public static ItemCResponse build(ItemDO itemDO, List<ItemOrgDO> itemOrgDOList) {
        ItemCResponse obj = new ItemCResponse();
        obj.setId(itemDO.getId());
        obj.setServerType(itemDO.getServerType());
        obj.setName(itemDO.getName());
        obj.setPrice(itemDO.getPrice());
        obj.setStatus(itemDO.getStatus());
        obj.setNursingStar(itemDO.getNursingStar());
        obj.setRemark(itemDO.getRemark());
        obj.setCoverPicture(itemDO.getCoverPicture());
        return obj;
    }

    public static ItemCResponse buildByItemDO(ItemDO d) {
        ItemCResponse itemResponse = new ItemCResponse();
        itemResponse.setId(d.getId());
        itemResponse.setServerType(d.getServerType());
        itemResponse.setName(d.getName());
        itemResponse.setPrice(d.getPrice());
        itemResponse.setStatus(d.getStatus());
        itemResponse.setNursingStar(d.getNursingStar());
        itemResponse.setRemark(d.getRemark());
        itemResponse.setCoverPicture(d.getCoverPicture());
        itemResponse.setChargingTime(d.getChargingTime());
        return itemResponse;
    }
}
