package com.avatar.hospital.chaperone.request.baccount;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/19
 */
@Data
public class WebAccountUpdatePasswordRequest implements Serializable {

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 新的密码
     */
    private String newPassword;

    /**
     * 操作人ID
     */
    private Long updateBy;

}
