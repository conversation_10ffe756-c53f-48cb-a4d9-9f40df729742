package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 定时任务执行记录;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Getter
@Setter
  @TableName("t_scheduled_log")
public class ScheduledLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 任务名称
     */
      private String name;

      /**
     * 任务code
     */
      private String code;

      /**
     * 执行耗时（ms）
     */
      private Integer cost;

      /**
     * 执行结果 0 执行中 1 成功 2 失败
     */
      private Integer result;

      /**
     * 结果备注
     */
      private String remark;


  public ScheduledLogDO failByLock() {
    ScheduledLogDO logDO = new ScheduledLogDO();
    logDO.setId(id);
    logDO.setResult(2);
    logDO.setRemark("fail lock");
    return logDO;
  }

  public ScheduledLogDO successByLock(Long cost) {
    ScheduledLogDO logDO = new ScheduledLogDO();
    logDO.setId(id);
    logDO.setResult(1);
    logDO.setCost(cost.intValue());
    logDO.setRemark("success");
    return logDO;
  }

  public ScheduledLogDO failByException(Long cost) {
    ScheduledLogDO logDO = new ScheduledLogDO();
    logDO.setId(id);
    logDO.setResult(2);
    logDO.setCost(cost.intValue());
    logDO.setRemark("fail exception");
    return logDO;
  }
}
