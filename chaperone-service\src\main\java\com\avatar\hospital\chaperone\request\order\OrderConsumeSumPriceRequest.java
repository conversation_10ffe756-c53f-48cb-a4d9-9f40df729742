package com.avatar.hospital.chaperone.request.order;

import lombok.Data;

import java.io.Serializable;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderConsumeSumPriceRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 开始日期(包括),格式yyyyMMdd
     */
    private Integer startDate;

    /**
     * 结束日期(包括),格式yyyyMMdd
     */
    private Integer endDate;

    public static final OrderConsumeSumPriceRequest build(Long orderId) {
        OrderConsumeSumPriceRequest obj = new OrderConsumeSumPriceRequest();
        obj.setOrderId(orderId);
        return obj;
    }

}
