package com.avatar.hospital.chaperone.database.part.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/26 19:55
 */
@Getter
public enum PartBatchStatusType {
    NONE(-1, "未知"),
    NON_STOCK(0, "待入库"),
    STOCK(1, "已入库"),

    ;

    private final Integer code;

    private final String describe;


    PartBatchStatusType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static PartBatchStatusType of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (PartBatchStatusType type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
