package com.avatar.hospital.chaperone.template.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 16:36
 **/
public class ToStringForLongListSerializer extends JsonSerializer<List<Long>> {
    @Override
    public void serialize(List<Long> longs, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {

        if (CollectionUtils.isEmpty(longs)) {
            jsonGenerator.writeArray(new String[]{},0,0);
        }else {
            String[] list = longs.stream()
                    .map(item -> item.toString())
                    .toArray(String[]::new);
            jsonGenerator.writeArray(list,0,list.length);
        }
    }
}
