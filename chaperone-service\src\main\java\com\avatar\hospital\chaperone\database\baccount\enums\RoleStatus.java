package com.avatar.hospital.chaperone.database.baccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum RoleStatus {

    /**
     * 禁用
     */
    DISABLE(0, "禁用"),


    /**
     * 启用
     */
    ENABLE(1, "启用"),


    ;

    private final Integer status;

    private final String describe;


    RoleStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static RoleStatus of(Integer status) {
        if (status == null) {
            return null;
        }
        for (RoleStatus roleStatus : values()) {
            if (status.equals(roleStatus.getStatus())) {
                return roleStatus;
            }
        }
        return null;
    }
}
