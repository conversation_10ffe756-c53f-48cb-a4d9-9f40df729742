package com.avatar.hospital.chaperone.database.caccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum AccountSex {

    /**
     * 未知
     */
    DISABLE(0, "未知"),


    /**
     * 男
     */
    MAN(1, "男"),

    /**
     * 女
     */
    WOMAN(2, "女"),


    ;

    private final Integer status;

    private final String describe;


    AccountSex(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static AccountSex of(Integer status) {
        if (status == null) {
            return null;
        }
        for (AccountSex accountSex : values()) {
            if (status.equals(accountSex.getStatus())) {
                return accountSex;
            }
        }
        return null;
    }
}
