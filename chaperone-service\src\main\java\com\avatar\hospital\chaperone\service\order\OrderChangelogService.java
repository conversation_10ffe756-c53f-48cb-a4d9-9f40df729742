package com.avatar.hospital.chaperone.service.order;

import com.avatar.hospital.chaperone.request.order.OrderChangelogPageRequest;
import com.avatar.hospital.chaperone.request.order.OrderChangelogSaveRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderChangelogResponse;
import com.avatar.hospital.chaperone.response.order.OrderChangelogSaveResponse;

/**
 * @program: hospital-chaperone
 * @description: 订单修改记录
 * @author: sp0372
 * @create: 2023-10-11 17:51
 **/
public interface OrderChangelogService {

    /**
     * 分页查询修改记录
     */
    PageResponse<OrderChangelogResponse> paging(OrderChangelogPageRequest request);


    /**
     * 插入一条订单修改记录
     */
    OrderChangelogSaveResponse add(OrderChangelogSaveRequest request);

}
