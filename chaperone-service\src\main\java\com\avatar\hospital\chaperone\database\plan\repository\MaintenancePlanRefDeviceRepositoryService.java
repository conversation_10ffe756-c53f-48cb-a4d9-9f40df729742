package com.avatar.hospital.chaperone.database.plan.repository;

import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanRefDeviceDO;
import com.avatar.hospital.chaperone.database.plan.repository.base.PlanRefRepositoryService;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 维保计划关联设备 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface MaintenancePlanRefDeviceRepositoryService extends IService<MaintenancePlanRefDeviceDO>, PlanRefRepositoryService {

}
