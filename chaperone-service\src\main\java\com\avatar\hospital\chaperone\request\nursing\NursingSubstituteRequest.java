package com.avatar.hospital.chaperone.request.nursing;

import lombok.Data;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.io.Serializable;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingSubstituteRequest implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 替班医护
     */
    private Long nursingId;

    /**
     * 替班医护名称
     */
    private String nursingName;

    /**
     * 更新者ID
     */
    private Long updateBy;
}
