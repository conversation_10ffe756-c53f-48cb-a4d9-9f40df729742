package com.avatar.hospital.chaperone.database.baccount.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountOrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.baccount.mapper.OrganizationMapper;
import com.avatar.hospital.chaperone.database.baccount.repository.AccountOrganizationRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.OrganizationRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * B端组织机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrganizationRepositoryServiceImpl extends ServiceImpl<OrganizationMapper, OrganizationDO> implements OrganizationRepositoryService {

    private final AccountOrganizationRepositoryService accountOrganizationRepositoryService;

    @Override
    public Long add(OrganizationDO organizationDO) {
        if (organizationDO == null) {
            return null;
        }
        organizationDO.setId(IdUtils.getId());
        if (!save(organizationDO)) {
            organizationDO.setId(null);
        }
        return organizationDO.getId();
    }

    @Override
    public Boolean incrementUpdate(OrganizationDO organizationDO) {
        if (organizationDO == null) {
            return false;
        }
        LambdaUpdateWrapper<OrganizationDO> updateWrapper = updateWrapper();
        updateWrapper.set(organizationDO.getName() != null, OrganizationDO::getName, organizationDO.getName());
        updateWrapper.set(organizationDO.getLiaisonName() != null, OrganizationDO::getLiaisonName, organizationDO.getLiaisonName());
        updateWrapper.set(organizationDO.getLiaisonPhoneNumber() != null, OrganizationDO::getLiaisonPhoneNumber, organizationDO.getLiaisonPhoneNumber());
        updateWrapper.set(organizationDO.getBankAccountNumber() != null, OrganizationDO::getBankAccountNumber, organizationDO.getBankAccountNumber());
        updateWrapper.set(organizationDO.getStatus() != null, OrganizationDO::getStatus, organizationDO.getStatus());
        updateWrapper.set(organizationDO.getSort() != null, OrganizationDO::getSort, organizationDO.getSort());
        updateWrapper.set(organizationDO.getUpdateBy() != null, OrganizationDO::getUpdateBy, organizationDO.getUpdateBy());

        updateWrapper.eq(OrganizationDO::getId, organizationDO.getId());
        return update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByIds(Set<Long> ids, Long updateBy) {
        if (CollectionUtils.isEmpty(ids) || updateBy == null) {
            return true;
        }
        LambdaUpdateWrapper<OrganizationDO> updateWrapper = updateWrapper();
        updateWrapper.set(OrganizationDO::getDeleted, System.currentTimeMillis());
        updateWrapper.set(OrganizationDO::getUpdateBy, updateBy);
        if (ids.size() == 1) {
            updateWrapper.eq(OrganizationDO::getId, ids.toArray()[0]);
        }
        else {
            updateWrapper.in(OrganizationDO::getId, ids);
        }
        if (!update(updateWrapper)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        // 删除关联数据
        List<AccountOrganizationDO> accountOrganizationList = accountOrganizationRepositoryService.findByOrganizationIds(ids);
        if (CollectionUtils.isEmpty(accountOrganizationList)) {
            return true;
        }
        if (!Boolean.TRUE.equals(accountOrganizationRepositoryService.deleteByOrganizationIds(ids))) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        return true;
    }

    @Override
    public OrganizationDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<OrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrganizationDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<OrganizationDO> findByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrganizationDO> queryWrapper = queryWrapper();
        if (ids.size() == 1) {
            queryWrapper.eq(OrganizationDO::getId, ids.toArray()[0]);
        }
        else {
            queryWrapper.in(OrganizationDO::getId, ids);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrganizationDO> listAll() {
        LambdaQueryWrapper<OrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(OrganizationDO::getSort);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrganizationDO> findByIdsAndLevel(Set<Long> ids, Integer level) {
        if (CollectionUtils.isEmpty(ids) || level == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrganizationDO::getLevel, level);

        if (ids.size() == 1) {
            queryWrapper.eq(OrganizationDO::getId, ids.toArray()[0]);
        }
        else {
            queryWrapper.in(OrganizationDO::getId, ids);
        }
        queryWrapper.orderByDesc(OrganizationDO::getSort);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrganizationDO> findByIdsAndLevelAndTypesAndStatus(Set<Long> ids, Integer level, Set<Integer> types, Integer status) {
        if (CollectionUtils.isEmpty(ids) || level == null || CollectionUtils.isEmpty(types) || status == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrganizationDO::getLevel, level);
        queryWrapper.eq(OrganizationDO::getStatus, status);

        if (ids.size() == 1) {
            queryWrapper.eq(OrganizationDO::getId, ids.toArray()[0]);
        }
        else {
            queryWrapper.in(OrganizationDO::getId, ids);
        }
        queryWrapper.orderByDesc(OrganizationDO::getSort);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrganizationDO> findByParentId(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrganizationDO> queryWrapper = queryWrapper();
        if (ids.size() == 1) {
            queryWrapper.eq(OrganizationDO::getParentId, ids.toArray()[0]);
        }
        else {
            queryWrapper.in(OrganizationDO::getParentId, ids);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrganizationDO> findByLevelAndTypesAndStatus(Integer level, Set<Integer> types, Integer status) {
        if (level == null || CollectionUtils.isEmpty(types) || status == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrganizationDO::getLevel, level);
        queryWrapper.in(OrganizationDO::getType, types);
        queryWrapper.eq(OrganizationDO::getStatus, status);
        queryWrapper.orderByDesc(OrganizationDO::getSort);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrganizationDO> findByIdsAndLevelAndTypes(Set<Long> ids, Integer level, Set<Integer> types) {
        if (CollectionUtils.isEmpty(ids) || level == null || CollectionUtils.isEmpty(types)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrganizationDO::getId, ids);
        queryWrapper.eq(OrganizationDO::getLevel, level);
        queryWrapper.in(OrganizationDO::getType, types);
        queryWrapper.orderByDesc(OrganizationDO::getSort);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrganizationDO> findByParentIdAndLevelAndStatus(Long parentId, Integer level, Integer status) {
        if (parentId == null || level == null || status == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrganizationDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrganizationDO::getParentId, parentId);
        queryWrapper.eq(OrganizationDO::getLevel, level);
        queryWrapper.eq(OrganizationDO::getStatus, status);
        queryWrapper.orderByDesc(OrganizationDO::getSort);
        return baseMapper.selectList(queryWrapper);
    }

    private LambdaQueryWrapper<OrganizationDO> queryWrapper() {
        LambdaQueryWrapper<OrganizationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrganizationDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<OrganizationDO> updateWrapper() {
        LambdaUpdateWrapper<OrganizationDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrganizationDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
