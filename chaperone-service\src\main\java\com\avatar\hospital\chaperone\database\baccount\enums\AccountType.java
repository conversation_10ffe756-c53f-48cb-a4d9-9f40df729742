package com.avatar.hospital.chaperone.database.baccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum AccountType {

    /**
     * 调度员
     */
    DISPATCHER(1, "调度员"),


    /**
     * 收银员
     */
    CASHIER(2, "收银员"),


    ;

    private final Integer type;

    private final String describe;


    AccountType(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }

    public static AccountType of(Integer type) {
        if (type == null) {
            return null;
        }
        for (AccountType accountType : values()) {
            if (type.equals(accountType.getType())) {
                return accountType;
            }
        }
        return null;
    }
}
