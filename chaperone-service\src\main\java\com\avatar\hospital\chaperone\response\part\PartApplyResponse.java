package com.avatar.hospital.chaperone.response.part;

import com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:53
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PartApplyResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审批ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 审批单编号
     */
    private String code;

    /**
     * 状态（1-待审核，2-允许使用，3-不允许使用）
     */
    private Integer status;

    /**
     * 反馈信息
     */
    private RepairFormFeedbackModel repairFormFeedbackInfo;

    /**
     * 备件信息
     */
    private PartApplyModel partInfo;

    /**
     * 审核结果说明
     */
    private String auditRemark;

    // 报修单数据

    /**
     * 报修单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long repairFormId;


    /**
     * 报修单Code
     */
    private String repairFormCode;

}
