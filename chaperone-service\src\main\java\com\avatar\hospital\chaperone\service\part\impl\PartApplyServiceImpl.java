package com.avatar.hospital.chaperone.service.part.impl;

import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.builder.part.PartApplyBuilder;
import com.avatar.hospital.chaperone.builder.part.PartBuilder;
import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartDO;
import com.avatar.hospital.chaperone.database.part.enums.PartApplyStatus;
import com.avatar.hospital.chaperone.database.part.repository.PartApplyRefPartBatchRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartApplyRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartBatchRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartRepositoryService;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormFeedbackDO;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormStatus;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormFeedbackRepositoryService;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.part.PartApplyPageRequest;
import com.avatar.hospital.chaperone.request.part.PartApplyPassRequest;
import com.avatar.hospital.chaperone.request.part.PartApplyRefuseRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartApplyIdResponse;
import com.avatar.hospital.chaperone.response.part.PartApplyResponse;
import com.avatar.hospital.chaperone.service.part.PartApplyService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 16:31
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PartApplyServiceImpl implements PartApplyService {
    private final PartApplyRepositoryService partApplyRepositoryService;
    private final PartApplyRefPartBatchRepositoryService partApplyRefPartBatchRepositoryService;
    private final RepairFormFeedbackRepositoryService repairFormFeedbackRepositoryService;
    private final PartBatchRepositoryService partBatchRepositoryService;
    private final PartRepositoryService partRepositoryService;
    private final RepairFormRepositoryService repairFormRepositoryService;
    @Override
    public PageResponse<PartApplyResponse> paging(PartApplyPageRequest request) {
        Page<PartApplyDO> page = request.ofPage();

        LambdaQueryWrapper<PartApplyDO> queryWrapper = queryWrapper();
        queryWrapper.eq(!StringUtils.isEmpty(request.getRepairFromId()),PartApplyDO::getRepairFormId,request.getRepairFromId());
        queryWrapper.orderByDesc(PartApplyDO::getCreatedAt);
        page = partApplyRepositoryService.page(page,queryWrapper);
        List<PartApplyDO> records = page.getRecords();
        // 封装数据
        List<PartApplyResponse> list = new ArrayList<>();
        if (Objects.nonNull(records) && !records.isEmpty()) {
            List<Long> feedbackIdList = CollUtils.toListLongDistinct(records, PartApplyDO::getRepairFormFeedbackId);
            List<RepairFormFeedbackDO> feedbackList = repairFormFeedbackRepositoryService.findByIdList(feedbackIdList);

            List<Long> applyIdList = CollUtils.toListLongDistinct(records, PartApplyDO::getId);
            List<PartApplyRefPartBatchDO> refPartBachList = partApplyRefPartBatchRepositoryService.getByApplyIdList(applyIdList);

            List<Long> formIdList = CollUtils.toListLongDistinct(records, PartApplyDO::getRepairFormId);
            Map<Long, RepairFormDO> repairIdRef =repairFormRepositoryService.getByIdList(formIdList);

            list = PartApplyBuilder.toPartApplyResponseList(records,feedbackList,refPartBachList,repairIdRef);
        }

        return PageResponse.build(page,list);
    }

    @Override
    public PartApplyIdResponse pass(PartApplyPassRequest request) {
        PartApplyDO partApplyDO = partApplyRepositoryService.getById(request.getId());
        AssertUtils.isNotNull(partApplyDO, ErrorCode.PROJECT_PART_APPLY_NOT_EXIST);
        AssertUtils.isTrue(Objects.equals(PartApplyStatus.INIT.getCode(),partApplyDO.getStatus()), ErrorCode.PROJECT_PART_APPLY_AUTH_STATUS_ERROR);

        PartApplyRefPartBatchDO applyRefBatch = partApplyRefPartBatchRepositoryService.getByApplyId(partApplyDO.getId());
        PartBatchDO partBatchDO = partBatchRepositoryService.getById(applyRefBatch.getSparePartBatchId());

        Integer quantity = applyRefBatch.getQuantity();
        Long batchId = applyRefBatch.getSparePartBatchId();

        List<PartDO> partList = partRepositoryService.listN(batchId,quantity);
        AssertUtils.isTrue(Objects.equals(partList.size(),quantity),ErrorCode.PROJECT_PARAM_PART_NUM_LE);
        PartApplyDO updateApply = PartApplyBuilder.createPartApplyDOByPass(request);
        LocalDateTime now = LocalDateTime.now();
        List<PartDO> updatePartList = partList.stream()
                .map(part -> PartBuilder.use(part, now,request.getId(), request.getOperator()))
                .collect(Collectors.toList());
        log.info("updatePartList:{}", JSON.toJSONString(updatePartList));
        partApplyRepositoryService.pass(updateApply,partBatchDO.getId(),updatePartList);

/*        if (Objects.nonNull(partApplyDO.getRepairFormId())) {
            RepairFormDO repairForm = repairFormRepositoryService.getById(partApplyDO.getRepairFormId());
            RepairFormDO newRepairForm = new RepairFormDO();
            newRepairForm.setId(repairForm.getId());
            newRepairForm.setStatus(RepairFormStatus.NOT_FINISH.getStatus());
            newRepairForm.setUpdatedAt(LocalDateTime.now());
            repairFormRepositoryService.updateById(newRepairForm);
        }*/

        return PartApplyIdResponse.builder()
                .id(request.getId())
                .build();
    }

    @Override
    public PartApplyIdResponse refuse(PartApplyRefuseRequest request) {
        PartApplyDO partApplyDO = partApplyRepositoryService.getById(request.getId());
        AssertUtils.isNotNull(partApplyDO, ErrorCode.PROJECT_PART_APPLY_NOT_EXIST);
        AssertUtils.isTrue(Objects.equals(PartApplyStatus.INIT.getCode(),partApplyDO.getStatus()), ErrorCode.PROJECT_PART_APPLY_AUTH_STATUS_ERROR);
        RepairFormDO newRepairForm = null;
       /* if (Objects.nonNull(partApplyDO.getRepairFormId())) {
            RepairFormDO repairForm = repairFormRepositoryService.getById(partApplyDO.getRepairFormId());
            newRepairForm = new RepairFormDO();
            newRepairForm.setId(repairForm.getId());
            newRepairForm.setStatus(RepairFormStatus.NOT_FINISH.getStatus());
            newRepairForm.setUpdatedAt(LocalDateTime.now());
        }*/
        PartApplyDO updateEntity = PartApplyBuilder.createPartApplyDOByRefuse(request);
        partApplyRepositoryService.refuse(updateEntity,newRepairForm);
        return PartApplyIdResponse.builder()
                .id(request.getId())
                .build();
    }

    private LambdaQueryWrapper<PartApplyDO> queryWrapper() {
        LambdaQueryWrapper<PartApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PartApplyDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
