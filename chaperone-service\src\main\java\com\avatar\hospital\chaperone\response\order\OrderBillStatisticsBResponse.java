package com.avatar.hospital.chaperone.response.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillStatisticsBResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 应收金额 分
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long priceReceived;

    /**
     * 未收金额 分
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long priceUnReceived;

    public static final OrderBillStatisticsBResponse build(Long priceReceived, Long priceUnReceived) {
        OrderBillStatisticsBResponse obj = new OrderBillStatisticsBResponse();
        obj.setPriceReceived(priceReceived);
        obj.setPriceUnReceived(priceUnReceived);
        return obj;
    }

}
