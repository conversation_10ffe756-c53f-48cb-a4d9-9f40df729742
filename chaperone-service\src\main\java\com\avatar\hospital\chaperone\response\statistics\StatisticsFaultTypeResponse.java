package com.avatar.hospital.chaperone.response.statistics;

import com.google.common.collect.Maps;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 10:13
 **/
@Data
public class StatisticsFaultTypeResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 报修系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他,7-暖通系统，8-建筑系统，9-消防系统）
     */
    private Map<Integer, DAT> data;

    @Data
    public static class DAT implements Serializable {
        /**
         * 年度数量
         */
        private Integer yearNum;

        /**
         * 月度数量
         */
        private Integer monthNum;


        public static final Map<Integer, DAT> of(Map<Integer,Integer> yearNumMap,
                                                                                Map<Integer,Integer> monthNumMap) {
            Map<Integer, DAT> result = Maps.newHashMap();
            for (int i = 1; i <= 6; i++) {
                DAT dat = new DAT();
                dat.setYearNum(yearNumMap.getOrDefault(i,0));
                dat.setMonthNum(monthNumMap.getOrDefault(i,0));
                result.put(i,dat);
            }
            return result;
        }
    }
}
