package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import lombok.Getter;

import java.util.Objects;

/**
 * Description:
 *  订单评价来源
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderEstimateSource implements IEnumConvert<Integer> {
    NONE(-1, "未知"),

    USER(1, "用户"),
    HOSPITAL(2, "医院"),
    ;

    private final Integer status;

    private final String describe;


    OrderEstimateSource(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderEstimateSource of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderEstimateSource itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
    @Override
    public String convertDesc(Integer val) {
        OrderEstimateSource e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }
}
