package com.avatar.hospital.chaperone.utils;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.database.order.enums.*;
import com.avatar.hospital.chaperone.database.part.enums.PartStatusType;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.google.common.base.Throwables;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import org.springframework.util.CollectionUtils;

/**
 * @program: hospital-chaperone
 * @description: 导出工具类
 * @author: sp0372
 * @create: 2023-10-24 10:41
 **/
@Slf4j
public class ExportUtil {

    public final static Integer MAX_EXPORT_SIZE = 30000;


    /**
     * 导出入口
     */
    @SneakyThrows
    public final static  <T extends ExportParamDTO<R>,R> void export(HttpServletResponse response,
                                                                     T param,
                                                                     Function<T,List<R>> fun) {
        export(response,param,fun,null);
    }

    /**
     * 导出入口
     */
    @SneakyThrows
    public final static  <T extends ExportParamDTO<R>,R> void export(HttpServletResponse response,
                                                                     T param,
                                                                     Function<T,List<R>> fun,
                                                                     ExcelTypeEnum excelType) {
        try {
            boolean excelTypeSetting = Objects.nonNull(excelType);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            if (excelTypeSetting && ExcelTypeEnum.XLS.equals(excelType)) {
                response.setContentType("application/vnd.ms-excel");
            }
            response.setCharacterEncoding("utf-8");
            String name = DateUtils.dateTimeStr(DateUtils.DAY_LONG_PATTERN);;
            if (StringUtils.isNotBlank(param.getFileName())) {
                name = name + param.getFileName();
            }
            // 指定文件后缀
            String suffix = ".xlsx";
            if (excelTypeSetting) {
                suffix = excelType.getValue();
            }
            String fileName = URLEncoder.encode(name, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + suffix);
            // 这里需要设置不关闭流
            try (ExcelWriter writer = EasyExcel.write(response.getOutputStream(), param.getClazz())
                    .registerConverter(new LongStringConverter())
                    .excelType(excelTypeSetting ? excelType : ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.FALSE)
                    .build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("导出数据").build();
                Integer listSize = 0;
                do {
                    List<R> list = fun.apply(param);
                    if (CollectionUtils.isEmpty(list)) {
                        break;
                    }
                    writer.write(list, writeSheet);
                    param.pageIndexAdd1();
                    // 如果返回数据 页面大小一致则继续运行
                    listSize = list.size();
                } while (Objects.equals(listSize,param.getPageSize()));
            }
        } catch (Exception e) {
            // 重置response
            log.error("ExportUtil Exception fail {}",Throwables.getStackTraceAsString(e));
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            SingleResponse singleResponse
                    = SingleResponse.buildFailure(ErrorCode.SYSTEM_ERROR.getErrorCode(), ErrorCode.SYSTEM_ERROR.getErrorMessage());
            response.getWriter().println(JSON.toJSONString(singleResponse));
        }
    }

    // 临时转换

    /**
     * 金额 分转元
     */
    public static class Amount implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer fen = context.getValue();
            if (Objects.isNull(fen) || fen == 0) {
                return new WriteCellData<>("0");
            }
            if (fen < 0) {
                // 取正数
                fen = -fen;
                Integer yuan = fen / 100;
                Integer shi = fen % 100;
                if (shi < 10) {
                    return new WriteCellData<>("-" + yuan + ".0" + shi);
                }
                return new WriteCellData<>("-"  + yuan + "." + shi);
            }
            // 正数
            Integer yuan = fen / 100;
            Integer shi = fen % 100;
            if (shi < 10) {
                return new WriteCellData<>(yuan + ".0" + shi);
            }
            return new WriteCellData<>(yuan + "." + shi);
        }
    }

    /**
     * 时间转换
     */
    public static class Date implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer date = context.getValue();
            if (Objects.isNull(date) || date == 0) {
                return new WriteCellData<>("0");
            }
            String dateStr = date.toString();
            if (dateStr.length() == 8) {
                return new WriteCellData<>(dateStr.substring(0,4) + "-" + dateStr.substring(4,6) + "-" + dateStr.substring(6,8));
            }
            return new WriteCellData<>("0");
        }
    }

    /**
     * 金额 分转元
     */
    public static  class Version implements Converter<Long> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Long.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Long> context) {
            Long value = context.getValue();
            if (Objects.equals(OrderConst.CONSUMER_LOG_VERSION,value)) {
                return new WriteCellData<>("有效");
            }
            return new WriteCellData<>("无效");
        }
    }

    /**
     * 金额 分转元
     */
    public static  class Rate implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            if (Objects.isNull(value)) {
                return new WriteCellData<>("0%");
            }
            Integer i1 = value / 1000;
            Integer i2 = value % 1000;
            String i2Str = i2 < 10? "00" + i2 : i2 < 10? "0" + i2 : i2 + "";
            return new WriteCellData<>(i1 + "." + i2Str + "%");
        }
    }

    /**
     * 支付状态转换
     */
    public static  class PayStatus implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            OrderPayStatus payStatus = OrderPayStatus.of(value);
            String describe = Objects.isNull(payStatus) ? "未知" : payStatus.getDescribe();
            return new WriteCellData<>(describe);
        }
    }


    public static  class BillType implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            OrderBillType payStatus = OrderBillType.of(value);
            String describe = Objects.isNull(payStatus) ? "未知" : payStatus.getDescribe();
            return new WriteCellData<>(describe);
        }
    }

    public static  class BillSubType implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            OrderBillSubType payStatus = OrderBillSubType.of(value);
            String describe = Objects.isNull(payStatus) ? "未知" : payStatus.getDescribe();
            return new WriteCellData<>(describe);
        }
    }

    public static  class TradeType implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            OrderTradeType payStatus = OrderTradeType.of(value);
            String describe = Objects.isNull(payStatus) ? "未知" : payStatus.getDescribe();
            return new WriteCellData<>(describe);
        }
    }

    public static  class InvoiceNeedCertificate implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            if (Objects.isNull(value)) {
                return new WriteCellData<>("");
            }
            OrderInvoiceNeedCertificate payStatus = OrderInvoiceNeedCertificate.of(value);
            String describe = Objects.isNull(payStatus) ? "未知" : payStatus.getDescribe();
            return new WriteCellData<>(describe);
        }
    }


    public static  class InvoiceType implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            if (Objects.isNull(value)) {
                return new WriteCellData<>("");
            }
            OrderInvoiceType payStatus = OrderInvoiceType.of(value);
            String describe = Objects.isNull(payStatus) ? "未知" : payStatus.getDescribe();
            return new WriteCellData<>(describe);
        }
    }

    public static  class PayTimeStr implements Converter<String> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return String.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<String> context) {
            String str = context.getValue();
            if (Objects.isNull(str) || str.length() == 0) {
                return new WriteCellData<>("");
            }else {
                String year = str.substring(0, 4);
                String month = str.substring(4, 6);
                String day = str.substring(6, 8);
                String hour = str.substring(8, 10);
                String minute = str.substring(10, 12);
                String sec = str.substring(12, 14);
                return new WriteCellData<>(year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + sec);
            }
        }
    }

    public static  class PartStatus implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            PartStatusType payStatus = PartStatusType.of(value);
            String describe = Objects.isNull(payStatus) ? "未知" : payStatus.getDescribe();
            return new WriteCellData<>(describe);
        }
    }


    public static  class LocalDateTimeStr implements Converter<LocalDateTime> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<LocalDateTime> context) {
            LocalDateTime localDateTime = context.getValue();
            if (Objects.isNull(localDateTime)) {
                return new WriteCellData<>("");
            }
            String str = localDateTime.format(DateTimeFormatter.ofPattern(DateUtils.DATETIME_PATTERN));
            return new WriteCellData<>(str);
        }
    }

    /**
     * 出资方类型（1-物业，2-医院）
     */
    public static  class InvestorType implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            if (Objects.nonNull(value) && Objects.equals(1,value)) {
                return new WriteCellData<>("物业");
            }
            if (Objects.nonNull(value) && Objects.equals(2,value)) {
                return new WriteCellData<>("医院");
            }
            return new WriteCellData<>("未知");
        }
    }

}
