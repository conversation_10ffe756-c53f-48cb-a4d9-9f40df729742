package com.avatar.hospital.chaperone.database.repair.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 13:23
 **/
@Getter
public enum RepairFormFeedbackType implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    FINISH(1, "完成"),
    ASSIST(2, "需要协助"),
    UER_PART(3, "需要使用备件"),
    ;

    private final Integer status;

    private final String describe;


    RepairFormFeedbackType(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static RepairFormFeedbackType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (RepairFormFeedbackType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        RepairFormFeedbackType e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    public static  final Map<Integer, String> toMap() {
        RepairFormFeedbackType[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (RepairFormFeedbackType value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

}
