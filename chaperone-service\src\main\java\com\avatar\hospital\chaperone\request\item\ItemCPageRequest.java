package com.avatar.hospital.chaperone.request.item;

import com.avatar.hospital.chaperone.request.PageRequest;
import lombok.Data;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 15:45
 **/
@Data
public class ItemCPageRequest extends PageRequest {

    /**
     * 套餐类型：0 其他 1 住院陪护
     * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
     */
    private Integer serverType;
    
    /**
     * 机构ID
     */
    private Long orgId;


}
