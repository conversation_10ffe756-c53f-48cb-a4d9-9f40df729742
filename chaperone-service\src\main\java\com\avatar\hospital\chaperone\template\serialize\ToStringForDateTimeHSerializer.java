package com.avatar.hospital.chaperone.template.serialize;

import com.avatar.hospital.chaperone.utils.DateUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * 时间戳序列化 yyyyMMddHH -> 时间戳
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 16:36
 **/
public class ToStringForDateTimeHSerializer extends JsonSerializer<Integer> {
    /**
     *
     * @param dateTime
     * @param jsonGenerator
     * @param serializerProvider
     * @throws IOException
     */
    @Override
    public void serialize(Integer dateTime, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        String milli = DateUtils.toEpochMilliStr(dateTime);
        jsonGenerator.writeString(milli);
    }
}
