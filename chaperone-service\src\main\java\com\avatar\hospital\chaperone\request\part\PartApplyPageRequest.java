package com.avatar.hospital.chaperone.request.part;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:52
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PartApplyPageRequest extends PageRequest implements OperatorReq {

    /**
     * 报修单Id
     */
    private Long repairFromId;

    /**
     * 操作人
     */
    public Operator operatorUser;
}
