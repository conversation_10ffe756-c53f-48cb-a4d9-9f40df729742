<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.item.mapper.ItemMapper">

    <select id="countByC" resultType="java.lang.Long">
        SELECT count(org.id)
            FROM `t_item` item
            LEFT JOIN `t_item_org` org on org.`item_id`= item.`id`
            WHERE org.`org_id` = #{orgId}
            and item.`server_type`= #{serverType}
            and item.`status` = 1
            and item.`deleted` = 0
            and item.`display` = 1
            and org.`deleted` = 0
            ORDER BY item.id desc,
            org.`org_id` desc
    </select>

    <select id="listByC" resultType="com.avatar.hospital.chaperone.database.item.dataobject.ItemDO">
        SELECT item.*
            FROM `t_item` item
            LEFT JOIN `t_item_org` org on org.`item_id`= item.`id`
            WHERE org.`org_id`= #{orgId}
            AND item.`server_type`= #{serverType}
            and item.`status` = 1
            and item.`deleted` = 0
            and item.`display` = 1
            and org.`deleted` = 0
            ORDER BY item.id desc,
            org.`org_id` desc
            limit #{offset},#{pageSize}
    </select>
</mapper>