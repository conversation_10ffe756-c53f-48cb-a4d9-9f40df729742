package com.avatar.hospital.chaperone.request.nursing;

import com.avatar.hospital.chaperone.request.ExportPageRequest;
import com.avatar.hospital.chaperone.service.nursing.dto.NursingDayExportDTO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingDayExportPageRequest extends ExportPageRequest implements OperatorReq {

    /**
     * 陪护单号
     */
    private Long orderId;

    /**
     * 陪护单状态
     */
    private Integer status;

    /**
     * 开始时间 yyyyMMdd(包括)
     */
    private String startTime;

    /**
     * 结束时间 yyyyMMdd(包括)
     */
    private String endTime;

    /**
     * 是否是有效记录 不传(全部) ture 有效 false 无效记录
     */
    private Boolean versionFlag;

    /**
     * 护工id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long nursingId;

    /**
     * 护工名称
     */
    private String nursingName;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    @Override
    public Class getClazz() {
        return NursingDayExportDTO.class;
    }
}
