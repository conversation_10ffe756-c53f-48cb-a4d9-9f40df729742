package com.avatar.hospital.chaperone.decorator;

import com.avatar.hospital.chaperone.constant.TraceConstant;
import com.avatar.hospital.chaperone.utils.TraceUtils;
import org.springframework.core.task.TaskDecorator;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
public class TraceTaskDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        String traceId = TraceUtils.get(TraceConstant.TRACE_ID);
        return () -> {
            try {
                TraceUtils.put(TraceConstant.TRACE_ID, traceId);
                runnable.run();
            } finally {
                TraceUtils.clear();
            }
        };
    }
}
