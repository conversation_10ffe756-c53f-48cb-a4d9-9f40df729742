package com.avatar.hospital.chaperone.database.order.dataobject.model;

import com.google.common.collect.Maps;
import lombok.Data;
import org.mybatis.spring.annotation.MapperScan;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * 评价统计数据
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-20 11:51
 **/
@Data
public class StatisticsEstimateModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 一星数量
     */
    private Integer star1;
    /**
     * 二星数量
     */
    private Integer star2;
    /**
     * 三星数量
     */
    private Integer star3;
    /**
     * 四星数量
     */
    private Integer star4;
    /**
     * 五星数量
     */
    private Integer star5;

    public static StatisticsEstimateModel build(Map<Integer, Integer> starNumRef) {
        starNumRef = Objects.isNull(starNumRef) ? Maps.newHashMap() : starNumRef;
        StatisticsEstimateModel result = new StatisticsEstimateModel();
        result.setStar1(starNumRef.getOrDefault(1,0));
        result.setStar2(starNumRef.getOrDefault(2,0));
        result.setStar3(starNumRef.getOrDefault(3,0));
        result.setStar4(starNumRef.getOrDefault(4,0));
        result.setStar5(starNumRef.getOrDefault(5,0));
        return result;
    }
}
