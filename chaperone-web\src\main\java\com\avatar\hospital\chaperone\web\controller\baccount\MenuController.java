package com.avatar.hospital.chaperone.web.controller.baccount;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.MenuAccountPermissionRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuAddRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuTreeRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuAccountPermissionResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuAddResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuPagingResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuTreeResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuUpdateResponse;
import com.avatar.hospital.chaperone.service.baccount.MenuService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import com.avatar.hospital.chaperone.web.validator.baccount.MenuValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description: 菜单权限接口
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/menu")
public class MenuController {

    private final MenuService menuService;

    /**
     * 添加菜单权限
     *
     * @param request -
     * @return -
     */
    @PostMapping(value = "")
    public SingleResponse<MenuAddResponse> add(@RequestBody MenuAddRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("MenuController add request:{}", JSON.toJSONString(request));
            MenuValidator.addValidate(request);
            return menuService.add(request);
        });
    }

    /**
     * 更新菜单权限
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "")
    public SingleResponse<MenuUpdateResponse> update(@RequestBody MenuUpdateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("MenuController update request:{}", JSON.toJSONString(request));
            MenuValidator.updateValidate(request);
            return menuService.update(request);
        });
    }

    /**
     * 删除菜单权限
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "/delete")
    public SingleResponse<MenuDeleteResponse> delete(@RequestBody MenuDeleteRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("MenuController delete request:{}", JSON.toJSONString(request));
            MenuValidator.deleteValidate(request);
            return menuService.delete(request);
        });
    }

    /**
     * 分页查询菜单权限
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/paging")
    public SingleResponse<PageResponse<MenuPagingResponse>> paging(MenuPagingRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("MenuController paging request:{}", JSON.toJSONString(request));
            MenuValidator.pagingValidate(request);
            return menuService.paging(request);
        });
    }

    /**
     * 查询菜单权限详情
     *
     * @param menuId -
     * @return -
     */
    @GetMapping(value = "/{id}")
    public SingleResponse<MenuDetailResponse> detail(@PathVariable("id") Long menuId) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("MenuController detail request:{}", menuId);
            AssertUtils.isNotNull(menuId, ErrorCode.PARAMETER_ERROR);
            return menuService.detail(menuId);
        });
    }

    /**
     * 返回当前登录用户的左菜单栏
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/account-menu-tree")
    public SingleResponse<List<MenuTreeResponse>> accountMenuTree(@RequestBody(required = false) MenuTreeRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            MenuTreeRequest newRequest = request;
            if (request == null) {
                newRequest = new MenuTreeRequest();
            }
            // 设置当前登录用户
            Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
            newRequest.setAccountId(accountId);
            log.info("MenuController accountMenuTree request:{}", JSON.toJSONString(newRequest));
            MenuValidator.accountMenuTreeValidate(newRequest);
            return menuService.accountMenuTree(newRequest);
        });
    }

    /**
     * 查询菜单权限树 （返回所有的菜单权限）
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/tree")
    public SingleResponse<List<MenuTreeResponse>> tree(@RequestBody(required = false) MenuTreeRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            MenuTreeRequest newRequest = request;
            if (request == null) {
                newRequest = new MenuTreeRequest();
            }
            // 设置当前登录用户
            Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
            newRequest.setAccountId(accountId);
            log.info("MenuController tree request:{}", JSON.toJSONString(newRequest));
            MenuValidator.treeValidate(newRequest);
            return menuService.tree(newRequest);
        });
    }

    /**
     * 查询用户的权限字符串
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/account-permission")
    public SingleResponse<MenuAccountPermissionResponse> accountPermission(@RequestBody(required = false) MenuAccountPermissionRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            MenuAccountPermissionRequest newRequest = request;
            if (request == null) {
                newRequest = new MenuAccountPermissionRequest();
            }
            // 设置当前登录用户
            Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
            newRequest.setAccountId(accountId);
            log.info("MenuController accountPermission request:{}", JSON.toJSONString(newRequest));
            return menuService.accountPermission(newRequest);
        });
    }
}
