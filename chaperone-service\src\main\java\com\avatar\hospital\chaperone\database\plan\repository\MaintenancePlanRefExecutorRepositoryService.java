package com.avatar.hospital.chaperone.database.plan.repository;

import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanRefExecutorDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.repository.base.PlanRefRepositoryService;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Set;

/**
 * <p>
 * 巡检计划关联人员 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface MaintenancePlanRefExecutorRepositoryService extends IService<MaintenancePlanRefExecutorDO>, PlanRefRepositoryService {

}
