package com.avatar.hospital.chaperone.database.baccount.dataobject;

import com.avatar.hospital.chaperone.database.baccount.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_menu")
public class MenuDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 前端菜单英文名称
     */
    private String frontName;

    /**
     * 上级菜单 顶级节点默认为null
     */
    private Long parentId;

    /**
     * 组件路径
     */
    private String componentUrl;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 菜单权限字符串
     */
    private String menuKey;

    /**
     * 菜单权限类型
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.MenuType
     */
    private Integer type;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.MenuStatus
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;
}
