package com.avatar.hospital.chaperone.annotation;


import com.avatar.hospital.chaperone.utils.TraceUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Aspect
@Component
public class TraceLogAspect {

    @Pointcut("@annotation(com.avatar.hospital.chaperone.annotation.TraceLog)")
    public void log() {
    }

    /**
     * 环绕通知
     */
    @Around("log()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        String tranceId = TraceUtils.getTranceId();
        if (tranceId != null) {
            return joinPoint.proceed();
        }
        TraceUtils.setTraceId();
        Object proceed;
        try {
            proceed = joinPoint.proceed();
        } finally {
            TraceUtils.clear();
        }
        return proceed;
    }

}
