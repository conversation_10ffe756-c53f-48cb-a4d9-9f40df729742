# 医院陪护系统 Nginx 配置

# 后台管理系统
upstream chaperone-web {
    server chaperone-web:8081;
}

# 客户端系统
upstream chaperone-consumer {
    server chaperone-consumer:8080;
}

# 后台管理系统代理
server {
    listen 80;
    server_name admin.hospital-chaperone.com localhost;

    # 访问日志
    access_log /var/log/nginx/admin.access.log main;
    error_log /var/log/nginx/admin.error.log;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /api/v1/web/ {
        proxy_pass http://chaperone-web;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # 健康检查
    location /health {
        proxy_pass http://chaperone-web/actuator/health;
        access_log off;
    }

    # 默认页面
    location / {
        return 200 'Hospital Chaperone Admin System';
        add_header Content-Type text/plain;
    }
}

# 客户端系统代理
server {
    listen 80;
    server_name api.hospital-chaperone.com;

    # 访问日志
    access_log /var/log/nginx/consumer.access.log main;
    error_log /var/log/nginx/consumer.error.log;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /api/v1/consumer/ {
        proxy_pass http://chaperone-consumer;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # 健康检查
    location /health {
        proxy_pass http://chaperone-consumer/actuator/health;
        access_log off;
    }

    # 默认页面
    location / {
        return 200 'Hospital Chaperone Consumer API';
        add_header Content-Type text/plain;
    }
}
