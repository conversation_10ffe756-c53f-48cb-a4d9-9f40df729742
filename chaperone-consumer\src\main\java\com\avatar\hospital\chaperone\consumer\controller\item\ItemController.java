package com.avatar.hospital.chaperone.consumer.controller.item;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.database.item.enums.ItemServerType;
import com.avatar.hospital.chaperone.request.item.ItemCPageRequest;
import com.avatar.hospital.chaperone.request.item.ItemCreateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.item.ItemCResponse;
import com.avatar.hospital.chaperone.service.item.ItemService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;


/**
 * C端套餐
 * @program: hospital-chaperone
 * @description: 订单|C端
 * @author: sp0372
 * @create: 2023-10-13 15:04
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/item")
public class ItemController {
    private final ItemService itemService;

    /**
     * 查询列表
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<ItemCResponse>> paging(@RequestBody ItemCPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            ItemServerType serverType = ItemServerType.of(request.getServerType());
            if (Objects.nonNull(serverType.getOrgId())) {
                request.setOrgId(serverType.getOrgId());
            }
            return itemService.pagingToC(request);
        });
    }


    /**
     * 枚举-服务类型(serveType)
     */
    @PostMapping("enum/serveType")
    public SingleResponse<Map<Integer,String>> enumServeType() {
        return TemplateProcess.doProcess(log, () -> {
            return ItemServerType.toMap();
        });
    }
}
