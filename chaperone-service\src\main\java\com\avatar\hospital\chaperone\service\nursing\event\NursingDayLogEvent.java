package com.avatar.hospital.chaperone.service.nursing.event;

import com.avatar.hospital.chaperone.database.nursing.enums.NursingLogEvent;
import com.avatar.hospital.chaperone.service.nursing.dto.NursingDayLogDTO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.utils.CompareDTO;
import lombok.Data;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import javax.annotation.Nullable;

/**
 * @author:sp0420
 * @Description:
 */
public class NursingDayLogEvent extends ApplicationEvent {
    @Getter
    private Holder holder;

    public NursingDayLogEvent(Object source, Holder holder) {
        super(source);
        this.holder = holder;
    }


    @Data
    public static class Holder {
        /**
         * 操作时间
         */
        private String time;

        /**
         * 操作事件
         */
        private NursingLogEvent event;

        /**
         * 更新之前
         */
        @Nullable
        private NursingDayLogDTO.AbsCompareDTO before;

        /**
         * 更新之后
         */
        private NursingDayLogDTO.AbsCompareDTO after;

        /**
         * 比较对象类对象
         */
        private Class<CompareDTO> clazz;

        /**
         * 操作用户
         */
        private Operator operatorUser;

        public static Holder build(NursingLogEvent event, NursingDayLogDTO.Create buildByBefore, NursingDayLogDTO.Create buildByAfter, Class clazz, Operator createBy) {
            Holder obj = new Holder();
            obj.setEvent(event);
            obj.setBefore(buildByBefore);
            obj.setAfter(buildByAfter);
            obj.setClazz(clazz);
            obj.setOperatorUser(createBy);
            return obj;
        }
    }

    public static ApplicationEvent build(Object source, NursingLogEvent event, NursingDayLogDTO.Create buildByBefore, NursingDayLogDTO.Create buildByAfter, Operator createBy) {
        NursingDayLogEvent obj = new NursingDayLogEvent(source, Holder.build(event, buildByBefore, buildByAfter, event.getClazz(), createBy));
        return obj;
    }
}
