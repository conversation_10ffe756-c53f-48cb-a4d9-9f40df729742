package com.avatar.hospital.chaperone.database.repair.repository;

import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormFeedbackDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 报修单反馈 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface RepairFormFeedbackRepositoryService extends IService<RepairFormFeedbackDO> {

    void add(RepairFormDO repairFormDO,RepairFormFeedbackDO feedback, PartApplyDO partApply, PartApplyRefPartBatchDO partApplyRefPartBatchDO);

    List<RepairFormFeedbackDO> findByIdList(List<Long> feedbackIdList);


    Map<Long, RepairFormFeedbackDO> getLastRepairFormFeedback(List<Long> formIdList);

}
