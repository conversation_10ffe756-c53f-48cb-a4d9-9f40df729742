package com.avatar.hospital.chaperone.request.nursing;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingLeaveRequest implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 更新者ID
     */
    private Long updateBy;
}
