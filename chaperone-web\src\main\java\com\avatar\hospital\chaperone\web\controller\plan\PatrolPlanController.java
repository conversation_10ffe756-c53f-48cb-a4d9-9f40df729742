package com.avatar.hospital.chaperone.web.controller.plan;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.plan.PlanVO;
import com.avatar.hospital.chaperone.service.plan.PlanService;
import com.avatar.hospital.chaperone.service.plan.consts.CacheKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 巡检计划
 * @date 2023/10/27 15:46
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/web/project/plan/patrol")
public class PatrolPlanController {

    @Autowired
    private PlanService planService;


    /**
     * 创建
     *
     * @param request
     * @return
     */
    @Idempotent(value = CacheKey.PLAN_PATROL_CREATE_KEY + "+ #request.circleType + #request.orgId + #request.planType")
    @PostMapping("create")
    public SingleResponse<Long> create(@RequestBody PlanRequest request) {
        request.setPlanType(PlanType.PATROL);
        return TemplateProcess.doProcess(log, () -> {

            return planService.create(request);
        });
    }

    /**
     * 更新
     *
     * @param request
     * @return
     */
    @Idempotent(value = CacheKey.PLAN_PATROL_UPDATE_KEY + " + #request.id")
    @PostMapping("update")
    public SingleResponse<Boolean> update(@RequestBody PlanRequest request) {
        request.setPlanType(PlanType.PATROL);
        return TemplateProcess.doProcess(log, () -> {
            return planService.update(request);
        });
    }


    /**
     * 查询
     *
     * @param request
     * @return
     */
    @GetMapping("paging")
    public SingleResponse<PageResponse<PlanVO>> paging(QueryRequest request) {
        request.setPlanType(PlanType.PATROL);
        return TemplateProcess.doProcess(log, () -> {
            return planService.paging(request);
        });
    }

    /**
     * 查询-详情
     *
     * @param id
     * @return
     */
    @GetMapping("detail/{id}")
    public SingleResponse<PlanVO> getById(@PathVariable("id") Long id) {
        return TemplateProcess.doProcess(log, () -> {
            return planService.detail(PlanType.PATROL, id);
        });
    }

    /**
     * 作废
     *
     * @param request
     * @return
     */
    @DeleteMapping("abandon/{id}")
    public SingleResponse<Boolean> abandon(@PathVariable("id") Long id) {

        return TemplateProcess.doProcess(log, () -> {
            Long accountId = WebAccountUtils.getCurrentAccountId();
            PlanRequest request = new PlanRequest();
            request.setId(id);
            Operator operatorUser = new Operator();
            operatorUser.setSource(2);
            operatorUser.setId(accountId);
            request.setOperatorUser(operatorUser);
            request.setPlanType(PlanType.PATROL);
            return planService.abandon(request);
        });
    }


}
