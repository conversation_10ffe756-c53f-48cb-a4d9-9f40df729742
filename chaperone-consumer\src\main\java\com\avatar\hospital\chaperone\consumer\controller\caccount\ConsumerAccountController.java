package com.avatar.hospital.chaperone.consumer.controller.caccount;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.consumer.utils.ConsumerAccountUtils;
import com.avatar.hospital.chaperone.consumer.validator.caccount.ConsumerAccountValidator;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountBasicInfoWriteConditionRequest;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountUpdateRequest;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountBasicInfoWriteConditionResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountDetailResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountUpdateResponse;
import com.avatar.hospital.chaperone.service.caccount.ConsumerAccountService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: C端用户中心接口
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/account")
public class ConsumerAccountController {

    private final ConsumerAccountService consumerAccountService;

    /**
     * 查询用户基本信息填写情况
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/basic-info-write-condition")
    public SingleResponse<ConsumerAccountBasicInfoWriteConditionResponse> basicInfoWriteCondition(@RequestBody(required = false) ConsumerAccountBasicInfoWriteConditionRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            ConsumerAccountBasicInfoWriteConditionRequest newRequest = request;
            if (request == null) {
                newRequest = new ConsumerAccountBasicInfoWriteConditionRequest();
            }
            // 设置当前登录用户
            Long accountId = ConsumerAccountUtils.getCurrentAccountIdAndThrow();
            newRequest.setAccountId(accountId);
            log.info("ConsumerAccountController basicInfoWriteCondition request:{}", JSON.toJSONString(newRequest));
            return consumerAccountService.basicInfoWriteCondition(newRequest);
        });
    }

    /**
     * 用户详情
     *
     * @param accountId -
     * @return -
     */
    @GetMapping(value = "/{id}")
    public SingleResponse<ConsumerAccountDetailResponse> detail(@PathVariable(value = "id") Long accountId) {
        return TemplateProcess.doProcess(log, () -> {
            ConsumerAccountValidator.detailValidate(accountId);
            return consumerAccountService.detail(accountId);
        });
    }

    /**
     * 更新用户信息
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "")
    public SingleResponse<ConsumerAccountUpdateResponse> update(@RequestBody ConsumerAccountUpdateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            Long accountId = ConsumerAccountUtils.getCurrentAccountIdAndThrow();
            request.setAccountId(accountId);
            log.info("ConsumerAccountController update request:{}", JSON.toJSONString(request));
            ConsumerAccountValidator.updateValidate(request);
            return consumerAccountService.update(request);
        });
    }
}
