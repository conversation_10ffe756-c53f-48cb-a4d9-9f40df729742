package com.avatar.hospital.chaperone.service.order.impl;

import com.avatar.hospital.chaperone.builder.order.OrderBuilder;
import com.avatar.hospital.chaperone.database.order.dataobject.CashLogDO;
import com.avatar.hospital.chaperone.database.order.enums.CashSource;
import com.avatar.hospital.chaperone.database.order.enums.CashType;
import com.avatar.hospital.chaperone.database.order.repository.CashLogRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.order.CashBalanceRequest;
import com.avatar.hospital.chaperone.request.order.CashDepositRequest;
import com.avatar.hospital.chaperone.request.order.CashExtractRequest;
import com.avatar.hospital.chaperone.request.order.CashPageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.CashBalanceResponse;
import com.avatar.hospital.chaperone.response.order.CashIdResponse;
import com.avatar.hospital.chaperone.response.order.CashResponse;
import com.avatar.hospital.chaperone.service.order.CashService;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 现金操作
 * @author: sp0372
 * @create: 2023-10-19 16:59
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CashServiceImpl implements CashService {
    private final CashLogRepositoryService cashLogRepositoryService;
    @Override
    public PageResponse<CashResponse> paging(CashPageRequest request) {
        Page<CashLogDO> page = request.ofPage();
        LambdaQueryWrapper<CashLogDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(CashLogDO::getCreatedAt);
        queryWrapper.eq(Objects.nonNull(request.getType()),CashLogDO::getType,request.getType());
        queryWrapper.eq(Objects.nonNull(request.getCashSource()),CashLogDO::getCashSource,request.getCashSource());
        page = cashLogRepositoryService.page(page,queryWrapper);
        return PageResponse.build(page, OrderBuilder::toCashResponse);
    }

    @Override
    public CashIdResponse extract(CashExtractRequest request) {
        CashBalanceResponse balanceResp = balance(CashBalanceRequest.build(request.getOperatorUser()));
        Integer balance = balanceResp.getPrice();
        if (balance < request.getPrice()) {
            throw BusinessException.buildBusinessException(ErrorCode.CASH_EXTRACT_PRICE_GT_BALANCE);
        }

        long id = IdUtils.getId();
        CashLogDO cashLog = OrderBuilder.createCashLogByExtract(request);
        cashLog.setId(id);
        cashLogRepositoryService.save(cashLog);
        return CashIdResponse.build(id);
    }

    @Override
    public CashIdResponse deposit(CashDepositRequest request) {
        long id = IdUtils.getId();
        CashLogDO cashLog = OrderBuilder.createCashLogByDeposit(request);
        cashLog.setId(id);
        cashLogRepositoryService.save(cashLog);
        return CashIdResponse.build(id);
    }

    @Override
    public CashBalanceResponse balance(CashBalanceRequest request) {
        Integer addPrice = cashLogRepositoryService.sumByType(CashType.ADD.getStatus());
        Integer extractPrice = cashLogRepositoryService.sumByType(CashType.EXTRACT.getStatus());
        // 现金
        Integer cashAddPrice = cashLogRepositoryService.sumByType(CashType.ADD.getStatus(), CashSource.CASH.getStatus());
        Integer cashExtractPrice = cashLogRepositoryService.sumByType(CashType.EXTRACT.getStatus(),CashSource.CASH.getStatus());
        // 支付宝
        Integer alipayAddPrice = cashLogRepositoryService.sumByType(CashType.ADD.getStatus(), CashSource.ALIPAY.getStatus());
        Integer alipayExtractPrice = cashLogRepositoryService.sumByType(CashType.EXTRACT.getStatus(),CashSource.ALIPAY.getStatus());
        return CashBalanceResponse.build(addPrice - extractPrice,
                cashAddPrice-cashExtractPrice,
                alipayAddPrice -alipayExtractPrice);
    }

    private LambdaQueryWrapper<CashLogDO> queryWrapper() {
        LambdaQueryWrapper<CashLogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashLogDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
