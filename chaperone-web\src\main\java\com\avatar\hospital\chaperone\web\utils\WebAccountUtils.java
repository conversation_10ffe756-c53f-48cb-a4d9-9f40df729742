package com.avatar.hospital.chaperone.web.utils;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.google.common.base.Throwables;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Slf4j
public class WebAccountUtils {

    /**
     * 获取当前请求用户的accountId 没有获取到不抛出异常 返回空
     *
     * @return -
     */
    public static Long getCurrentAccountId() {
        try {
            return StpUtil.getLoginIdAsLong();
        } catch (NotLoginException notLoginException) {
            log.warn("WebAccountUtils getCurrentAccountId notLogin");
        } catch (Exception e) {
            log.error("WebAccountUtils getCurrentAccountId notLogin error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 获取当前请求用户的accountId 没有获取到抛出异常
     *
     * @return -
     */
    public static Long getCurrentAccountIdAndThrow() {
        try {
            return StpUtil.getLoginIdAsLong();
        } catch (NotLoginException notLoginException) {
            log.warn("WebAccountUtils getCurrentAccountIdAndThrow notLogin");
            throw notLoginException;
        } catch (Exception e) {
            log.error("WebAccountUtils getCurrentAccountIdAndThrow notLogin error:{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.of(ErrorCode.WEB_ACCOUNT_NOT_LOGIN_ERROR);
        }
    }
}
