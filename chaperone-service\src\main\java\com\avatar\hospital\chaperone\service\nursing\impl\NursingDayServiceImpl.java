package com.avatar.hospital.chaperone.service.nursing.impl;

import com.avatar.hospital.chaperone.builder.nursing.NursingDayBuilder;
import com.avatar.hospital.chaperone.builder.nursing.NursingLogBuilder;
import com.avatar.hospital.chaperone.builder.nursing.NursingOrderBuilder;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDayDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingOrderDO;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingLogStatus;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingDayRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingLogRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingOrderRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.nursing.NursingDayExportPageRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingDayPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingLeaveRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingSubstituteRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse;
import com.avatar.hospital.chaperone.service.nursing.NursingDayService;
import com.avatar.hospital.chaperone.service.nursing.dto.NursingDayExportDTO;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class NursingDayServiceImpl implements NursingDayService {

    private final NursingRepositoryService nursingRepositoryService;
    private final NursingDayRepositoryService nursingDayRepositoryService;
    private final NursingLogRepositoryService nursingLogRepositoryService;
    private final NursingOrderRepositoryService nursingOrderRepositoryService;

    @Override
    public PageResponse<NursingDayPagingResponse> paging(NursingDayPagingRequest request) {
        // 添加护工名称
        if (StrUtils.hasText(request.getNursingName())) {
            NursingDO nursingDO = nursingRepositoryService.findByNursingName(request.getNursingName());
            if (Objects.nonNull(nursingDO)) {
                request.setNursingId(nursingDO.getId());
            } else {
                request.setNursingId(-1L);
            }
        }
        Long total = nursingDayRepositoryService.countByB(request);
        if (total <= 0) {
            return new PageResponse<>();
        }

        List<NursingDayPagingResponse> list = nursingDayRepositoryService.listByB(request);
        if (CollectionUtils.isEmpty(list)) {
            return new PageResponse<>();
        }
        return PageResponse.build(total, request.getPageIndexLong(), request.getPageSizeLong(), list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean leave(NursingLeaveRequest request) {
        //判断当前用户班次状态
        NursingDayDO nursingDayDO = nursingDayRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(nursingDayDO, ErrorCode.NURSING_DAY_NOT_EXIST);
//        AssertUtils.isTrue(NursingDayStatus.FREE.getStatus().equals(nursingDayDO.getStatus()), ErrorCode.NURSING_STATUS_NOT_FREE);

        //查询订单关系
        List<NursingOrderDO> nursingOrderDOS = nursingOrderRepositoryService.findByNursingIdAndDate(nursingDayDO.getNursingId(), nursingDayDO.getDate());
        if (!CollectionUtils.isEmpty(nursingOrderDOS)) {
            throw BusinessException.of(ErrorCode.NURSING_ORDER_IN_PROGRESS);
        }

        //请假申请
        Boolean result = nursingDayRepositoryService.incrementUpdate(NursingDayBuilder.buildLeaveNursingDayDO(nursingDayDO));
        AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);

        //新增日志
        addNursingLog(nursingDayDO.getNursingId(), null, NursingLogStatus.LEAVE.getStatus(), request.getUpdateBy());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelLeave(NursingLeaveRequest request) {
        //判断当前用户班次状态
        NursingDayDO nursingDayDO = nursingDayRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(nursingDayDO, ErrorCode.NURSING_DAY_NOT_EXIST);
        AssertUtils.isTrue(Objects.equals(nursingDayDO.getStatus(), NursingDayStatus.LEAVE.getStatus()), ErrorCode.NURSING_STATUS_NOT_LEAVE);

        //请假申请
        Boolean result = nursingDayRepositoryService.incrementUpdate(NursingDayBuilder.buildCancelLeaveNursingDayDO(nursingDayDO));
        AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);

        //新增日志
        addNursingLog(nursingDayDO.getNursingId(), null, NursingLogStatus.CANCEL_LEAVE.getStatus(), request.getUpdateBy());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean substitute(NursingSubstituteRequest request) {
        //判断当前用户班次状态
        NursingDayDO nursingDayDO = nursingDayRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(nursingDayDO, ErrorCode.NURSING_DAY_NOT_EXIST);
//        AssertUtils.isTrue(NursingDayStatus.FREE.getStatus().equals(nursingDayDO.getStatus()), ErrorCode.NURSING_STATUS_NOT_FREE);

        //查询订单关系
        List<NursingOrderDO> nursingOrderDOS = nursingOrderRepositoryService.findByNursingIdAndDate(nursingDayDO.getNursingId(), nursingDayDO.getDate());
        if (CollectionUtils.isEmpty(nursingOrderDOS)) {
            throw BusinessException.of(ErrorCode.NURSING_REPLACE_NOT_EXIST);
        }
        //判断替换医护是否存在
        NursingDO nursingDO = nursingRepositoryService.findById(request.getNursingId());
        AssertUtils.isNotNull(nursingDO, ErrorCode.NURSING_REPLACE_NOT_EXIST);
        request.setNursingName(nursingDO.getName());
        //查询替换医护当前状态，如果空闲-忙碌
        NursingDayDO nursingReplaceDayDO = nursingDayRepositoryService.findByNursingIdAndDate(request.getNursingId(), nursingDayDO.getDate());
        if (Objects.isNull(nursingReplaceDayDO)) {
            //新增
            nursingDayRepositoryService.add(NursingDayBuilder.buildNursingDayDO(request, nursingDayDO.getDate()));
        } else {
            if (NursingDayStatus.LEAVE.getStatus().equals(nursingReplaceDayDO.getStatus())) {
                //请假的不能替换
                throw BusinessException.of(ErrorCode.NURSING_REPLACE_ON_LEAVE);
            }
            if (NursingDayStatus.FREE.getStatus().equals(nursingReplaceDayDO.getStatus())) {
                //空闲改为忙碌
                Boolean result = nursingDayRepositoryService.incrementUpdate(NursingDayBuilder.buildSubstituteNursingDayDO(nursingReplaceDayDO, NursingDayStatus.BUSY.getStatus()));
                AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);
            }
        }

        //修改当前医护状态：忙碌-空闲
        Boolean result = nursingDayRepositoryService.incrementUpdate(NursingDayBuilder.buildSubstituteNursingDayDO(nursingDayDO, NursingDayStatus.FREE.getStatus()));
        AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);

        //更新t_nursing_order
        Boolean updateBatchById = nursingOrderRepositoryService.updateBatchById(NursingOrderBuilder.buildNursingOrderDO(nursingOrderDOS, request));
        AssertUtils.isTrue(updateBatchById, ErrorCode.UPDATE_ERROR);

        //新增日志
        addNursingLog(nursingDayDO.getNursingId(), request.getNursingId(), NursingLogStatus.REPLACE.getStatus(), request.getUpdateBy());
        return Boolean.TRUE;
    }

    @Override
    public List<NursingDayExportDTO> export(NursingDayExportPageRequest request) {
        NursingDayPagingRequest pagingRequest = new NursingDayPagingRequest();
        pagingRequest.setOrderId(request.getOrderId());
        pagingRequest.setStatus(request.getStatus());
        if (StrUtils.hasText(request.getStartTime())) {
            pagingRequest.setStartTime(Integer.valueOf(request.getStartTime()));
        }
        if (StrUtils.hasText(request.getEndTime())) {
            pagingRequest.setEndTime(Integer.valueOf(request.getEndTime()));
        }
        pagingRequest.setSortType(2);
        pagingRequest.setNursingId(request.getNursingId());
        pagingRequest.setNursingName(request.getNursingName());
        pagingRequest.setPageIndex(request.getPageIndex());
        pagingRequest.setPageSize(request.getPageSize());
        PageResponse<NursingDayPagingResponse> pagingResponsePageResponse = paging(pagingRequest);
        if (Objects.isNull(pagingResponsePageResponse) || CollectionUtils.isEmpty(pagingResponsePageResponse.getRecords())) {
            return new ArrayList<>();
        }
        return pagingResponsePageResponse.getRecords()
                .stream().map(NursingDayBuilder::buildNursingDayExportDTO).collect(Collectors.toList());

    }

    private Boolean addNursingLog(Long nursingId, Long replaceNursingId, Integer logStatus, Long updateBy) {
        return nursingLogRepositoryService.saveBatch(NursingLogBuilder.buildNursingLogDO(nursingId, replaceNursingId, logStatus, updateBy));
    }
}
