package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.database.order.enums.OrderEstimateSource;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:05
 **/
@Data
public class OrderEstimateSaveRequest implements OperatorReq,Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @NotNull
    private Long orderId;

    /**
     * 星级
     */
    private Integer level;

    /**
     * 描述
     */
    private String remark;

    /**
     * 来源,1 用户 2 院发
     * @see OrderEstimateSource
     */
    private Integer source;

    /**
     * 操作用户
     */
    private Operator operatorUser;


}
