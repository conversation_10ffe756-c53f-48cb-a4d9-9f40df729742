package com.avatar.hospital.chaperone.database.nursing.repository;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingOrderDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 护工-日期订单关联表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface NursingOrderRepositoryService extends IService<NursingOrderDO> {

    /**
     * 根据护工和日期查询订单
     * @param nursingId
     * @param date
     * @return
     */
    List<NursingOrderDO> findByNursingIdAndDate(Long nursingId, Integer date);

    /**
     * 根据护工查询订单
     * @param nursingId
     * @return
     */
    List<NursingOrderDO> findByNursingId(Long nursingId);

    /**
     * 返回  <date,<orderId,nursing>>
     * @param orderIdList
     * @param startDate
     * @param endDate
     * @return
     */
    Map<Integer, Map<Long,NursingOrderDO>> findAllByOrderId(List<Long> orderIdList, Integer startDate, Integer endDate);

    /**
     * 返回  <date,<nursing>>
     * @param orderId
     * @param startDate
     * @param endDate
     * @return
     */
    Map<Integer, Map<Long,NursingOrderDO>> findAllByOrderId(Long orderId, Integer startDate, Integer endDate);

    /**
     * 清理
     *
     * @param orderId
     * @param date
     */
    void clean(Long orderId, Integer date);

    /**
     * 统计
     * @param localDateTime
     * @param localDateTime1
     * @return
     */
    Integer countNum(LocalDateTime localDateTime, LocalDateTime localDateTime1);

    /**
     *
     * @param orderId
     * @param startDate
     */
    void delNursingOrder(Long orderId, Integer startDate);
}
