package com.avatar.hospital.chaperone.properties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/17
 */
@Data
@Slf4j
@ConfigurationProperties(prefix = OssProperties.PREFIX)
@Component
public class OssProperties {

    public static final String PREFIX = "oss";

    private String endpoint;

    private String bucketName;

    private String accessKeyId;

    private String secretAccessKey;

    private String customDomain;

}
