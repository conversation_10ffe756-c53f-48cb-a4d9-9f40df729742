package com.avatar.hospital.chaperone.request.repair;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:49
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormAssignExecutorRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 报修单ID
     */
    @NotNull
    private Long id;

    /**
     * 执行人员accountId列表
     */
    private List<Long> executorAccountIdList;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    /**
     * 紧急程度
     *
     * @see com.avatar.hospital.chaperone.database.repair.enums.RepairFormUrgencyDegreeType
     */
    private Integer urgencyDegreeType;
}
