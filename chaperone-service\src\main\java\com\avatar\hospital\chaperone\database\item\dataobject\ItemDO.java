package com.avatar.hospital.chaperone.database.item.dataobject;

import com.avatar.hospital.chaperone.database.item.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.item.enums.ItemDisplay;
import com.avatar.hospital.chaperone.database.item.enums.ItemStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 套餐表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_item")
public class ItemDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 套餐类型：参考，server_type
       * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
     */
      private Integer serverType;

      /**
     * 套餐名称
     */
      private String name;

      /**
     * 服务价格(单位：分/天)
     */
      private Integer price;

      /**
     * 状态(影响C端展示)：1 上架，0 下架
       * @see ItemStatus
     */
      private Integer status;

      /**
     * 护工星级 1 ~ 5 星
     */
      private Integer nursingStar;

      /**
     * 详细描述
     */
      private String remark;

      /**
     * 封面图
     */
      private String coverPicture;

      /**
     * 物业管理费比例
     */
      private Integer rateCertifiedProperty;

      /**
     * 院方管理费比例
     */
      private Integer rateHospital;

      /**
     * 护工费比例
     */
      private Integer rateNursing;


  /**
   * 时间比例 0 ~ 23
   */
  private Integer chargingTime;

  /**
   * 是否外显示 1 是 0 不是
   * @see ItemDisplay
   */
  private Integer display;
}
