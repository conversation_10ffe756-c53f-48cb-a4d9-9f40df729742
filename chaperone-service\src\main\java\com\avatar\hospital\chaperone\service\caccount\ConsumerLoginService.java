package com.avatar.hospital.chaperone.service.caccount;

import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountLoginRequest;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountLoginResponse;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
public interface ConsumerLoginService {

    /**
     * C端用户微信小程序登录
     *
     * @param request -
     * @return -
     */
    ConsumerAccountLoginResponse login(ConsumerAccountLoginRequest request);

}
