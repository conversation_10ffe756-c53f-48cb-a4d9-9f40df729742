package com.avatar.hospital.chaperone.response.statistics;

import com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsEstimateModel;
import com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsNursingModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 10:14
 **/
@Data
public class StatisticsEstimateResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 年度-统计数据
     */
    private DATA year;

    /**
     * 月度-统计数据
     */
    private DATA month;

    /**
     * 总数据
     */
    @Data
    public static class DATA {
        /**
         * 星级统计
         */
        private StatisticsEstimateModel start;
        /**
         * 护工 排行前10
         */
        private List<StatisticsNursingModel> nursingList;

        public static final DATA build(StatisticsEstimateModel start,
                                       List<StatisticsNursingModel> nursingList) {
            DATA obj = new DATA();
            obj.setStart(start);
            obj.setNursingList(nursingList);
            return obj;
        }
    }

    public static final StatisticsEstimateResponse build(StatisticsEstimateModel startYear,
                                                       List<StatisticsNursingModel> nursingListYear,
                                                         StatisticsEstimateModel startMonth,
                                                         List<StatisticsNursingModel> nursingListMonth) {
        StatisticsEstimateResponse obj = new StatisticsEstimateResponse();
        obj.setYear(DATA.build(startYear, nursingListYear));
        obj.setMonth(DATA.build(startMonth, nursingListMonth));
        return obj;
    }
}
