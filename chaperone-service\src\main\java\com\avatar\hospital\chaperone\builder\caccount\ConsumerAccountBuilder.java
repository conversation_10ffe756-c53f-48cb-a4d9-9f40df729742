package com.avatar.hospital.chaperone.builder.caccount;

import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountDetailResponse;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountUpdateRequest;
import com.avatar.hospital.chaperone.utils.DateUtils;
import org.apache.commons.lang.StringUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/16
 */
public class ConsumerAccountBuilder {

    public static AccountDO buildAccountDO(ConsumerAccountUpdateRequest request) {
        if (request == null) {
            return null;
        }
        AccountDO accountDO = new AccountDO();
        accountDO.setId(request.getAccountId());
        accountDO.setNickName(request.getNickName());
        accountDO.setName(request.getName());
        accountDO.setPhoneNumber(request.getPhoneNumber());

        accountDO.setSex(request.getSex());
        if(StringUtils.isNotBlank(request.getBirthday())){
            accountDO.setBirthday(DateUtils.dataStrToLocalDate(request.getBirthday(), DateUtils.DAY_PATTERN));
        }
        if(StringUtils.isNotBlank(request.getAdmissionTime())){
            accountDO.setAdmissionTime(DateUtils.dataStrToLocalDate(request.getAdmissionTime(), DateUtils.DAY_PATTERN));
        }
        return accountDO;
    }

    public static ConsumerAccountDetailResponse buildConsumerAccountDetailResponse(AccountDO accountDO) {
        if (accountDO == null) {
            return null;
        }
        ConsumerAccountDetailResponse consumerAccountDetailResponse = new ConsumerAccountDetailResponse();
        consumerAccountDetailResponse.setId(accountDO.getId());
        consumerAccountDetailResponse.setNickName(accountDO.getNickName());
        consumerAccountDetailResponse.setPhoneNumber(accountDO.getPhoneNumber());
        consumerAccountDetailResponse.setSex(accountDO.getSex());
        consumerAccountDetailResponse.setBirthday(accountDO.getBirthday());
        consumerAccountDetailResponse.setAvatarUrl(accountDO.getAvatarUrl());
        consumerAccountDetailResponse.setStatus(accountDO.getStatus());
        consumerAccountDetailResponse.setCreatedAt(accountDO.getCreatedAt());
        consumerAccountDetailResponse.setUpdatedAt(accountDO.getUpdatedAt());
        return consumerAccountDetailResponse;
    }
}
