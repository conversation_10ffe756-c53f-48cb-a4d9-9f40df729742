package com.avatar.hospital.chaperone.database.item.enums;

import lombok.Getter;

/**
 * Description:
 *  商品上下架状态
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum ItemStatus {

    NONE(-1, "未知"),
    OFF(0, "下架"),

    NO(1, "上架"),


    ;

    private final Integer status;

    private final String describe;


    ItemStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static ItemStatus of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (ItemStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
