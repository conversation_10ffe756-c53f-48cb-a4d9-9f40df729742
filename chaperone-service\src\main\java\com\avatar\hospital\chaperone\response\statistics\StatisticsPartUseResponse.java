package com.avatar.hospital.chaperone.response.statistics;

import com.avatar.hospital.chaperone.database.part.dataobject.model.PartStatisticsModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-13 10:14
 **/
@Data
public class StatisticsPartUseResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 年度统计数据
     */
    private List<PartStatisticsModel> yearList;

    /**
     * 月度统计数据
     */
    private List<PartStatisticsModel> monthList;
}
