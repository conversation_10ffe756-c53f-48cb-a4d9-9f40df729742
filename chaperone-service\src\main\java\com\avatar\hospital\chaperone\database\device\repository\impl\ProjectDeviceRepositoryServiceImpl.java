package com.avatar.hospital.chaperone.database.device.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO;
import com.avatar.hospital.chaperone.database.device.mapper.ProjectDeviceMapper;
import com.avatar.hospital.chaperone.database.device.repository.ProjectDeviceRepositoryService;
import com.avatar.hospital.chaperone.database.emergencylog.dataobject.EmergencyHandlingLogDO;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.device.DevicePagingRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.DefaultUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class ProjectDeviceRepositoryServiceImpl extends ServiceImpl<ProjectDeviceMapper, ProjectDeviceDO> implements ProjectDeviceRepositoryService {

    @Override
    public Map<Long, String> findMapByIdList(List<Long> linkDeviceId) {
        if (CollUtils.isEmpty(linkDeviceId)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<ProjectDeviceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ProjectDeviceDO::getId, ProjectDeviceDO::getName);
        queryWrapper.in(ProjectDeviceDO::getId, linkDeviceId);
        List<ProjectDeviceDO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream()
                .collect(Collectors.toMap(ProjectDeviceDO::getId, ProjectDeviceDO::getName));
    }

    @Override
    public String findNameById(Long deviceId) {
        LambdaQueryWrapper<ProjectDeviceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ProjectDeviceDO::getId, ProjectDeviceDO::getName);
        queryWrapper.eq(ProjectDeviceDO::getId, deviceId);
        ProjectDeviceDO projectDeviceDO = getOne(queryWrapper);
        if (Objects.isNull(projectDeviceDO)) {
            return null;
        }
        return projectDeviceDO.getName();
    }

    @Override
    public Long add(ProjectDeviceDO projectDeviceDO) {
        if (projectDeviceDO == null) {
            return null;
        }

        projectDeviceDO.setId(IdUtils.getId());
        projectDeviceDO.setCode(projectDeviceDO.getId().toString());
        if (!save(projectDeviceDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        return projectDeviceDO.getId();
    }

    @Override
    public ProjectDeviceDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<ProjectDeviceDO> queryWrapper = queryWrapper();
        queryWrapper.eq(ProjectDeviceDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public Boolean incrementUpdate(ProjectDeviceDO projectDeviceDO) {
        if (projectDeviceDO == null) {
            return null;
        }
        LambdaUpdateWrapper<ProjectDeviceDO> updateWrapper = updateWrapper();
        updateWrapper.set(projectDeviceDO.getCode() != null, ProjectDeviceDO::getCode, projectDeviceDO.getCode());
        updateWrapper.set(projectDeviceDO.getName() != null, ProjectDeviceDO::getName, projectDeviceDO.getName());
        updateWrapper.set(projectDeviceDO.getOrgDepartmentId() != null, ProjectDeviceDO::getOrgDepartmentId, projectDeviceDO.getOrgDepartmentId());
        updateWrapper.set(projectDeviceDO.getOrgId() != null, ProjectDeviceDO::getOrgId, projectDeviceDO.getOrgId());
        updateWrapper.set(projectDeviceDO.getStatus() != null, ProjectDeviceDO::getStatus, projectDeviceDO.getStatus());
        updateWrapper.set(projectDeviceDO.getAddress() != null, ProjectDeviceDO::getAddress, projectDeviceDO.getAddress());
        updateWrapper.set(projectDeviceDO.getRemark() != null, ProjectDeviceDO::getRemark, projectDeviceDO.getRemark());
        updateWrapper.set(projectDeviceDO.getSystemType() != null, ProjectDeviceDO::getSystemType, projectDeviceDO.getSystemType());
        updateWrapper.set(projectDeviceDO.getUpdateBy() != null, ProjectDeviceDO::getUpdateBy, projectDeviceDO.getUpdateBy());
        updateWrapper.set(projectDeviceDO.getUpdatedAt() != null, ProjectDeviceDO::getUpdatedAt, projectDeviceDO.getUpdatedAt());
        updateWrapper.eq(ProjectDeviceDO::getId, projectDeviceDO.getId());

        return update(updateWrapper);
    }

    @Override
    public PageResponse<ProjectDeviceDO> paging(DevicePagingRequest request) {
        int index = DefaultUtils.ifNullDefault(request.getPageIndex(), 1);
        int size = DefaultUtils.ifNullDefault(request.getPageSize(), 10);
        LambdaQueryWrapper<ProjectDeviceDO> queryWrapper = queryWrapper();
        queryWrapper.eq(request.getCode() != null, ProjectDeviceDO::getCode, request.getCode());
        queryWrapper.eq(request.getOrgId() != null, ProjectDeviceDO::getOrgId, request.getOrgId());
        queryWrapper.eq(request.getStatus() != null, ProjectDeviceDO::getStatus, request.getStatus());
        queryWrapper.eq(request.getName() != null, ProjectDeviceDO::getName, request.getName());
        queryWrapper.eq(request.getSystemType() != null, ProjectDeviceDO::getSystemType, request.getSystemType());
        Page<ProjectDeviceDO> page = baseMapper.selectPage(new Page<>(index, size), queryWrapper);
        return PageResponse.build(page.getTotal(), page.getCurrent(), page.getSize(), page.getRecords());
    }

    @Override
    public List<ProjectDeviceDO> fault10(LocalDateTime start, LocalDateTime end, Integer model) {
        List<ProjectDeviceDO> list = baseMapper.fault10(start, end, model);
        return CollUtils.isEmpty(list) ? Collections.emptyList() : list;
    }

    private LambdaQueryWrapper<ProjectDeviceDO> queryWrapper() {
        LambdaQueryWrapper<ProjectDeviceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectDeviceDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<ProjectDeviceDO> updateWrapper() {
        LambdaUpdateWrapper<ProjectDeviceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjectDeviceDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
