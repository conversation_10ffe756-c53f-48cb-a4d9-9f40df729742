package com.avatar.hospital.chaperone.web.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.database.order.enums.*;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.*;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.utils.ExportUtil;
import com.avatar.hospital.chaperone.web.validator.order.OrderValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * B端陪护单-账单
 * @program: hospital-chaperone
 * @description: 套餐
 * @author: sp0372
 * @create: 2023-10-13 10:07
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/order/bill")
public class OrderBillController {
    private final OrderBillService orderBillService;

    /**
     * 查询
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<OrderBillBResponse>> paging(@RequestBody OrderBillPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderBillService.paging(request);
        });
    }

    /**
     * 查询导出
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.BILL_EXPORT_PREFIX)
    @PostMapping("export")
    public void export(@RequestBody OrderBillExportPageRequest request,
                                                                   HttpServletResponse response) {
        ExportUtil.export(response,request,orderBillService::exportPaging, ExcelTypeEnum.XLS);
    }

    /**
     * 统计
     * @param request
     * @return
     */
    @PostMapping("statistics")
    public SingleResponse<OrderBillStatisticsBResponse> statistics(@RequestBody OrderBillPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderBillService.statisticsforB(request);
        });
    }

    /**
     * 修改-账单
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.ORDER_MODIFY_PREFIX + " + #request.orderId")
    @PostMapping("modify")
    public SingleResponse<OrderBillIdResponse> modifyBillType(@Validated @RequestBody OrderBillModifyTradeRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.modifyBillValidate(request);
            return orderBillService.modifyBillType(request);
        });
    }

    /**
     * 修改-现金支付
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.BILL_MODIFY_PREFIX + " + #request.billId")
    @PostMapping("cashPay")
    public SingleResponse<OrderBillIdResponse> cashPay(@Validated @RequestBody OrderBillCashPayRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.cashPayValidate(request);
            return orderBillService.cashPay(request);
        });
    }


    /**
     * 枚举-账单类型(billType)
     * @return
     */
    @PostMapping("enum/billType")
    public SingleResponse<Map<Integer,String>> enumBillType() {
        return TemplateProcess.doProcess(log, () -> {
            return OrderBillType.toMap();
        });
    }

    /**
     * 枚举-账单子类型(billSubType)
     * @return
     */
    @PostMapping("enum/billSubType")
    public SingleResponse<Map<Integer,String>> enumBillSubType() {
        return TemplateProcess.doProcess(log, () -> {
            return OrderBillSubType.toMap();
        });
    }


    /**
     * 枚举-交易类型(tradeType)
     * @return
     */
    @PostMapping("enum/tradeType")
    public SingleResponse<Map<Integer,String>> enumTradeType() {
        return TemplateProcess.doProcess(log, () -> {
            return OrderTradeType.toMap();
        });
    }

    /**
     * 枚举-支付状态类型(payStatus)
     * @return
     */
    @PostMapping("enum/payStatus")
    public SingleResponse<Map<Integer,String>> enumPayStatus() {
        return TemplateProcess.doProcess(log, () -> {
            return OrderPayStatus.toMap();
        });
    }

    /**
     * 修改-微信退款内测
     * @param request
     * @return
     */
    @Idempotent(value = OrderKey.BILL_MODIFY_PREFIX + " + #request.billId")
    @PostMapping("wxRefund")
    public SingleResponse<OrderBillIdResponse> wxRefund(@Validated @RequestBody OrderBillCashPayRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderBillService.wxRefund(request);
        });
    }

}
