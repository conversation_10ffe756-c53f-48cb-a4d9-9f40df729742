package com.avatar.hospital.chaperone.template.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 16:36
 **/
public class ToIntegerNegativeSerializer extends JsonSerializer<Integer> {
    @Override
    public void serialize(Integer val, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (Objects.nonNull(val) && val > 0) {
            val = val * -1;
            jsonGenerator.writeNumber(val);
        }else {
            jsonGenerator.writeNumber(val);
        }
    }
}
