package com.avatar.hospital.chaperone.database.nursing.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDayDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingOrderDO;
import com.avatar.hospital.chaperone.database.nursing.mapper.NursingOrderMapper;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingOrderRepositoryService;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 护工-日期订单关联表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class NursingOrderRepositoryServiceImpl extends ServiceImpl<NursingOrderMapper, NursingOrderDO> implements NursingOrderRepositoryService {

    @Override
    public Map<Integer, Map<Long,NursingOrderDO>> findAllByOrderId(List<Long> orderIdList, Integer startDate, Integer endDate) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<NursingOrderDO> queryWrapper = queryWrapper();
        queryWrapper.in(NursingOrderDO::getOrderId,orderIdList);
        queryWrapper.ge(NursingOrderDO::getDate,startDate);
        queryWrapper.le(NursingOrderDO::getDate,endDate);
        List<NursingOrderDO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream()
                .collect(Collectors.groupingBy(NursingOrderDO::getDate,Collectors.toMap(NursingOrderDO::getOrderId, Function.identity())));
    }

    @Override
    public Map<Integer, Map<Long,NursingOrderDO>> findAllByOrderId(Long orderId, Integer startDate, Integer endDate) {
        LambdaQueryWrapper<NursingOrderDO> queryWrapper = queryWrapper();
        queryWrapper.eq(NursingOrderDO::getOrderId,orderId);
        queryWrapper.ge(NursingOrderDO::getDate,startDate);
        queryWrapper.le(NursingOrderDO::getDate,endDate);
        List<NursingOrderDO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<Integer, Map<Long,NursingOrderDO>> result = Maps.newHashMap();
        for (NursingOrderDO nursingOrder : list) {
            Map<Long, NursingOrderDO> nursingIdRefNursingOrderMap = result.getOrDefault(nursingOrder.getDate(), Maps.newHashMap());
            nursingIdRefNursingOrderMap.put(nursingOrder.getNursingId(), nursingOrder);
            result.put(nursingOrder.getDate(), nursingIdRefNursingOrderMap);
        }
        return result;
    }

    @Override
    public void clean(Long orderId, Integer date) {
        if (Objects.isNull(orderId) || Objects.isNull(date)) {
            return;
        }
        LambdaUpdateWrapper<NursingOrderDO> updateWrapper = updateWrapper();
        updateWrapper.eq(NursingOrderDO::getOrderId,orderId);
        updateWrapper.gt(NursingOrderDO::getDate,date);
        remove(updateWrapper);
    }

    @Override
    public Integer countNum(LocalDateTime start, LocalDateTime end) {
        Integer startInt = DateUtils.dateInt(start);
        Integer endInt = DateUtils.dateInt(end);
        LambdaQueryWrapper<NursingOrderDO> queryWrapper = queryWrapper();
        queryWrapper.ge(NursingOrderDO::getDate,startInt);
        queryWrapper.le(NursingOrderDO::getDate,endInt);
        Long count = count(queryWrapper);
        return Objects.isNull(count) ? 0 : count.intValue();
    }

    @Override
    public void delNursingOrder(Long orderId, Integer startDate) {
        LambdaUpdateWrapper<NursingOrderDO> objectLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        objectLambdaUpdateWrapper.eq(NursingOrderDO::getOrderId,orderId);
        objectLambdaUpdateWrapper.ge(NursingOrderDO::getDate,startDate);
        objectLambdaUpdateWrapper.eq(NursingOrderDO::getWay,0);
        remove(objectLambdaUpdateWrapper);
    }

    @Override
    public List<NursingOrderDO> findByNursingIdAndDate(Long nursingId, Integer date) {
        if (nursingId == null || nursingId < 0 || date == null || date < 0) {
            return null;
        }
        LambdaQueryWrapper<NursingOrderDO> queryWrapper = queryWrapper();
        queryWrapper.eq(NursingOrderDO::getNursingId, nursingId)
                .eq(NursingOrderDO::getDate, date);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<NursingOrderDO> findByNursingId(Long nursingId) {
        if (nursingId == null || nursingId < 0 ) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<NursingOrderDO> queryWrapper = queryWrapper();
        queryWrapper.eq(NursingOrderDO::getNursingId, nursingId);
        return baseMapper.selectList(queryWrapper);
    }

    private LambdaQueryWrapper<NursingOrderDO> queryWrapper() {
        LambdaQueryWrapper<NursingOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NursingOrderDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<NursingOrderDO> updateWrapper() {
        LambdaUpdateWrapper<NursingOrderDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(NursingOrderDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}
