package com.avatar.hospital.chaperone.database.part.dataobject;

import com.avatar.hospital.chaperone.database.part.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 备品备件批次
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_spare_part_batch")
public class PartBatchDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 批次编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 可用数量
     */
    private Integer balance;

    /**
     * 单价
     */
    private Integer price;

    /**
     * 数量单位
     */
    private String unit;

    /**
     * 状态（0-待入库，1-已入库）
     */
    private Integer status;

    /**
     * 入库时间
     */
    private LocalDateTime entryTime;

    /**
     * 版本号
     */
    private Long version;


}
