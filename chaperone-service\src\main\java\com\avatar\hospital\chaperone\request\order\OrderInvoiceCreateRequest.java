package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.database.order.enums.OrderInvoiceNeedCertificate;
import com.avatar.hospital.chaperone.database.order.enums.OrderInvoiceType;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderInvoiceCreateRequest implements OperatorReq,Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账单ID
     */
    @NotNull
    private Long billId;

    /**
     * 是否需要付费凭证,0 不需要，1 需要
     * @see OrderInvoiceNeedCertificate
     */
    private Integer needCertificate;

    /**
     * 类型: 1 电子收据 2 电子发票 3 实体收据
     * @see OrderInvoiceType
     */
    private Integer invoiceType;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 纳税人名称
     */
    private String taxpayerName;

    /**
     * 纳税人识别号
     */
    private String taxpayerNo;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
