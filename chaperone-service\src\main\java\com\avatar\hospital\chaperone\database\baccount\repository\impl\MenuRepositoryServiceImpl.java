package com.avatar.hospital.chaperone.database.baccount.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.dataobject.MenuDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleMenuDO;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.baccount.mapper.MenuMapper;
import com.avatar.hospital.chaperone.database.baccount.repository.MenuRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleMenuRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.DefaultUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MenuRepositoryServiceImpl extends ServiceImpl<MenuMapper, MenuDO> implements MenuRepositoryService {

    private final RoleMenuRepositoryService roleMenuRepositoryService;

    @Override
    public Long add(MenuDO menuDO) {
        if (menuDO == null) {
            return null;
        }
        menuDO.setId(IdUtils.getId());
        if (!save(menuDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        return menuDO.getId();
    }

    @Override
    public Boolean incrementUpdate(MenuDO menuDO) {
        if (menuDO == null) {
            return false;
        }
        LambdaUpdateWrapper<MenuDO> updateWrapper = updateWrapper();
        updateWrapper.set(menuDO.getName() != null, MenuDO::getName, menuDO.getName());
        updateWrapper.set(menuDO.getFrontName() != null, MenuDO::getFrontName, menuDO.getFrontName());
        updateWrapper.set(menuDO.getParentId() != null, MenuDO::getParentId, menuDO.getParentId());
        updateWrapper.set(menuDO.getComponentUrl() != null, MenuDO::getComponentUrl, menuDO.getComponentUrl());
        updateWrapper.set(menuDO.getIcon() != null, MenuDO::getIcon, menuDO.getIcon());
        updateWrapper.set(menuDO.getMenuKey() != null, MenuDO::getMenuKey, menuDO.getMenuKey());
        updateWrapper.set(menuDO.getStatus() != null, MenuDO::getStatus, menuDO.getStatus());
        updateWrapper.set(menuDO.getSort() != null, MenuDO::getSort, menuDO.getSort());
        updateWrapper.set(menuDO.getRemark() != null, MenuDO::getRemark, menuDO.getRemark());
        updateWrapper.set(menuDO.getUpdateBy() != null, MenuDO::getUpdateBy, menuDO.getUpdateBy());

        updateWrapper.eq(MenuDO::getId, menuDO.getId());

        return update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByIds(Set<Long> ids, Long updateBy) {
        if (CollectionUtils.isEmpty(ids) || updateBy == null) {
            return true;
        }
        LambdaUpdateWrapper<MenuDO> updateWrapper = updateWrapper();
        updateWrapper.set(MenuDO::getDeleted, System.currentTimeMillis());
        updateWrapper.set(MenuDO::getUpdateBy, updateBy);
        if (ids.size() == 1) {
            updateWrapper.eq(MenuDO::getId, ids.toArray()[0]);
        }
        else {
            updateWrapper.in(MenuDO::getId, ids);
        }
        if (!update(updateWrapper)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        // 删除关联数据
        List<RoleMenuDO> roleMenuList = roleMenuRepositoryService.findByMenuIds(ids);
        if (CollectionUtils.isNotEmpty(roleMenuList)) {
            if (!Boolean.TRUE.equals(roleMenuRepositoryService.deleteByMenuIds(
                    roleMenuList.stream().map(RoleMenuDO::getMenuId).collect(Collectors.toSet())
            ))) {
                throw BusinessException.of(ErrorCode.UPDATE_ERROR);
            }
        }
        return true;
    }

    @Override
    public MenuDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<MenuDO> queryWrapper = queryWrapper();
        queryWrapper.eq(MenuDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<MenuDO> findByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MenuDO> queryWrapper = queryWrapper();
        if (ids.size() == 1) {
            queryWrapper.eq(MenuDO::getId, ids.toArray()[0]);
        }
        else {
            queryWrapper.in(MenuDO::getId, ids);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public PageResponse<MenuDO> paging(Integer pageIndex, Integer pageSize, MenuDO menuDO) {
        int index = DefaultUtils.ifNullDefault(pageIndex, 1);
        int size = DefaultUtils.ifNullDefault(pageSize, 10);
        LambdaQueryWrapper<MenuDO> queryWrapper = queryWrapper();
        if (menuDO != null) {
            queryWrapper.eq(menuDO.getName() != null, MenuDO::getName, menuDO.getName());
        }
        queryWrapper.orderByDesc(MenuDO::getSort);
        IPage<MenuDO> page = baseMapper.selectPage(new Page<>(index, size), queryWrapper);
        return PageResponse.build(page.getTotal(), page.getCurrent(), page.getSize(), page.getRecords());
    }

    @Override
    public List<MenuDO> listAll() {
        LambdaQueryWrapper<MenuDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(MenuDO::getSort);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<MenuDO> findByIdsAndTypesAndStatus(Set<Long> ids, Set<Integer> types, Integer status) {
        if (CollectionUtils.isEmpty(ids) || CollectionUtils.isEmpty(types) || status == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MenuDO> queryWrapper = queryWrapper();
        queryWrapper.eq(MenuDO::getStatus, status);

        if (ids.size() == 1) {
            queryWrapper.eq(MenuDO::getId, ids.toArray()[0]);
        }
        else {
            queryWrapper.in(MenuDO::getId, ids);
        }

        if (types.size() == 1) {
            queryWrapper.eq(MenuDO::getType, types.toArray()[0]);
        }
        else {
            queryWrapper.in(MenuDO::getType, types);
        }
        queryWrapper.orderByDesc(MenuDO::getSort);
        return baseMapper.selectList(queryWrapper);
    }

    private LambdaQueryWrapper<MenuDO> queryWrapper() {
        LambdaQueryWrapper<MenuDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MenuDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<MenuDO> updateWrapper() {
        LambdaUpdateWrapper<MenuDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MenuDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
