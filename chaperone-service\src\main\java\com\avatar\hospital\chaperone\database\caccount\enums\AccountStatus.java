package com.avatar.hospital.chaperone.database.caccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum AccountStatus {

    /**
     * 禁用
     */
    DISABLE(0, "禁用"),


    /**
     * 启用
     */
    ENABLE(1, "启用"),


    ;

    private final Integer status;

    private final String describe;


    AccountStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static AccountStatus of(Integer status) {
        if (status == null) {
            return null;
        }
        for (AccountStatus accountStatus : values()) {
            if (status.equals(accountStatus.getStatus())) {
                return accountStatus;
            }
        }
        return null;
    }
}
