package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 16:47
 **/
@Data
public class OrderModifyDiscountRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @NotNull
    private Long orderId;

    /**
     * 折扣修改
     */
    @Size(min = 0,max = 100)
    private Integer discount;


    /**
     * 类型 0 不影响已产生费用 1 影响已产生费用
     * @see com.avatar.hospital.chaperone.database.order.enums.OrderDiscountType
     */
    private Integer type;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
