package com.avatar.hospital.chaperone.response.repair;

import com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:52
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormFeedbackResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 保修单编号
     */
    private String code;

    /**
     * 报修系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他,7-暖通系统，8-建筑系统，9-消防系统）
     */
    private Integer systemType;

    /**
     * 反馈人员ID
     */
    private Long createBy;

    /**
     * 反馈人员名称
     */
    private String createByName;

    /**
     * 反馈时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createAt;

    /**
     * 反馈结果类型（1-完成任务,2-需协助,3-需要使用备件）
     */
    private Integer feedbackType;

    /**
     * 情况说明
     */
    private String remark;

    /**
     * 附件URL,数组
     */
    private String attachments;

    /**
     * 备件信息
     */
    private List<PartApplyModel> part;


    public String getAttachments() {
        if (StrUtils.hasText(attachments)) {
            // 把最后一个逗号去除
            boolean flag = attachments.endsWith(",");
            if (flag) {
                return attachments.substring(0, attachments.length() - 1);
            }
        }
        return attachments;
    }
}
