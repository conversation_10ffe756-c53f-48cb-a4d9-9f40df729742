# 05-Docker部署文档

## 概述

本文档详细介绍了医院陪护系统的Docker容器化部署方案，包括环境准备、配置说明、部署步骤、运维管理等完整的部署流程。

## 系统架构

### 1. 容器化架构图

```
Docker 容器化架构
├── Nginx (反向代理)
│   ├── 端口: 80, 443
│   └── 负载均衡和静态文件服务
├── 应用服务
│   ├── chaperone-web (后台管理系统)
│   │   ├── 端口: 8081
│   │   └── 管理员、护工调度员接口
│   └── chaperone-consumer (客户端系统)
│       ├── 端口: 8080
│       └── 患者、家属接口
├── 数据存储
│   ├── MySQL 8.0 (主数据库)
│   │   ├── 端口: 3306
│   │   └── 持久化存储业务数据
│   └── Redis 7 (缓存数据库)
│       ├── 端口: 6379
│       └── 缓存和会话存储
└── 数据卷
    ├── mysql_data (MySQL数据持久化)
    ├── redis_data (Redis数据持久化)
    ├── web_logs (后台系统日志)
    ├── consumer_logs (客户端系统日志)
    └── nginx_logs (Nginx日志)
```

### 2. 网络配置

- **网络名称**: `chaperone-network`
- **网络类型**: bridge
- **容器间通信**: 通过服务名进行内部通信
- **外部访问**: 通过端口映射提供外部访问

## 环境要求

### 3. 系统要求

#### 3.1 硬件要求

**最低配置**:
- CPU: 2核心
- 内存: 4GB
- 存储: 20GB可用空间
- 网络: 100Mbps

**推荐配置**:
- CPU: 4核心
- 内存: 8GB
- 存储: 50GB可用空间（SSD推荐）
- 网络: 1Gbps

#### 3.2 软件要求

**必需软件**:
- Docker 20.10+
- Docker Compose 2.0+
- Maven 3.6+
- JDK 1.8+

**操作系统支持**:
- Linux (Ubuntu 18.04+, CentOS 7+)
- Windows 10/11 (Docker Desktop)
- macOS 10.14+ (Docker Desktop)

### 4. 环境准备

#### 4.1 安装Docker (Linux)

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
```

#### 4.2 安装Docker Compose

```bash
# 下载Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

#### 4.3 安装Maven

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install maven

# CentOS/RHEL
sudo yum install maven

# 验证安装
mvn --version
```

## 配置说明

### 5. 环境变量配置

#### 5.1 .env 文件配置

```bash
# 环境配置
ENV=prod

# MySQL 配置
MYSQL_ROOT_PASSWORD=root123456
MYSQL_DATABASE=hospital_chaperone
MYSQL_USER=chaperone
MYSQL_PASSWORD=chaperone123
MYSQL_PORT=3306

# Redis 配置
REDIS_PORT=6379

# 应用端口配置
WEB_PORT=8081
CONSUMER_PORT=8080

# Nginx 端口配置
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# JVM 配置
JAVA_OPTS_WEB=-Xms512m -Xmx1024m -XX:+UseG1GC
JAVA_OPTS_CONSUMER=-Xms512m -Xmx1024m -XX:+UseG1GC
```

#### 5.2 生产环境配置调整

**高性能配置**:
```bash
# 高性能环境变量
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_PASSWORD=your_secure_password
JAVA_OPTS_WEB=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
JAVA_OPTS_CONSUMER=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
```

### 6. 数据库配置

#### 6.1 MySQL 配置优化

**文件位置**: `docker/mysql/conf.d/my.cnf`

```ini
[mysqld]
# 性能优化
innodb_buffer_pool_size=1G
innodb_log_file_size=256M
innodb_log_buffer_size=32M
max_connections=2000

# 字符集配置
character-set-server=utf8mb4
collation-server=utf8mb4_general_ci

# 时区配置
default-time-zone='+8:00'
```

#### 6.2 Redis 配置优化

**文件位置**: `docker/redis/redis.conf`

```conf
# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
appendonly yes
```

### 7. Nginx 配置

#### 7.1 反向代理配置

**文件位置**: `docker/nginx/conf.d/default.conf`

```nginx
# 后台管理系统
upstream chaperone-web {
    server chaperone-web:8081;
}

# 客户端系统
upstream chaperone-consumer {
    server chaperone-consumer:8080;
}

server {
    listen 80;
    server_name admin.hospital-chaperone.com;
    
    location /api/v1/web/ {
        proxy_pass http://chaperone-web;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 部署步骤

### 8. 快速部署

#### 8.1 一键部署 (Linux/macOS)

```bash
# 克隆项目
git clone <repository-url>
cd hospital-chaperone

# 给部署脚本执行权限
chmod +x deploy.sh

# 执行一键部署
./deploy.sh deploy
```

#### 8.2 一键部署 (Windows)

```cmd
# 克隆项目
git clone <repository-url>
cd hospital-chaperone

# 执行一键部署
deploy.bat deploy
```

### 9. 分步部署

#### 9.1 步骤1: 构建应用

```bash
# 清理并编译项目
mvn clean package -DskipTests

# 检查构建结果
ls -la chaperone-web-start/target/chaperone-web-start-*.jar
ls -la chaperone-consumer-start/target/chaperone-consumer-start-*.jar
```

#### 9.2 步骤2: 构建Docker镜像

```bash
# 构建后台管理系统镜像
docker build -f Dockerfile.web -t hospital-chaperone-web:latest .

# 构建客户端系统镜像
docker build -f Dockerfile.consumer -t hospital-chaperone-consumer:latest .

# 查看构建的镜像
docker images | grep hospital-chaperone
```

#### 9.3 步骤3: 启动服务

```bash
# 创建网络
docker network create chaperone-network

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

#### 9.4 步骤4: 验证部署

```bash
# 检查容器状态
docker-compose ps

# 检查服务健康状态
curl http://localhost:8081/actuator/health
curl http://localhost:8080/actuator/health

# 查看日志
docker-compose logs -f chaperone-web
docker-compose logs -f chaperone-consumer
```

### 10. 数据库初始化

#### 10.1 自动初始化

Docker Compose 会自动执行 `document/sql/` 目录下的SQL脚本：

```bash
document/sql/
├── b_account.sql      # 后台账户表
├── c_account.sql      # 客户账户表
└── ruoyi.sql         # 基础系统表
```

#### 10.2 手动初始化

如果需要手动初始化数据库：

```bash
# 连接到MySQL容器
docker-compose exec mysql mysql -u root -p

# 执行SQL脚本
mysql> source /docker-entrypoint-initdb.d/b_account.sql;
mysql> source /docker-entrypoint-initdb.d/c_account.sql;
mysql> source /docker-entrypoint-initdb.d/ruoyi.sql;
```

## 运维管理

### 11. 常用运维命令

#### 11.1 服务管理

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart

# 重启特定服务
docker-compose restart chaperone-web

# 查看服务状态
docker-compose ps

# 查看服务资源使用情况
docker stats
```

#### 11.2 日志管理

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs chaperone-web
docker-compose logs chaperone-consumer

# 实时跟踪日志
docker-compose logs -f chaperone-web

# 查看最近100行日志
docker-compose logs --tail=100 chaperone-web
```

#### 11.3 数据备份

```bash
# 备份MySQL数据
docker-compose exec mysql mysqldump -u root -p hospital_chaperone > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份Redis数据
docker-compose exec redis redis-cli BGSAVE

# 备份数据卷
docker run --rm -v hospital-chaperone_mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql_backup_$(date +%Y%m%d_%H%M%S).tar.gz /data
```

#### 11.4 数据恢复

```bash
# 恢复MySQL数据
docker-compose exec -T mysql mysql -u root -p hospital_chaperone < backup_20231101_120000.sql

# 恢复数据卷
docker run --rm -v hospital-chaperone_mysql_data:/data -v $(pwd):/backup alpine tar xzf /backup/mysql_backup_20231101_120000.tar.gz -C /
```

### 12. 监控和健康检查

#### 12.1 健康检查端点

```bash
# 后台管理系统健康检查
curl http://localhost:8081/actuator/health

# 客户端系统健康检查
curl http://localhost:8080/actuator/health

# 系统信息
curl http://localhost:8081/actuator/info
```

#### 12.2 性能监控

```bash
# 查看容器资源使用情况
docker stats

# 查看容器详细信息
docker inspect hospital-chaperone-web

# 查看网络信息
docker network inspect chaperone-network
```

### 13. 故障排查

#### 13.1 常见问题

**问题1: 容器启动失败**
```bash
# 查看容器日志
docker-compose logs container_name

# 查看容器详细信息
docker inspect container_name

# 检查端口占用
netstat -tulpn | grep :8081
```

**问题2: 数据库连接失败**
```bash
# 检查MySQL容器状态
docker-compose ps mysql

# 测试数据库连接
docker-compose exec mysql mysql -u chaperone -p hospital_chaperone

# 查看MySQL日志
docker-compose logs mysql
```

**问题3: 应用无法访问**
```bash
# 检查网络连接
docker network ls
docker network inspect chaperone-network

# 检查端口映射
docker-compose ps

# 测试内部网络连接
docker-compose exec chaperone-web ping mysql
```

#### 13.2 日志分析

```bash
# 查看应用启动日志
docker-compose logs chaperone-web | grep "Started"

# 查看错误日志
docker-compose logs chaperone-web | grep "ERROR"

# 查看数据库连接日志
docker-compose logs chaperone-web | grep "database"
```

## 安全配置

### 14. 生产环境安全

#### 14.1 密码安全

```bash
# 生成强密码
openssl rand -base64 32

# 更新.env文件中的密码
MYSQL_ROOT_PASSWORD=your_generated_strong_password
MYSQL_PASSWORD=your_generated_strong_password
```

#### 14.2 网络安全

```bash
# 限制外部访问（仅允许必要端口）
# 在docker-compose.yml中移除不必要的端口映射

# 使用防火墙限制访问
sudo ufw allow 80
sudo ufw allow 443
sudo ufw deny 3306  # 禁止外部直接访问数据库
```

#### 14.3 SSL/TLS配置

```nginx
# 在nginx配置中添加SSL
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
}
```

## 扩展和优化

### 15. 性能优化

#### 15.1 JVM优化

```bash
# 在.env文件中调整JVM参数
JAVA_OPTS_WEB=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+PrintGCDetails
JAVA_OPTS_CONSUMER=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+PrintGCDetails
```

#### 15.2 数据库优化

```sql
-- MySQL性能优化
SET GLOBAL innodb_buffer_pool_size = 2147483648;  -- 2GB
SET GLOBAL max_connections = 2000;
SET GLOBAL query_cache_size = 134217728;  -- 128MB
```

#### 15.3 缓存优化

```conf
# Redis配置优化
maxmemory 1gb
maxmemory-policy allkeys-lru
tcp-keepalive 60
```

### 16. 水平扩展

#### 16.1 多实例部署

```yaml
# docker-compose.yml 扩展配置
services:
  chaperone-web-1:
    # ... 配置
  chaperone-web-2:
    # ... 配置
  
  nginx:
    # 负载均衡配置
```

#### 16.2 负载均衡

```nginx
upstream chaperone-web {
    server chaperone-web-1:8081;
    server chaperone-web-2:8081;
}
```

## 总结

### 17. 部署检查清单

- [ ] 环境依赖安装完成
- [ ] 配置文件正确设置
- [ ] 应用构建成功
- [ ] Docker镜像构建完成
- [ ] 服务启动正常
- [ ] 健康检查通过
- [ ] 数据库初始化完成
- [ ] 网络连接正常
- [ ] 日志输出正常
- [ ] 备份策略配置

### 18. 维护建议

1. **定期备份**: 每日备份数据库和重要配置
2. **监控告警**: 配置服务监控和告警机制
3. **日志轮转**: 配置日志轮转避免磁盘空间不足
4. **安全更新**: 定期更新基础镜像和依赖
5. **性能调优**: 根据实际使用情况调整配置参数

通过本文档的详细指导，您可以成功部署和维护医院陪护系统的Docker容器化环境。
