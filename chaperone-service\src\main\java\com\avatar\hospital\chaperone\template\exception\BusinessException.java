package com.avatar.hospital.chaperone.template.exception;


import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public class BusinessException extends BaseException {

    private BusinessException() {

    }

    public BusinessException(String errorCode, String errorMessage) {
        super(errorCode, errorMessage);
    }

    public BusinessException(String errorCode, String errorMessage, Object errorData) {
        super(errorCode, errorMessage, errorData);
    }

    public BusinessException(IErrorCode iErrorCode) {
        super(iErrorCode.getErrorCode(), iErrorCode.getErrorMessage(), iErrorCode.getErrorData());
    }

    public static BusinessException of(String errorCode, String errorMessage, Object errorData) {
        return new BusinessException(errorCode, errorMessage, errorData);
    }

    public static BusinessException of(String errorCode, String errorMessage) {
        return new BusinessException(errorCode, errorMessage);
    }

    public static BusinessException of(IErrorCode iErrorCode) {
        return new BusinessException(iErrorCode);
    }

    public static BusinessException buildBusinessException(IErrorCode iErrorCode) {
        BusinessException businessException = new BusinessException();
        String errorCode = iErrorCode.getErrorCode();
        if (Objects.nonNull(errorCode)) {
            businessException.setErrorCode(errorCode);
        }
        String errorMessage = iErrorCode.getErrorMessage();
        if (Objects.nonNull(errorMessage)) {
            businessException.setErrorMessage(errorMessage);
        }
        Object errorData = iErrorCode.getErrorData();
        if (Objects.nonNull(errorData)) {
            businessException.setErrorData(errorData);
        }
        return businessException;
    }

}
