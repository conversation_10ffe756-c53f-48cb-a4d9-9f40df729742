package com.avatar.hospital.chaperone.database.message.repository;

import com.avatar.hospital.chaperone.database.message.dataobject.MessageDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 消息-C端; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface MessageRepositoryService extends IService<MessageDO> {

    /**
     * 添加消息记录
     *
     * @param messageDO -
     * @return -
     */
    Long add(MessageDO messageDO);

    /**
     * 更新消息记录
     *
     * @param messageId    -
     * @param sendFlag     -
     * @param result       -
     * @param errorMessage -
     * @return -
     */
    Boolean updateMessageRecord(Long messageId, Integer sendFlag, String result, String errorMessage);

    /**
     * 根据ID查询
     *
     * @param id -
     * @return -
     */
    MessageDO findById(Long id);
}
