package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.CashLogDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderBillDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderPayDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillType;
import com.avatar.hospital.chaperone.database.order.mapper.OrderBillMapper;
import com.avatar.hospital.chaperone.database.order.repository.CashLogRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderBillRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderPayRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单-账单; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
@RequiredArgsConstructor
public class OrderBillRepositoryServiceImpl extends ServiceImpl<OrderBillMapper, OrderBillDO> implements OrderBillRepositoryService {

    private final OrderPayRepositoryService orderPayRepositoryService;
    private final CashLogRepositoryService cashLogRepositoryService;

    @Override
    public OrderBillDO getTotalByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderBillDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderBillDO::getOrderId,orderId);
        queryWrapper.eq(OrderBillDO::getParentBillId, OrderConst.TOP_BILL);
        return getOne(queryWrapper);
    }

    @Override
    public OrderBillDO getRepayByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderBillDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderBillDO::getOrderId,orderId);
        queryWrapper.ne(OrderBillDO::getParentBillId,OrderConst.TOP_BILL);
        queryWrapper.eq(OrderBillDO::getBillType, OrderBillType.PREPAY.getStatus());
        return getOne(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean modifyBillType(OrderBillDO totalBillUpdate, OrderBillDO repayBill, OrderBillDO oldRepayBill) {
        AssertUtils.isTrue(updateById(totalBillUpdate), ErrorCode.UPDATE_ERROR);
        if (Objects.nonNull(oldRepayBill)) {
            AssertUtils.isTrue(updateById(oldRepayBill), ErrorCode.UPDATE_ERROR);
        }
        if (Objects.nonNull(repayBill)) {
            AssertUtils.isTrue(save(repayBill), ErrorCode.INSERT_ERROR);
        }
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cashPay(OrderBillDO billDO, OrderPayDO payDO,CashLogDO cashLog) {
        AssertUtils.isTrue(updateById(billDO), ErrorCode.UPDATE_ERROR);
        AssertUtils.isTrue(orderPayRepositoryService.save(payDO), ErrorCode.INSERT_ERROR);
        AssertUtils.isTrue(cashLogRepositoryService.save(cashLog), ErrorCode.INSERT_ERROR);
        return Boolean.TRUE;
    }

    @Override
    public List<Long> findAllTotal(Integer type, Integer subType) {
        return baseMapper.findAllTotal(type,subType);
    }

    @Override
    public List<OrderBillDO> findAllSubBillByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderBillDO> queryWrapper = queryWrapper();
        queryWrapper.ne(OrderBillDO::getParentBillId,0);
        queryWrapper.eq(OrderBillDO::getOrderId,orderId);
        List<OrderBillDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByWxNotify(OrderPayDO updateEntity, OrderBillDO updateBillEntity) {
        AssertUtils.isTrue(orderPayRepositoryService.updateById(updateEntity),ErrorCode.UPDATE_ERROR);
        AssertUtils.isTrue(updateById(updateBillEntity),ErrorCode.UPDATE_ERROR);
    }

    @Override
    public void checkExist(Long orderId) {
        OrderBillDO orderBillDO = getTotalByOrderId(orderId);
        if (Objects.isNull(orderBillDO) || OrderBillType.NONE.getStatus().equals(orderBillDO.getBillType())) {
            throw BusinessException.buildBusinessException(ErrorCode.ORDER_NOT_SET_BILL_ERROR);
        }
    }

    @Override
    public void add(OrderBillDO newOrderBill) {
        LambdaQueryWrapper<OrderBillDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderBillDO::getOrderId,newOrderBill.getOrderId());
        queryWrapper.eq(OrderBillDO::getBillType,newOrderBill.getBillType());
        queryWrapper.eq(OrderBillDO::getBillSubType,newOrderBill.getBillSubType());
        queryWrapper.eq(OrderBillDO::getBizId,newOrderBill.getBizId());
        OrderBillDO billDO = getOne(queryWrapper);
        if (Objects.isNull(billDO)) {
            save(newOrderBill);
        } else {
            newOrderBill.setId(billDO.getId());
            updateById(newOrderBill);
        }
    }

    @Override
    public OrderBillDO getNewBill(Long orderId) {
        LambdaQueryWrapper<OrderBillDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderBillDO::getOrderId,orderId);
        queryWrapper.ne(OrderBillDO::getParentBillId,OrderConst.TOP_BILL);
        queryWrapper.orderByDesc(OrderBillDO::getCreatedAt);
        List<OrderBillDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public Long unpay(LocalDateTime start, LocalDateTime end) {
        QueryWrapper<OrderBillDO> queryWrapper = new QueryWrapper();
        queryWrapper.select("SUM(price_receivable) as id")
                .lambda()
                .eq(OrderBillDO::getDeleted, DelUtils.NO_DELETED)
                .ne(OrderBillDO::getParentBillId, OrderConst.TOP_BILL)
                .ge(OrderBillDO::getCreatedAt, start)
                .lt(OrderBillDO::getCreatedAt, end);
        OrderBillDO total = getOne(queryWrapper);
        Long totalPrice = Objects.isNull(total) ? 0L : total.getId();

        Long pay = baseMapper.readypay(start,end);
        pay = Objects.isNull(pay) ? 0L : pay;
        // 总金额 - 已付款 = 未付款
        return totalPrice - pay;
    }

    @Override
    public void updateRepayStartTime(Long orderId, Integer realStartTime) {
        OrderBillDO billDO = getRepayByOrderId(orderId);
        if (Objects.nonNull(billDO)) {
            billDO.setStartTime(DateUtils.toyyyyMMddHHmmssStr(realStartTime));
            billDO.setUpdatedAt(LocalDateTime.now());
            updateById(billDO);
        }
    }


    private LambdaQueryWrapper<OrderBillDO> queryWrapper() {
        LambdaQueryWrapper<OrderBillDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderBillDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
