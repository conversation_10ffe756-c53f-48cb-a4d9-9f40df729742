package com.avatar.hospital.chaperone.database.part.repository.impl;

import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.mapper.PartBatchMapper;
import com.avatar.hospital.chaperone.database.part.repository.PartBatchRepositoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 备品备件批次 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Slf4j
@Service
public class PartBatchRepositoryServiceImpl extends ServiceImpl<PartBatchMapper, PartBatchDO> implements PartBatchRepositoryService {

    @Override
    public Boolean updateBalance(Long id, Integer quality) {
        int i = baseMapper.updateBalance(id, quality);
        log.info("PartBatchRepositoryServiceImpl[]updateBalance >> id:{}, quality:{},i:{}",id, quality,i);
        return Boolean.TRUE;
    }

}
