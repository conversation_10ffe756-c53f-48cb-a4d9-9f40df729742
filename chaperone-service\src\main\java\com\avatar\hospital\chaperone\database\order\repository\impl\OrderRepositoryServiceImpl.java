package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.item.dataobject.ItemDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderBillDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderStatus;
import com.avatar.hospital.chaperone.database.order.mapper.OrderMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderBillRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderItemRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单主表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
@RequiredArgsConstructor
public class OrderRepositoryServiceImpl extends ServiceImpl<OrderMapper, OrderDO> implements OrderRepositoryService {
    private final OrderBillRepositoryService orderBillRepositoryService;
    private final OrderItemRepositoryService orderItemRepositoryService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(OrderDO orderDO, OrderBillDO orderBillDO, List<OrderItemDO> orderItemDOList) {
        long orderId = IdUtils.getId();
        long totalBillId = IdUtils.getId();
        orderDO.setId(orderId);
        orderDO.setParentBillId(totalBillId);
        orderBillDO.setId(IdUtils.getId());
        orderBillDO.setOrderId(orderId);
        orderItemDOList.forEach(item -> item.setOrderId(orderId));

        AssertUtils.isTrue(save(orderDO), ErrorCode.INSERT_ERROR);
        AssertUtils.isTrue(orderBillRepositoryService.save(orderBillDO), ErrorCode.INSERT_ERROR);
        if (!CollectionUtils.isEmpty(orderItemDOList)) {
            AssertUtils.isTrue(orderItemRepositoryService.saveBatch(orderItemDOList), ErrorCode.INSERT_ERROR);
        }
        return orderId;
    }

    @Override
    public List<OrderDO> findAllNeedSettle(Integer dateHour) {
        LambdaQueryWrapper<OrderDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrderDO::getOrderStatus, OrderStatus.WORK.getStatus(),OrderStatus.APPLY_SETTLE.getStatus());
        queryWrapper.le(OrderDO::getRealEndTime,dateHour);
        List<OrderDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveByModifyItem(OrderDO newOrder, OrderBillDO newTotalBill, List<OrderItemDO> list) {
         AssertUtils.isTrue(updateById(newOrder), ErrorCode.UPDATE_ERROR);
         AssertUtils.isTrue(orderBillRepositoryService.updateById(newTotalBill), ErrorCode.UPDATE_ERROR);
         AssertUtils.isTrue(orderItemRepositoryService.saveOrUpdateBatch(list), ErrorCode.UPDATE_ERROR);
    }

    @Override
    public OrderDO getByIdAndAccountId(Long orderId, Long accountId) {
        LambdaQueryWrapper<OrderDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderDO::getId,orderId);
        queryWrapper.eq(OrderDO::getAccountId,accountId);
        return getOne(queryWrapper);
    }

    @Override
    public List<OrderDO> findAllByNursingId(Long nursingId, Long orderId, Integer startDate, Integer endDate) {
        // 时间加上小时
        startDate = DateUtils.toDateHourStart(startDate);
        endDate = DateUtils.toDateHourEnd(endDate);
        List<OrderDO> list = baseMapper.findAllByNursingId(nursingId,orderId,startDate,endDate);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<OrderDO> findByIdList(List<Long> orderId) {
        LambdaQueryWrapper<OrderDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrderDO::getId,orderId);
        queryWrapper.in(OrderDO::getOrderStatus, OrderStatus.WORK.getStatus(),OrderStatus.APPLY_SETTLE.getStatus());
        return list(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void commitCompleteInfo(OrderDO orderEntity, OrderBillDO totalBill, List<OrderItemDO> itemList) {
        AssertUtils.isTrue(updateById(orderEntity),ErrorCode.UPDATE_ERROR);
        AssertUtils.isTrue(orderBillRepositoryService.updateById(totalBill),ErrorCode.UPDATE_ERROR);
        AssertUtils.isTrue(orderItemRepositoryService.updateBatchById(itemList),ErrorCode.UPDATE_ERROR);
    }

    @Override
    public void modifyDiscount(OrderBillDO totalBill, OrderDO orderEntity) {
        updateById(orderEntity);
        orderBillRepositoryService.updateById(totalBill);
    }

    @Override
    public OrderDO findById(Long orderId) {
        if (Objects.isNull(orderId)) {
            return null;
        }
        OrderDO order = getById(orderId);
        return order;
    }

    @Override
    public List<OrderDO> findAllExpire(Integer dateHour) {
        if (Objects.isNull(dateHour)) {
            return null;
        }
        List<Integer> statusList = Arrays.asList(OrderStatus.COMMIT_HOSPITAL.getStatus(),
                OrderStatus.COMMIT_CERTIFIED_PROPERTY.getStatus(),
                OrderStatus.COMMIT_COMPLETE_INFO.getStatus(),
                OrderStatus.WAIT_CONFIRM.getStatus());

        LambdaQueryWrapper<OrderDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrderDO::getOrderStatus, statusList);
        queryWrapper.le(OrderDO::getRealEndTime,dateHour);
        List<OrderDO> list = list(queryWrapper);
        return list;
    }

    @Override
    public Map<Long, OrderDO> listRef(List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<OrderDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrderDO::getId,orderIdList);
        List<OrderDO> list = list(queryWrapper);
        list = CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
        Map<Long, OrderDO> ref = list.stream()
                .collect(Collectors.toMap(OrderDO::getId, Function.identity()));
        return ref;
    }

    private LambdaQueryWrapper<OrderDO> queryWrapper() {
        LambdaQueryWrapper<OrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderDO::getDeleted, OrderConst.NO_DELETED);
        return queryWrapper;
    }
}
