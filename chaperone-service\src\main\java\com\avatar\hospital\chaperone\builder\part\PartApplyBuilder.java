package com.avatar.hospital.chaperone.builder.part;

import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel;
import com.avatar.hospital.chaperone.database.part.enums.PartApplyStatus;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormFeedbackDO;
import com.avatar.hospital.chaperone.request.part.PartApplyPassRequest;
import com.avatar.hospital.chaperone.request.part.PartApplyRefuseRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormFeedbackCreateRequest;
import com.avatar.hospital.chaperone.response.part.PartApplyResponse;
import com.avatar.hospital.chaperone.response.part.RepairFormFeedbackModel;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:58
 **/
public class PartApplyBuilder {
    public static PartApplyDO createPartApplyDO(RepairFormFeedbackCreateRequest request,
                                                RepairFormFeedbackDO repairFormFeedback) {
        Long operator = request.getOperator();
        long id = IdUtils.getId();

        PartApplyDO partApply = new PartApplyDO();
        partApply.setId(id);
        partApply.setStatus(PartApplyStatus.INIT.getCode());
        partApply.setRepairFormFeedbackId(repairFormFeedback.getId());
        partApply.setRemark(repairFormFeedback.getRemark());
        partApply.setAuditRemark(StrUtils.EMPTY);
        partApply.setCreateBy(operator);
        partApply.setUpdateBy(operator);
        partApply.setDeleted(DelUtils.NO_DELETED);
        return partApply;
    }

    public static PartApplyRefPartBatchDO createPartApplyRefPartBatchDO(RepairFormFeedbackCreateRequest request,
                                                                        PartApplyDO partApply,
                                                                        String partBackName) {
        Long operator = request.getOperator();
        PartApplyModel part = request.getPart();

        PartApplyRefPartBatchDO refPartBatchDO = new PartApplyRefPartBatchDO();
        refPartBatchDO.setSparePartBatchId(part.getPartBatchId());
        refPartBatchDO.setSparePartBatchName(partBackName);
        refPartBatchDO.setQuantity(part.getQuantity());
        refPartBatchDO.setSparePartApplyId(partApply.getId());
        refPartBatchDO.setCreateBy(operator);
        refPartBatchDO.setUpdateBy(operator);
        refPartBatchDO.setDeleted(DelUtils.NO_DELETED);
        return refPartBatchDO;
    }

    public static List<PartApplyResponse> toPartApplyResponseList(List<PartApplyDO> records,
                                                                  List<RepairFormFeedbackDO> repairFormFeedbackList,
                                                                  List<PartApplyRefPartBatchDO> refPartBachList,
                                                                  Map<Long, RepairFormDO> repairIdRef) {
        if (Objects.isNull(records) || records.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, RepairFormFeedbackDO> feedMap = repairFormFeedbackList
                .stream()
                .collect(Collectors.toMap(RepairFormFeedbackDO::getId, Function.identity()));

        Map<Long, PartApplyRefPartBatchDO> refBatchMap = refPartBachList.stream()
                .collect(Collectors.toMap(PartApplyRefPartBatchDO::getSparePartApplyId, Function.identity()));

        List<PartApplyResponse> list = new ArrayList<>(records.size());
        for (PartApplyDO record : records) {
            RepairFormFeedbackDO feedbackDO = feedMap.get(record.getRepairFormFeedbackId());
            PartApplyRefPartBatchDO partApplyRefPartBatchDO = refBatchMap.get(record.getId());
            RepairFormDO repairFormDO = repairIdRef.get(record.getRepairFormId());

            PartApplyResponse partApplyResponse = new PartApplyResponse();
            partApplyResponse.setId(record.getId());
            partApplyResponse.setCode(record.getCode());
            partApplyResponse.setStatus(record.getStatus());
            partApplyResponse.setRepairFormFeedbackInfo(toRepairFormFeedbackModel(feedbackDO));
            partApplyResponse.setPartInfo(toPartApplyRefPartBatchDO(partApplyRefPartBatchDO,feedbackDO));
            partApplyResponse.setAuditRemark(record.getAuditRemark());
            if (Objects.nonNull(repairFormDO)) {
                partApplyResponse.setRepairFormId(repairFormDO.getId());
                partApplyResponse.setRepairFormCode(repairFormDO.getCode());
            }

            list.add(partApplyResponse);
        }
        return list;
    }

    public static final PartApplyModel toPartApplyRefPartBatchDO(PartApplyRefPartBatchDO partApplyRefPartBatchDO,
                                                                 RepairFormFeedbackDO feedbackDO) {
        if (Objects.isNull(partApplyRefPartBatchDO) || Objects.isNull(feedbackDO)) {
            return null;
        }
        PartApplyModel partApplyModel = new PartApplyModel();
        partApplyModel.setFeedbackId(feedbackDO.getId());
        partApplyModel.setPartBatchId(partApplyRefPartBatchDO.getSparePartBatchId());
        partApplyModel.setQuantity(partApplyRefPartBatchDO.getQuantity());
        partApplyModel.setName(partApplyRefPartBatchDO.getSparePartBatchName());
        return partApplyModel;

    }

    public final static RepairFormFeedbackModel toRepairFormFeedbackModel(RepairFormFeedbackDO formFeedbackDO) {
        if (Objects.isNull(formFeedbackDO)) {
            return null;
        }
        return RepairFormFeedbackModel.builder()
                .id(formFeedbackDO.getId())
                .createBy(formFeedbackDO.getCreateBy())
                .createByName(formFeedbackDO.getCreateByName())
                .createAt(formFeedbackDO.getCreatedAt())
                .type(formFeedbackDO.getFeedbackType())
                .remark(formFeedbackDO.getRemark())
                .attachments(formFeedbackDO.getAttachments())
                .build();
    }

    public static PartApplyDO createPartApplyDOByPass(PartApplyPassRequest request) {
        Long operator = request.getOperator();
        LocalDateTime now = LocalDateTime.now();

        PartApplyDO updateEntity = new PartApplyDO();
        updateEntity.setId(request.getId());
        updateEntity.setStatus(PartApplyStatus.PASS.getCode());
        updateEntity.setAuditRemark(request.getAuditRemark());
        updateEntity.setUpdateBy(operator);
        updateEntity.setAuditRemark(StrUtils.hasText(request.getAuditRemark()) ? request.getAuditRemark() : "同意");
        updateEntity.setAuditCompletedTime(now);
        updateEntity.setUpdatedAt(now);
        updateEntity.setAuditAccountId(operator);
        return updateEntity;
    }

    public static PartApplyDO createPartApplyDOByRefuse(PartApplyRefuseRequest request) {
        Long operator = request.getOperator();
        LocalDateTime now = LocalDateTime.now();
        PartApplyDO updateEntity = new PartApplyDO();
        updateEntity.setId(request.getId());
        updateEntity.setStatus(PartApplyStatus.REJECT.getCode());
        updateEntity.setAuditRemark(request.getAuditRemark());
        updateEntity.setAuditRemark(StrUtils.hasText(request.getAuditRemark()) ? request.getAuditRemark() : "拒绝");
        updateEntity.setAuditCompletedTime(now);
        updateEntity.setUpdatedAt(now);
        updateEntity.setUpdateBy(operator);
        updateEntity.setAuditAccountId(operator);
        return updateEntity;
    }
}
