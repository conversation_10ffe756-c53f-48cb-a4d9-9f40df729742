package com.avatar.hospital.chaperone.database.part.mapper;

import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 备件使用申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PartApplyMapper extends BaseMapper<PartApplyDO> {


    /**
     * 反馈单ID
     * @param feedbackIdList
     * @return
     */
    List<PartApplyModel> findMapByFeedbackIdList(@Param("feedbackIdList") List<Long> feedbackIdList);
}
