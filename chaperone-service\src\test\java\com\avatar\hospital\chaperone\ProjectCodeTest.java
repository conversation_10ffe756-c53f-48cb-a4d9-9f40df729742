package com.avatar.hospital.chaperone;

import com.avatar.hospital.chaperone.TestApplication;
import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.service.code.ProjectCodeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @description (webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
 * @date 2023/10/27 11:23
 */

@SpringBootTest(classes = TestApplication.class)
public class ProjectCodeTest {

    @Autowired
    ProjectCodeService projectCodeService;

    @Test
    public void code() {

       String code =  projectCodeService.generateCode(CodeBizType.SB);
        System.out.println(code);

    }

    @Test
    public void test() {

        System.out.println("sss");
    }
}
