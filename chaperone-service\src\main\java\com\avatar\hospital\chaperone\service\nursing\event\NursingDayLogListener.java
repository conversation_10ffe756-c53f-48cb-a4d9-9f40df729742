package com.avatar.hospital.chaperone.service.nursing.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

/**
 * @author:sp0420
 * @Description:
 */

@Slf4j
@Service
@RequiredArgsConstructor
@EnableAsync
public class NursingDayLogListener implements ApplicationListener<NursingDayLogEvent> {

    @Async
    @Override
    public void onApplicationEvent(NursingDayLogEvent event) {

    }
}
