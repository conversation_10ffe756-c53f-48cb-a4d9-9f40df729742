package com.avatar.hospital.chaperone.request.order;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderConsumeResetCalculationRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 操作人
     */
    private Long operator;

    public static final OrderConsumeResetCalculationRequest build(Long orderId,Long operator) {
        OrderConsumeResetCalculationRequest obj = new OrderConsumeResetCalculationRequest();
        obj.setOrderId(orderId);
        obj.setOperator(operator);
        return obj;
    }


}
