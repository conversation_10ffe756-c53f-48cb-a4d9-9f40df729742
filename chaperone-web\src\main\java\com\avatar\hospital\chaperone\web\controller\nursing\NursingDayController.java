package com.avatar.hospital.chaperone.web.controller.nursing;

import com.avatar.hospital.chaperone.request.nursing.NursingDayExportPageRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingDayPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingLeaveRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingSubstituteRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse;
import com.avatar.hospital.chaperone.service.nursing.NursingDayService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.utils.ExportUtil;
import com.avatar.hospital.chaperone.web.validator.nursing.NursingDayValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @author:sp0420
 * @Description: 排班
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/nursing/schedule")
public class NursingDayController {
    private final NursingDayService nursingDayService;


    /**
     * 分页
     */
    @GetMapping(value = "/paging")
    public SingleResponse<PageResponse<NursingDayPagingResponse>> paging(NursingDayPagingRequest request){
        return TemplateProcess.doProcess(log,()->{
            log.info("NursingDayController[]paging[]request:{}",request);
            NursingDayValidator.pagingValidate(request);
            request.setSortType(2);
            return nursingDayService.paging(request);
        });
    }

    /**
     * 请假
     */
    @PostMapping(value = "/leave")
    public SingleResponse<Boolean> leave(@RequestBody NursingLeaveRequest request){
        return TemplateProcess.doProcess(log,()->{
            log.info("NursingDayController[]leave[]request:{}",request);
            NursingDayValidator.leaveValidate(request);
            return nursingDayService.leave(request);
        });
    }

    /**
     * 空闲
     */
    @PostMapping(value = "/cancel-leave")
    public SingleResponse<Boolean> cancelLeave(@RequestBody NursingLeaveRequest request){
        return TemplateProcess.doProcess(log,()->{
            log.info("NursingDayController[]cancelLeave[]request:{}",request);
            NursingDayValidator.cancelLeaveValidate(request);
            return nursingDayService.cancelLeave(request);
        });
    }

    /**
     * 替班
     */
    @PostMapping(value = "/substitute")
    public SingleResponse<Boolean> substitute(@RequestBody NursingSubstituteRequest request){
        return TemplateProcess.doProcess(log,()->{
            log.info("NursingDayController[]substitute[]request:{}",request);
            NursingDayValidator.substituteValidate(request);
            return nursingDayService.substitute(request);
        });
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public void export(@RequestBody NursingDayExportPageRequest request, HttpServletResponse response){
        ExportUtil.export(response,request,nursingDayService::export);
    }
}
