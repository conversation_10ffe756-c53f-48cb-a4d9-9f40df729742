package com.avatar.hospital.chaperone.template.util;


import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.exception.IErrorCode;

import java.util.Collection;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public class AssertUtils {

    public static void isTrue(Boolean expression, String errorCode, String errorMessage) {
        if (expression == null || !expression) {
            throw BusinessException.of(errorCode, errorMessage);
        }
    }

    public static void isTrue(Boolean expression, IErrorCode errorCode) {
        if (expression == null || !expression) {
            throw BusinessException.buildBusinessException(errorCode);
        }
    }

    public static void isFalse(Boolean expression, String errorCode, String errorMessage) {
        if (expression == null || expression) {
            throw BusinessException.of(errorCode, errorMessage);
        }
    }

    public static void isFalse(Boolean expression, IErrorCode errorCode) {
        if (expression == null || expression) {
            throw BusinessException.buildBusinessException(errorCode);
        }
    }

    public static void isNotNull(Object object, String errorCode, String errorMessage) {
        if (object == null) {
            throw BusinessException.of(errorCode, errorMessage);
        }
    }

    public static void isNotNull(Object object, IErrorCode errorCode) {
        if (object == null) {
            throw BusinessException.buildBusinessException(errorCode);
        }
    }

    public static void isNull(Object object, String errorCode, String errorMessage) {
        if (object != null) {
            throw BusinessException.of(errorCode, errorMessage);
        }
    }

    public static void isNull(Object object, IErrorCode errorCode) {
        if (object != null) {
            throw BusinessException.buildBusinessException(errorCode);
        }
    }

    public static void notEmpty(Collection<?> collection, String errorCode, String errorMessage) {
        if (collection == null || collection.isEmpty()) {
            throw BusinessException.of(errorCode, errorMessage);
        }
    }

    public static void notEmpty(Collection<?> collection, IErrorCode errorCode) {
        if (collection == null || collection.isEmpty()) {
            throw BusinessException.buildBusinessException(errorCode);
        }
    }

    public static void hasText(String text, String errorCode, String errorMessage) {
        if (!StrUtils.hasText(text)) {
            throw BusinessException.of(errorCode, errorMessage);
        }
    }

    public static void hasText(String text, IErrorCode errorCode) {
        if (!StrUtils.hasText(text)) {
            throw BusinessException.buildBusinessException(errorCode);
        }
    }

}
