package com.avatar.hospital.chaperone.service.order;

import com.avatar.hospital.chaperone.request.order.OrderEstimatePageRequest;
import com.avatar.hospital.chaperone.request.order.OrderEstimateRequest;
import com.avatar.hospital.chaperone.request.order.OrderEstimateSaveRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderEstimateIdResponse;
import com.avatar.hospital.chaperone.response.order.OrderEstimateResponse;

import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:04
 **/
public interface OrderEstimateService {

    /**
     * 订单评价
     * @param request
     * @return
     */
    OrderEstimateIdResponse create(OrderEstimateSaveRequest request);


    /**
     * 评价查询
     * @param request
     * @return
     */
    List<OrderEstimateResponse> get(OrderEstimateRequest request);

    /**
     * 订单评价列表查询
     * @param request
     * @return
     */
    PageResponse<OrderEstimateResponse> paging(OrderEstimatePageRequest request);
}
