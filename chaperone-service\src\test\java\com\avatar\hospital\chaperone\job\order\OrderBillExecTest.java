package com.avatar.hospital.chaperone.job.order;

import com.avatar.hospital.chaperone.TestApplication;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillSubType;
import com.avatar.hospital.chaperone.utils.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-06 13:16
 **/
@SpringBootTest(classes = TestApplication.class)
public class OrderBillExecTest {

    @Autowired
    private OrderBillExec orderBillExec;

    /**
     * 创建月付账单
     */
    @Test
    public void testCreateCycleByMonth() {
        final Integer startDate = DateUtils.lastMonthStart();
        final Integer endDate = DateUtils.lastMonthEnd();
        orderBillExec.createCycle(OrderBillSubType.MONTH,startDate,endDate);
    }

    /**
     * 创建季度账单
     */
    @Test
    public void testCreateCycleBySeason() {
        final Integer startDate = DateUtils.lastSeasonStart();
        final Integer endDate = DateUtils.lastSeasonEnd();
        orderBillExec.createCycle(OrderBillSubType.SEASON,startDate,endDate);
    }


    /**
     * 创建季度账单
     */
    @Test
    public void testCreateCycleByYearn() {
        final Integer startDate = DateUtils.lastYearStart();
        final Integer endDate = DateUtils.lastMonthEnd();
        orderBillExec.createCycle(OrderBillSubType.YEAR,startDate,endDate);
    }


    /**
     * 创建结算单
     */
    @Test
    public void testCreateSettleByYearn() {
        Integer dateHour = DateUtils.dateHourInt();
        orderBillExec.createSettle(dateHour);
    }

    /**
     * 创建结算单
     */
    @Test
    public void testCreateCycle() {
        orderBillExec.createCycle(301815185914257408L, null,20230930);
    }


}
