package com.avatar.hospital.chaperone.database.part.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/26 19:55
 */
@Getter
public enum InvestorType {
    NONE(-1, "未知"),
    property(1, "物业"),
    hospital(2, "医院"),
    ;

    private final Integer code;

    private final String describe;


    InvestorType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static InvestorType of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (InvestorType type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
