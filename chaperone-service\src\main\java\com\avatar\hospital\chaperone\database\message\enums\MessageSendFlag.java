package com.avatar.hospital.chaperone.database.message.enums;

import lombok.Getter;

/**
 * Description:
 *      消息发送成功标志
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum MessageSendFlag {
    NONE(-1, "未知"),
    INT(0, "待发送"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    ;

    private final Integer status;

    private final String describe;


    MessageSendFlag(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static MessageSendFlag of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (MessageSendFlag itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
