package com.avatar.hospital.chaperone.database.nursing.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum NursingStatus {
    NONE(-1, "未知"),
    INIT(0, "审核中"),
    NORMAL(1, "生效"),
    INVALID(2, "失效"),
    ;

    private final Integer status;

    private final String describe;


    NursingStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static NursingStatus of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (NursingStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
