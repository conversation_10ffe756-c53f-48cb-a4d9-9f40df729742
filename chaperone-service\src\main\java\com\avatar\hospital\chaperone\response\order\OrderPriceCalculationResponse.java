package com.avatar.hospital.chaperone.response.order;

import com.avatar.hospital.chaperone.response.item.ItemResponse;
import com.avatar.hospital.chaperone.utils.OrderUtils;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description: 订单价格计算
 * @author: sp0372
 * @create: 2023-10-11 16:55
 **/
@Data
public class OrderPriceCalculationResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总价格 单位分
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer totalPrice;

    /**
     * 总价格(折扣后的价格) 单位分
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer totalDiscountPrice;

    /**
     * 套餐价格明细
     */
    private List<PriceCalculationItem> itemPriceList;


    public static OrderPriceCalculationResponse build(Integer totalPrice, Integer totalDiscountPrice,List<PriceCalculationItem> itemPriceList) {
        OrderPriceCalculationResponse obj = new OrderPriceCalculationResponse();
        obj.setTotalPrice(totalPrice);
        obj.setTotalDiscountPrice(totalDiscountPrice);
        obj.setItemPriceList(itemPriceList);
        return obj;
    }


    @Data
    public static class PriceCalculationItem {
        /**
         * 套餐ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long itemId;

        /**
         * 套餐名称
         */
        private String name;

        /**
         * 单价，分
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Integer price;

        /**
         * 数量(天数)
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Integer number;

        /**
         * 总价格(原价)
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Integer totalPrice;

        /**
         * 折扣后的价格
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Integer totalDiscountPrice;

        /**
         * 套餐上下价状态 0 下架 1 上架
         * @see com.avatar.hospital.chaperone.database.item.enums.ItemStatus
         */
        private Integer status;

        /**
         * 商品数据
         * @param item 商品数据
         * @param intervalDay 间隔天数
         * @return
         */
        public static PriceCalculationItem build(ItemResponse item, Integer intervalDay,Integer discount) {
            Integer total = item.getPrice() * intervalDay;
            Integer totalDiscount = OrderUtils.calculationDiscount(total, discount);

            PriceCalculationItem priceItem = new PriceCalculationItem();
            priceItem.setItemId(item.getId());
            priceItem.setName(item.getName());
            priceItem.setPrice(item.getPrice());
            priceItem.setNumber(intervalDay);
            priceItem.setTotalPrice(total);
            priceItem.setTotalDiscountPrice(totalDiscount);
            priceItem.setStatus(item.getStatus());
            return priceItem;
        }

        public String toStr() {
            return name + ":" + price + ";";
        }
    }

    public String toItemStr() {
        StringBuilder sb = new StringBuilder();
        for (PriceCalculationItem priceCalculationItem : itemPriceList) {
            sb.append(priceCalculationItem.toStr())
                    .append("\n");
        }
        String str = sb.toString();
        return str;
    }
}
