package com.avatar.hospital.chaperone.consumer.handler;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Slf4j
@SuppressWarnings("ALL")
@RestControllerAdvice
public class ConsumerGlobalExceptionHandler {

    /**
     * 没有登录异常
     *
     * @param request           -
     * @param response          -
     * @param notLoginException -
     * @return -
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(HttpStatus.OK)
    public SingleResponse notLoginException(HttpServletRequest request, HttpServletResponse response, NotLoginException notLoginException) {
        log.info("ConsumerGlobalExceptionHandler notLoginException {}", Throwables.getStackTraceAsString(notLoginException));
        return SingleResponse.buildFailure(ErrorCode.CONSUMER_ACCOUNT_NOT_EXIST.getErrorCode(), ErrorCode.CONSUMER_ACCOUNT_NOT_EXIST.getErrorMessage());
    }

    /**
     * 访问接口没有权限异常
     *
     * @param request                -
     * @param response               -
     * @param notPermissionException -
     * @return -
     */
    @ExceptionHandler(NotPermissionException.class)
    @ResponseStatus(HttpStatus.OK)
    public SingleResponse notPermissionException(HttpServletRequest request, HttpServletResponse response, NotPermissionException notPermissionException) {
        log.info("ConsumerGlobalExceptionHandler notPermissionException {}", Throwables.getStackTraceAsString(notPermissionException));
        return SingleResponse.buildFailure(ErrorCode.CONSUMER_ACCOUNT_NOT_PERMISSION_ERROR.getErrorCode(), ErrorCode.CONSUMER_ACCOUNT_NOT_PERMISSION_ERROR.getErrorMessage());
    }

    /**
     * 普通未拦截异常
     *
     * @param request   -
     * @param response  -
     * @param exception -
     * @return
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public SingleResponse exception(HttpServletRequest request, HttpServletResponse response, Exception exception) {
        log.info("ConsumerGlobalExceptionHandler exception {}", Throwables.getStackTraceAsString(exception));
        return SingleResponse.buildFailure(ErrorCode.SYSTEM_ERROR.getErrorCode(), ErrorCode.SYSTEM_ERROR.getErrorMessage());
    }

    /**
     * 最高级别异常
     *
     * @param request   -
     * @param response  -
     * @param exception -
     * @return
     */
    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.OK)
    public SingleResponse throwableException(HttpServletRequest request, HttpServletResponse response, Throwable throwable) {
        log.info("ConsumerGlobalExceptionHandler throwableException {}", Throwables.getStackTraceAsString(throwable));
        return SingleResponse.buildFailure(ErrorCode.SYSTEM_ERROR.getErrorCode(), ErrorCode.SYSTEM_ERROR.getErrorMessage());
    }

}
