package com.avatar.hospital.chaperone.web.controller.repair;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.builder.repair.RepairFormBuilder;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.repair.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormIdResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormResponse;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.service.repair.RepairFormService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.utils.ExportUtil;
import com.avatar.hospital.chaperone.utils.QrUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * B端保修单
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 13:41
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/repair")
public class RepairFormController {
    private final RepairFormService repairFormService;

    /**
     * 查询
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<RepairFormResponse>> paging(@RequestBody RepairFormPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormService.paging(request);
        });
    }

    /**
     * 创建
     * @param request
     * @return
     */
    @PostMapping("create")
    public SingleResponse<RepairFormIdResponse> create(@RequestBody RepairFormCreateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormService.create(request);
        });
    }

    /**
     * 创建
     * @param request
     * @return
     */
    @PostMapping("modify")
    public SingleResponse<RepairFormIdResponse> modify(@RequestBody RepairFormModifyRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormService.modify(request);
        });
    }

    /**
     * 分派执行人员
     * @param request
     * @return
     */
    @PostMapping("assign-executor")
    public SingleResponse<RepairFormIdResponse> assignExecutor(@RequestBody RepairFormAssignExecutorRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormService.assignExecutor(request);
        });
    }

    /**
     * 审核
     * @param request
     * @return
     */
    @PostMapping("auth")
    public SingleResponse<RepairFormIdResponse> auth(@RequestBody RepairFormAuthRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormService.auth(request);
        });
    }

    /**
     * 二维码测试
     * @param response
     * @return
     */
    @GetMapping("qr")
    public void qr(HttpServletResponse response) throws IOException {
        QrUtils.createCodeToOutputStream("111",response.getOutputStream());
    }

    /**
     * 导出
     *
     * @param param -
     */
    @Idempotent(value = OrderKey.REPAIR_FORM_EXPORT_PREFIX)
    @PostMapping("export")
    public void export(@RequestBody RepairFormExportRequest param, HttpServletResponse response) throws IOException {
        RepairFormPageRequest request = RepairFormBuilder.toRepairFormPageRequest(param);
        request.setPageSize(1);
        PageResponse<RepairFormResponse> paging = repairFormService.paging(request);
        if (Objects.nonNull(paging) && paging.getTotal() > ExportUtil.MAX_EXPORT_SIZE) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            SingleResponse singleResponse = SingleResponse.buildFailure(ErrorCode.EXPORT_LIMIT_ERROR.getErrorCode(), ErrorCode.EXPORT_LIMIT_ERROR.getErrorMessage());
            response.getWriter().println(JSON.toJSONString(singleResponse));
            return;
        }
        ExportUtil.export(response, param, repairFormService::exportPaging, ExcelTypeEnum.XLS);
    }

}
