package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderConsumeCreateRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 日期,格式yyyyMMdd
     */
    private Integer date;

    /**
     * 套餐ID
     */
    private List<Long> itemIdList;

    /**
     * 操作用户
     */
    private Operator operatorUser;

}
