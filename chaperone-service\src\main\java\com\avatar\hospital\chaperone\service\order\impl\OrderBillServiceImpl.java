package com.avatar.hospital.chaperone.service.order.impl;

import com.alibaba.fastjson.JSONObject;
import com.avatar.hospital.chaperone.builder.order.OrderBuilder;
import com.avatar.hospital.chaperone.builder.order.OrderPayBuilder;
import com.avatar.hospital.chaperone.component.wx.WxPayComponent;
import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountOpenIdDO;
import com.avatar.hospital.chaperone.database.caccount.repository.AccountOpenIdRepositoryService;
import com.avatar.hospital.chaperone.database.order.dataobject.*;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillType;
import com.avatar.hospital.chaperone.database.order.enums.OrderLogEvent;
import com.avatar.hospital.chaperone.database.order.enums.OrderPayStatus;
import com.avatar.hospital.chaperone.database.order.enums.OrderTradeType;
import com.avatar.hospital.chaperone.database.order.repository.OrderBillRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderItemRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderNursingRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderPayRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.OrderBillService;
import com.avatar.hospital.chaperone.service.order.OrderInvoiceService;
import com.avatar.hospital.chaperone.service.order.OrderService;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.service.order.dto.OrderBillExportDTO;
import com.avatar.hospital.chaperone.service.order.dto.OrderChangeLogDTO;
import com.avatar.hospital.chaperone.service.order.event.OrderChangelogEvent;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;



/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-17 10:41
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderBillServiceImpl implements OrderBillService {
    private final OrderBillRepositoryService orderBillRepositoryService;
    private final OrderPayRepositoryService orderPayRepositoryService;
    private final WxPayComponent wxPayComponent;
    private final AccountOpenIdRepositoryService accountOpenIdRepositoryService;
    private final OrderItemRepositoryService orderItemRepositoryService;
    private final OrderInvoiceService orderInvoiceService;
    private final RedissonClient redissonClient;

    private final String STATISTICS_SQL = "sum( `price_receivable`) as id";

    @Autowired
    private OrderService orderService;

    @Value("${wx.pay.appId}")
    private String payAppId;
    @Value("${wx.pay.mchId}")
    private String payMchId;

    @Value("${wx.pay.mchKey}")
    private String payMchKey;
    @Value("${wx.pay.notifyUrl}")
    private String notifyUrl;

    @Override
    public PageResponse<OrderBillResponse> pagingForC(OrderBillCPageRequest request) {
        Page<OrderBillDO> page = request.ofPage();
        LambdaQueryWrapper<OrderBillDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderBillDO::getOrderId,request.getOrderId());
        queryWrapper.ne(OrderBillDO::getParentBillId, OrderConst.TOP_BILL);
        queryWrapper.orderByDesc(OrderBillDO::getId);
        page = orderBillRepositoryService.page(page,queryWrapper);
        List<OrderBillResponse> list = page.getRecords().stream()
                .map(bill -> {
                    OrderBillResponse response = OrderBuilder.toOrderBillResponse(bill);
                    OrderInvoiceResponse invoice = orderInvoiceService.getByBillId(bill.getId());
                    response.setInvoice(invoice);
                    return response;
                }).collect(Collectors.toList());
        return PageResponse.build(page, list);
    }

    @Override
    public PageResponse<OrderBillBResponse> paging(OrderBillPageRequest request) {
        List<LocalDateTime> createTimeList = DateUtils.toLocalDateTimeList(request.getCreateTime());
        List<Integer> statusList = Arrays.asList(OrderBillType.CYCLE.getStatus(), OrderBillType.PREPAY.getStatus(),OrderBillType.SETTLE.getStatus());
        // 查询所有总账单的数据
        Page<OrderBillDO> page = request.ofPage();
        LambdaQueryWrapper<OrderBillDO> queryWrapper = queryWrapper();
        queryWrapper.eq(Objects.nonNull(request.getOrderId()),OrderBillDO::getOrderId,request.getOrderId());
        queryWrapper.eq(Objects.nonNull(request.getTradeType()),OrderBillDO::getTradeType,request.getTradeType());
        queryWrapper.ge(Objects.nonNull(createTimeList.get(0)),OrderBillDO::getCreatedAt,createTimeList.get(0));
        queryWrapper.le(Objects.nonNull(createTimeList.get(1)),OrderBillDO::getCreatedAt,createTimeList.get(1));
        if (Objects.nonNull(request.getFilterRefundBill()) && Objects.equals(1,request.getFilterRefundBill())) {
            // 应退金额大于 0 为退款
            queryWrapper.gt(OrderBillDO::getPriceRefundable,0);
        }
        if (Objects.equals(0, request.getFilterRefundBill())) {
            queryWrapper.eq(OrderBillDO::getPriceRefundable, 0);
        }
        queryWrapper.ne(OrderBillDO::getParentBillId, OrderConst.TOP_BILL);
        queryWrapper.in(OrderBillDO::getBillType, statusList);
        queryWrapper.orderByDesc(OrderBillDO::getId);
        page = orderBillRepositoryService.page(page,queryWrapper);
        // 根据账单,查询所有有效支付订单
        List<Long> orderIds = CollUtils.toListLongDistinct(page.getRecords(),OrderBillDO::getOrderId);
        List<OrderPayDO> orderPayList = orderPayRepositoryService.findByOrderIdList(orderIds);
        Map<Long, OrderPayDO> orderPayMap = orderPayList.stream()
                .collect(Collectors.toMap(OrderPayDO::getBillId, Function.identity(), (v1, v2) -> v1.getId() > v2.getId() ? v1 : v2));
        // 查询有效套餐信息
        Map<Long, String> orderItemRef = orderItemRepositoryService.listByOrderIdList(orderIds);
        // 查询发票信息
        List<Long> billIds = CollUtils.toListLongDistinct(page.getRecords(),OrderBillDO::getId);
        Map<Long, OrderInvoiceResponse> invoiceMap = orderInvoiceService.getByBillIdList(billIds);
        List<OrderBillBResponse> list = OrderBuilder.toOrderBillBResponseList(page.getRecords(),orderPayMap,orderItemRef,invoiceMap);
        return PageResponse.build(page, list);
    }

    @Override
    public List<OrderBillExportDTO> exportPaging(OrderBillExportPageRequest request) {
        OrderBillPageRequest req = toOrderBillPageRequest(request);
        PageResponse<OrderBillBResponse> paging = paging(req);
        List<OrderBillExportDTO> list = OrderBuilder.toOrderBillExportDTOList(paging.getRecords());
        return list;
    }

    private OrderBillPageRequest toOrderBillPageRequest(OrderBillExportPageRequest request) {
        OrderBillPageRequest orderBillPageRequest = new OrderBillPageRequest();
        orderBillPageRequest.setOrderId(request.getOrderId());
        orderBillPageRequest.setTradeType(request.getTradeType());
        orderBillPageRequest.setCreateTime(request.getCreateTime());
        orderBillPageRequest.setFilterRefundBill(request.getFilterRefundBill());

        orderBillPageRequest.setPageIndex(request.getPageIndex());
        orderBillPageRequest.setPageSize(request.getPageSize());

        Operator operatorUser = new Operator();
        operatorUser.setId(request.getOperatorUser().getId());
        operatorUser.setMobile(request.getOperatorUser().getMobile());
        operatorUser.setName(request.getOperatorUser().getName());
        operatorUser.setSource(request.getOperatorUser().getSource());
        orderBillPageRequest.setOperatorUser(operatorUser);
        return orderBillPageRequest;
    }

    @Override
    public OrderBillIdResponse create(OrderBillCreateRequest request) {
        long billId = IdUtils.getId();
        OrderBillDO orderBillDO = OrderBuilder.createOrderBill(request);
        orderBillDO.setId(billId);
        orderBillRepositoryService.add(orderBillDO);
        return OrderBillIdResponse.build(billId);
    }

    @Override
    public List<OrderBillResponse> findAll(OrderBillFindAllRequest request) {
        LambdaQueryWrapper<OrderBillDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderBillDO::getOrderId,request.getOrderId());
        queryWrapper.eq(Objects.nonNull(request.getBillType()),OrderBillDO::getBillType,request.getBillType());
        queryWrapper.ne(OrderBillDO::getParentBillId,OrderConst.TOP_BILL);
        List<OrderBillDO> list = orderBillRepositoryService.list(queryWrapper);

        List<OrderBillResponse> result = list.stream()
                .map(OrderBuilder::toOrderBillResponse)
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public OrderBillIdResponse modifyBillType(OrderBillModifyTradeRequest request) {
        Long delVersion = DelUtils.delVersion();

        Long orderId = request.getOrderId();
        OrderDetailResponse order = orderService.getById(OrderRequest.buildById(orderId));
        String patientName = order.getBase().getPatientName();

        OrderBillDO totalBill = orderBillRepositoryService.getTotalByOrderId(orderId);
        AssertUtils.isNotNull(totalBill,ErrorCode.ORDER_NOT_EXIST);

        OrderBillDO oldRepayBill = orderBillRepositoryService.getRepayByOrderId(orderId);
        if (Objects.nonNull(oldRepayBill)) {
            oldRepayBill.setDeleted(delVersion);
        }

        OrderBillDO totalBillUpdate = OrderBuilder.createOrderBillDOByModifyBillType(totalBill.getId(),request);
        OrderBillDO repayBill = null;
        if (OrderBillType.of(request.getBillType()).isPrepay()) {
            repayBill = OrderBuilder.createOrderBillPrepayByModify(totalBill.getId(),request,patientName);
        }

        orderBillRepositoryService.modifyBillType(totalBillUpdate,repayBill,oldRepayBill);

        SpringUtils.publishEvent(OrderChangelogEvent.build(this, OrderLogEvent.ORDER_MODIFY_BILL,
                OrderChangeLogDTO.buildByModifyBillInfoBefore(totalBill,oldRepayBill),
                OrderChangeLogDTO.buildByModifyBillInfoAfter(request),
                request.getOperatorUser()));
        return OrderBillIdResponse.build(totalBill.getId());
    }

    @Override
    public OrderBillPayResponse pay(OrderBillPayRequest request) {
        Long operator = request.getOperator();
        OrderBillDO orderBillDO = orderBillRepositoryService.getById(request.getBillId());
        AssertUtils.isNotNull(orderBillDO,ErrorCode.ORDER_BILL_NOT_EXIST);

        Integer price = orderBillDO.getPriceReceivable() - orderBillDO.getPriceReceived();
        AssertUtils.isFalse(price <= 0,ErrorCode.ORDER_BILL_ALREADY_PAY_ERROR);

        AccountOpenIdDO accountOpenId = accountOpenIdRepositoryService.findByAccountIdAndAppId(operator,payAppId);
        AssertUtils.isNotNull(accountOpenId,ErrorCode.CONSUMER_ACCOUNT_PAY_OPENID_ERROR);

        // 查询是否已存在未过期的支付单,且金额相同
        OrderPayDO payDO = orderPayRepositoryService.getPay(request.getBillId(),price);
        if (Objects.nonNull(payDO)) {
            OrderBillPayInfoResponse payInfo = ObjUtils.jsonParseObject(payDO.getPayInfo(), OrderBillPayInfoResponse.class);
            return OrderBuilder.createOrderBillPayResponseByCreate(payDO,payInfo);
        }
        payDO = OrderBuilder.createOrderPayDOByPay(request,price,orderBillDO.getOrderId());
        boolean saveFlag = orderPayRepositoryService.save(payDO);
        AssertUtils.isTrue(saveFlag,ErrorCode.SYSTEM_ERROR);

        // 微信下单
        WxPayUnifiedOrderRequest payRequest = toWxPayUnifiedOrderRequest(accountOpenId.getOpenId(),payDO,request.getIp());
        WxPayUnifiedOrderResult payResult = wxPayComponent.pay(payRequest);
        if (Objects.isNull(payResult)) {
            orderPayRepositoryService.updateById(OrderPayBuilder.updateDel(payDO.getId()));
            throw BusinessException.buildBusinessException(ErrorCode.ORDER_PAY_CREATE_ERROR);
        }
        OrderBillPayInfoResponse payInfo = OrderBuilder.toOrderBillPayInfoResponse(payAppId,payMchId, payMchKey,payResult);
        orderPayRepositoryService.updateById(OrderPayBuilder.updatePayInfo(payDO.getId(),payInfo));
        OrderBillPayResponse response = OrderBuilder.createOrderBillPayResponseByCreate(payDO,payInfo);
        return response;
    }

    @Override
    public OrderBillPayResponse payInfo(OrderBillPayRequest request) {
        OrderBillDO orderBillDO = orderBillRepositoryService.getById(request.getBillId());
        AssertUtils.isNotNull(orderBillDO,ErrorCode.ORDER_NOT_EXIST);

        // 查询是否已存在未过期的支付单,且金额相同
        Integer price = orderBillDO.getPriceReceivable() - orderBillDO.getPriceReceived();
        OrderPayDO payDO = orderPayRepositoryService.getPay(request.getBillId(),price);
        if (Objects.nonNull(payDO)) {
            OrderBillPayInfoResponse payInfo = null;
            if (Objects.nonNull(payDO.getPayInfo())) {
                payInfo = JSONObject.parseObject(payDO.getPayInfo(), OrderBillPayInfoResponse.class);
            }
            return OrderBuilder.createOrderBillPayResponseByCreate(payDO,payInfo);
        }
        return null;
    }

    /**
     * 生成交易请求
     *
     * @param openId
     * @param payDO 订单事情
     * @return
     */
    public WxPayUnifiedOrderRequest toWxPayUnifiedOrderRequest(String openId,
                                                                   OrderPayDO payDO,String ip) {

        String timeExpire = payDO.getPayOutTime();

        WxPayUnifiedOrderRequest request = new WxPayUnifiedOrderRequest();
        request.setAppid(payAppId);
        request.setMchId(payMchId);
        request.setBody(OrderConst.PAY_DESCRIPTION);
        request.setOutTradeNo(payDO.getId().toString());
        request.setTotalFee(payDO.getPrice());
        request.setNotifyUrl(notifyUrl);
        request.setTradeType("JSAPI");
        request.setTimeExpire(timeExpire);
        request.setSpbillCreateIp(ip);
        request.setOpenid(openId);
        return request;
    }

    /**
     * 生成交易请求
     *
     * @param openId
     * @param payDO 订单事情
     * @return
     */
    public WxPayUnifiedOrderV3Request toWxPayUnifiedOrderV3Request(String openId,
                                                                   OrderPayDO payDO) {
        String timeExpire = payDO.getPayOutTime() + OrderUtils.getZoneOff();
        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(payAppId);
        request.setMchid(payAppId);
        request.setDescription(OrderConst.PAY_DESCRIPTION);
        request.setOutTradeNo(payDO.getId().toString());
        request.setTimeExpire(timeExpire);
        request.setNotifyUrl(notifyUrl);
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(payDO.getPrice());
        request.setAmount(amount);
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(openId);
        request.setPayer(payer);
        return request;
    }

    @Override
    public OrderBillIdResponse cashPay(OrderBillCashPayRequest request) {
        Long billId = request.getBillId();
        OrderBillDO orderBill = orderBillRepositoryService.getById(billId);
        AssertUtils.isNotNull(orderBill,ErrorCode.ORDER_BILL_NOT_EXIST);
        Integer price = orderBill.getPriceReceivable() - orderBill.getPriceReceived();
        Integer priceRefund = orderBill.getPriceRefundable() - orderBill.getPriceReceived();
        AssertUtils.isFalse(price <= 0 && priceRefund <= 0,ErrorCode.ORDER_BILL_ALREADY_PAY_ERROR);

        OrderPayDO payDO = OrderBuilder.createOrderPayByCash(orderBill,request.getOperator());
        OrderBillDO cashPay = orderBill.createCashPay(request.getOperator());
        CashLogDO cashLog = OrderBuilder.createCashLogByBillCashPay(orderBill,request.getOperatorUser(),request.getCashSource());
        orderBillRepositoryService.cashPay(cashPay,payDO,cashLog);

        Long orderId = orderBill.getOrderId();
        SpringUtils.publishEvent(OrderChangelogEvent.build(this, OrderLogEvent.ORDER_MODIFY_BILL_CASH_APY,
                OrderChangeLogDTO.buildByModifyBillCashPayBefore(orderId,billId),
                OrderChangeLogDTO.buildByModifyBillCashPayAfter(orderId,billId),
                request.getOperatorUser()));
        return OrderBillIdResponse.build(billId);
    }

    @Override
    public Boolean payUpdateByQuery(OrderPayDO payDO) {
        WxPayOrderQueryResult queryInfo = wxPayComponent.queryOrder(payDO.getId());
        Boolean paySuccess = OrderUtils.isSuccess(queryInfo);
        if (paySuccess) {
            paySuccessHandler(payDO,queryInfo.getTransactionId(),queryInfo.getTimeEnd());
        }
        return Boolean.TRUE;
    }

    @SneakyThrows
    @Override
    public void payUpdateByNotifyWx(String data) {
        log.info("OrderBillServiceImpl[]payUpdateByNotifyWx start>> data:{}",data);
        WxPayOrderNotifyResult notifyInfo = wxPayComponent.notify(data);
        log.info("OrderBillServiceImpl[]payUpdateByNotifyWx notifyInfo:{}",JSONObject.toJSON(notifyInfo));

        String outTradeNo = notifyInfo.getOutTradeNo();
        String lockKey = buildPaySuccessKey(notifyInfo.getOutTradeNo());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (!lock.tryLock(30, TimeUnit.SECONDS)) {
                log.info("notifyWx lock lockKey:{}",lockKey);
                return;
            }
            OrderPayDO orderPayDO = orderPayRepositoryService.getById(Long.parseLong(outTradeNo));
            AssertUtils.isNotNull(orderPayDO,ErrorCode.ORDER_BILL_NOT_EXIST);

            paySuccessHandler(orderPayDO,notifyInfo.getOutTradeNo(),notifyInfo.getTimeEnd());
        } finally {
            if (lock.isLocked()
                    && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public OrderBillDetailResponse detail(OrderBillDetailRequest request) {
        OrderBillDO bill = null;
        if (Objects.nonNull(request.getBillId())) {
            bill = orderBillRepositoryService.getById(request.getBillId());
        } else {
            bill = orderBillRepositoryService.getNewBill(request.getOrderId());
        }
        //AssertUtils.isNotNull(bill,ErrorCode.ORDER_BILL_NOT_EXIST);
        Long orderId = Objects.nonNull(bill) ? bill.getOrderId() : request.getOrderId();
        OrderRequest orderRequest = OrderRequest.buildById(orderId);
        orderRequest.setOperatorUser(request.getOperatorUser());
        OrderDetailResponse orderDetail = orderService.getById(orderRequest);
        OrderBillDetailResponse result = new OrderBillDetailResponse();
        result.setBase(orderDetail.getBase());
        result.setTotalBill(orderDetail.getBill());
        if (Objects.nonNull(bill)) {
            OrderBillResponse curBill = OrderBuilder.toOrderBillResponse(bill);
            result.setCurBill(curBill);
        }
        return result;
    }

    @Override
    public OrderBillIdResponse wxRefund(OrderBillCashPayRequest request) {
        OrderPayDO orderPayDO = orderPayRepositoryService.getPaySuccess(request.getBillId());

        WxPayRefundRequest refundRequest = new WxPayRefundRequest();
        refundRequest.setOutTradeNo(orderPayDO.getId().toString());
        refundRequest.setTotalFee(orderPayDO.getPrice());
        refundRequest.setRefundFee(orderPayDO.getPrice());
        refundRequest.setOutRefundNo(orderPayDO.getOutTradeNo().toString());
        log.info("微信退款: refundRequest:{}",JSONObject.toJSONString(refundRequest));
        WxPayRefundResult res = wxPayComponent.refund(refundRequest);
        log.info("微信退款: res:{}",JSONObject.toJSONString(res));
        return OrderBillIdResponse.build(request.getBillId());
    }

    @Override
    public OrderBillStatisticsBResponse statisticsforB(OrderBillPageRequest request) {
        List<LocalDateTime> createTimeList = DateUtils.toLocalDateTimeList(request.getCreateTime());
        List<Integer> statusList = Arrays.asList(OrderBillType.CYCLE.getStatus(), OrderBillType.PREPAY.getStatus(),OrderBillType.SETTLE.getStatus());
        // 查询所有总账单的数据 应付款
        QueryWrapper<OrderBillDO> queryWrapper = new QueryWrapper();
        queryWrapper.select(STATISTICS_SQL)
                .lambda()
                .eq(OrderBillDO::getDeleted, DelUtils.NO_DELETED)
                .ne(OrderBillDO::getParentBillId, OrderConst.TOP_BILL)
                .in(OrderBillDO::getBillType, statusList)
                .eq(OrderBillDO::getPriceRefundable,0)
                .eq(Objects.nonNull(request.getOrderId()),OrderBillDO::getOrderId,request.getOrderId())
                .eq(Objects.nonNull(request.getTradeType()),OrderBillDO::getTradeType,request.getTradeType())
                .ge(Objects.nonNull(createTimeList.get(0)),OrderBillDO::getCreatedAt,createTimeList.get(0))
                .le(Objects.nonNull(createTimeList.get(1)),OrderBillDO::getCreatedAt,createTimeList.get(1))
                .gt(Objects.nonNull(request.getFilterRefundBill()) && Objects.equals(1,request.getFilterRefundBill()),
                        OrderBillDO::getPriceRefundable,0);
        OrderBillDO priceReceivableBill = orderBillRepositoryService.getOne(queryWrapper);
        Long priceReceivable = Objects.isNull(priceReceivableBill) ? 0L : priceReceivableBill.getId();

        QueryWrapper<OrderBillDO> queryWrapper2 = new QueryWrapper();
        queryWrapper2.select(STATISTICS_SQL)
                .lambda()
                .eq(OrderBillDO::getDeleted, DelUtils.NO_DELETED)
                .ne(OrderBillDO::getParentBillId, OrderConst.TOP_BILL)
                .in(OrderBillDO::getBillType, statusList)
                .eq(OrderBillDO::getPriceRefundable,0)
                .eq(OrderBillDO::getPriceReceived,0)
                .eq(Objects.nonNull(request.getOrderId()),OrderBillDO::getOrderId,request.getOrderId())
                .eq(Objects.nonNull(request.getTradeType()),OrderBillDO::getTradeType,request.getTradeType())
                .ge(Objects.nonNull(createTimeList.get(0)),OrderBillDO::getCreatedAt,createTimeList.get(0))
                .le(Objects.nonNull(createTimeList.get(1)),OrderBillDO::getCreatedAt,createTimeList.get(1))
                .gt(Objects.nonNull(request.getFilterRefundBill()) && Objects.equals(1,request.getFilterRefundBill()),
                        OrderBillDO::getPriceRefundable,0);
        OrderBillDO priceUnPayBill = orderBillRepositoryService.getOne(queryWrapper2);
        Long priceUnReceivable = Objects.isNull(priceUnPayBill) ? 0L : priceUnPayBill.getId();

        return OrderBillStatisticsBResponse.build(priceReceivable, priceUnReceivable);
    }

    /**
     * 支付成功唯一key
     * @param payId
     * @return
     */
    public String buildPaySuccessKey(String payId) {
        return OrderKey.PAY_WX_NOTIFY_PREFIX + payId;
    }

    /**
     * 支付成功处理
     * @param orderPayDO
     * @param wxOutTradeNo
     * @param payTime
     */
    public void paySuccessHandler(OrderPayDO orderPayDO,
                                  String wxOutTradeNo,
                                  String payTime) {
        // 更新支付状态
        OrderPayDO updateEntity = new OrderPayDO();
        updateEntity.setId(orderPayDO.getId());
        updateEntity.setOutTradeNo(wxOutTradeNo);
        updateEntity.setPayStatus(OrderPayStatus.SUCCESS.getStatus());
        updateEntity.setPayResult(OrderPayStatus.SUCCESS.getDescribe());
        updateEntity.setPayTime(payTime);

        OrderBillDO orderBillDO = orderBillRepositoryService.getById(orderPayDO.getBillId());
        OrderBillDO updateBillEntity = new OrderBillDO();
        updateBillEntity.setId(orderBillDO.getId());
        updateBillEntity.setPriceReceived(orderBillDO.getPriceReceived() + orderPayDO.getPrice());
        updateBillEntity.setTradeType(OrderTradeType.WECHAT.getStatus());

        orderBillRepositoryService.updateByWxNotify(updateEntity,updateBillEntity);
    }


    private LambdaQueryWrapper<OrderBillDO> queryWrapper() {
        LambdaQueryWrapper<OrderBillDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderBillDO::getDeleted,DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
