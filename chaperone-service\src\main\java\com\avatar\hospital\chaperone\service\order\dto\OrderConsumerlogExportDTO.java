package com.avatar.hospital.chaperone.service.order.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.avatar.hospital.chaperone.utils.ExportUtil;
import lombok.Data;

/**
 * @program: hospital-chaperone
 * @description: 订单消费记录导出
 * @author: sp0372
 * @create: 2023-10-24 10:29
 **/
@Data
@ExcelIgnoreUnannotated
public class OrderConsumerlogExportDTO {

    @ExcelProperty(value = "陪护单号")
    private Long orderId;
    @ExcelProperty(value = "病人姓名")
    private String patientName;

    @ExcelProperty(value = "服务名称")
    private String itemName;

    @ExcelProperty(value = "费用产生时间",converter = ExportUtil.Date.class)
    private Integer date;


    @ExcelProperty(value = "产生费用金额",converter = ExportUtil.Amount.class)
    private Integer totalPrice;

    @ExcelProperty(value = "物业管理金额",converter = ExportUtil.Amount.class)
    private Integer priceCertifiedProperty;

    @ExcelProperty(value = "物业管理分成比例",converter = ExportUtil.Rate.class)
    private Integer rateCertifiedProperty;

    @ExcelProperty(value = "护工总金额",converter = ExportUtil.Amount.class)
    private Integer priceNursing;

    @ExcelProperty(value = "护工总分成比例",converter = ExportUtil.Rate.class)
    private Integer rateNursing;

    @ExcelProperty(value = "护工分成")
    private String nursingOrder;

    @ExcelProperty(value = "院方金额",converter = ExportUtil.Amount.class)
    private Integer priceHospital;

    @ExcelProperty(value = "院方分成比例",converter = ExportUtil.Rate.class)
    private Integer rateHospital;




    @ExcelProperty(value = "费用明细状态",converter = ExportUtil.Version.class)
    private Long version;

    // 不在导出列表
    private Long itemId;
}
