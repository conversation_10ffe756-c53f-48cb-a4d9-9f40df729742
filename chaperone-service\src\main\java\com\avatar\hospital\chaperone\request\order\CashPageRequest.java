package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

/**
 * @program: hospital-chaperone
 * @description: c端列表
 * @author: sp0372
 * @create: 2023-10-11 16:50
 **/
@Data
public class CashPageRequest extends PageRequest implements OperatorReq {

    /**
     * 操作类型 1 增加 2 提取
     */
    private Integer type;

    /**
     * 金额类型 1 现金 3 支付宝
     * @see com.avatar.hospital.chaperone.database.order.enums.CashSource
     */
    private Integer cashSource;


    /**
     * 操作用户
     */
    private Operator operatorUser;
}
