package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单-支付记录;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_order_pay")
public class OrderPayDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 交易订单ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 订单ID
     */
      private Long orderId;

      /**
     * 账单ID
     */
      private Long billId;

      /**
     * 三方订单号(微信)
     */
      private String outTradeNo;

      /**
     * 交易类型，参考：trade_type
       * @see com.avatar.hospital.chaperone.database.order.enums.OrderTradeType
     */
      private Integer tradeType;

      /**
     * 金额，单位分
     */
      private Integer price;

      /**
     * 付款时间
     */
      private String payTime;

      /**
     * 订单超时时间
     */
      private String payOutTime;

      /**
     * 退款金额，单位分
     */
      private Integer refundAmount;

      /**
     * 支付备注
     */
      private String remark;

      /**
     * 交易状态，参考：pay_status
       * @see com.avatar.hospital.chaperone.database.order.enums.OrderPayStatus
     */
      private Integer payStatus;

      /**
     * 交易结果
     */
      private String payResult;

  /**
   * 支付信息，json字符串
   */
  private String payInfo;

}
