package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderChangelogPageRequest  extends  PageRequest implements OperatorReq {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
