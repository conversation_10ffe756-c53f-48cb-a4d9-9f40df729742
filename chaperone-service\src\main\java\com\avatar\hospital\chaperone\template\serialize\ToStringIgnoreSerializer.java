package com.avatar.hospital.chaperone.template.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * org 0 2 3 返回未null
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 16:36
 **/
public class ToStringIgnoreSerializer extends JsonSerializer<Long> {
    @Override
    public void serialize(Long val, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {

        if (Objects.isNull(val)) {
            jsonGenerator.writeNull();
        }else {
            if (Objects.equals(0L,val) || Objects.equals(2L,val)||Objects.equals(3L,val)) {
                jsonGenerator.writeNull();
            }else {
                jsonGenerator.writeString(val.toString());
            }
        }
    }
}
