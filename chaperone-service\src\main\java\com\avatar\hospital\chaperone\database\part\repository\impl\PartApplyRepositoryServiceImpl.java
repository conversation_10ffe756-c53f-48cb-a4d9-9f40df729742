package com.avatar.hospital.chaperone.database.part.repository.impl;

import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartDO;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel;
import com.avatar.hospital.chaperone.database.part.mapper.PartApplyMapper;
import com.avatar.hospital.chaperone.database.part.repository.PartApplyRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartBatchRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartRepositoryService;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 备件使用申请单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
@RequiredArgsConstructor
public class PartApplyRepositoryServiceImpl extends ServiceImpl<PartApplyMapper, PartApplyDO> implements PartApplyRepositoryService {

    private final PartBatchRepositoryService partBatchRepositoryService;
    private final PartRepositoryService partRepositoryService;
    private final RepairFormRepositoryService repairFormRepositoryService;

    @Override
    public Map<Long, List<PartApplyModel>> findMapByFeedbackIdList(List<Long> feedbackIdList) {
        if (CollUtils.isEmpty(feedbackIdList)) {
            return Maps.newHashMap();
        }
        List<PartApplyModel> list = baseMapper.findMapByFeedbackIdList(feedbackIdList);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<Long, List<PartApplyModel>> result = list.stream()
                .collect(Collectors.groupingBy(PartApplyModel::getFeedbackId));
        return result;
    }

    @Override
    public void pass(PartApplyDO updateApply, Long batchId, List<PartDO> partList) {
        AssertUtils.isTrue(updateById(updateApply), ErrorCode.UPDATE_ERROR);
        AssertUtils.isTrue(partBatchRepositoryService.updateBalance(batchId,partList.size()), ErrorCode.UPDATE_ERROR);
        AssertUtils.isTrue(partRepositoryService.updateBatchById(partList), ErrorCode.UPDATE_ERROR);
    }

    @Override
    public void refuse(PartApplyDO updateEntity, RepairFormDO newRepairForm) {
        AssertUtils.isTrue(updateById(updateEntity), ErrorCode.UPDATE_ERROR);
        if (Objects.nonNull(newRepairForm)) {
            AssertUtils.isTrue(repairFormRepositoryService.updateById(newRepairForm), ErrorCode.UPDATE_ERROR);
        }
    }
}
