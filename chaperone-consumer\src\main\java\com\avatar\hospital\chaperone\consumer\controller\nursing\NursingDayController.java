package com.avatar.hospital.chaperone.consumer.controller.nursing;

import com.avatar.hospital.chaperone.response.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.consumer.validator.nursing.NursingDayValidator;
import com.avatar.hospital.chaperone.request.nursing.NursingDayPagingRequest;
import com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse;
import com.avatar.hospital.chaperone.service.nursing.NursingDayService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author:sp0420
 * @Description:
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/nursing/day")
public class NursingDayController {
    private final NursingDayService nursingDayService;

    @GetMapping(value = "paging")
    public SingleResponse<PageResponse<NursingDayPagingResponse>> paging(NursingDayPagingRequest request) {
        return TemplateProcess.doProcess(log,()->{
            log.info("NursingDayController[]paging[]request:{}",request);
            NursingDayValidator.pagingValidate(request);
            request.setSortType(1);
            return nursingDayService.paging(request);
        });
    }
}
