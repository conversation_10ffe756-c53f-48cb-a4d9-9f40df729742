package com.avatar.hospital.chaperone.builder.nursing;

import com.alibaba.fastjson.JSONObject;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.request.nursing.NursingAddRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingPagingRequest;
import com.avatar.hospital.chaperone.request.nursing.NursingUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingDetailResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingPagingResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingSimpleResponse;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author:sp0420
 * @Description:
 */
public class NursingBuilder {

    public static NursingDO buildNursingDO(NursingAddRequest request) {
        if (request == null) {
            return null;
        }
        NursingDO nursingDO = new NursingDO();
        nursingDO.setName(request.getName());
        nursingDO.setGender(request.getGender());
        nursingDO.setAge(request.getAge());
        nursingDO.setWorkYear(request.getWorkYear());
        nursingDO.setSpecialty(request.getSpecialty());
        nursingDO.setRemark(request.getRemark());
        nursingDO.setSettleWay(request.getSettleWay());
        nursingDO.setSettleTime(request.getSettleTime());
        nursingDO.setSponsor(request.getSponsor());
        nursingDO.setSponsorPrice(request.getSponsorPrice());
        nursingDO.setIdCardPic(request.getIdCardPic());
        nursingDO.setCertificationPic(JSONObject.toJSONString(request.getCertificationPicList()));
        nursingDO.setStatus(request.getStatus());
        nursingDO.setCreateBy(request.getCreateBy());
        nursingDO.setUpdateBy(request.getUpdateBy());
        nursingDO.setDeleted(DelUtils.NO_DELETED);
        nursingDO.setMobile(request.getMobile());
        return nursingDO;
    }

    public static NursingDO buildNursingDO(NursingUpdateRequest request) {
        if (request == null) {
            return null;
        }
        NursingDO nursingDO = new NursingDO();
        nursingDO.setId(request.getId());
        nursingDO.setName(request.getName());
        nursingDO.setGender(request.getGender());
        nursingDO.setAge(request.getAge());
        nursingDO.setWorkYear(request.getWorkYear());
        nursingDO.setSpecialty(request.getSpecialty());
        nursingDO.setRemark(request.getRemark());
        nursingDO.setSettleWay(request.getSettleWay());
        nursingDO.setSettleTime(request.getSettleTime());
        nursingDO.setSponsor(request.getSponsor());
        nursingDO.setSponsorPrice(request.getSponsorPrice());
        nursingDO.setIdCardPic(request.getIdCardPic());
        if (CollectionUtils.isEmpty(request.getCertificationPicList())){
            nursingDO.setCertificationPic("");
        }else{
            nursingDO.setCertificationPic(JSONObject.toJSONString(request.getCertificationPicList()));
        }

        nursingDO.setStatus(request.getStatus());
        nursingDO.setUpdateBy(request.getUpdateBy());
        nursingDO.setNursingStar(request.getNursingStar());
        nursingDO.setMobile(request.getMobile());
        return nursingDO;
    }

    public static NursingDetailResponse buildNursingDetailResponse(NursingDO nursingDO) {
        if (nursingDO == null) {
            return null;
        }
        NursingDetailResponse nursingDetailResponse = new NursingDetailResponse();
        nursingDetailResponse.setId(nursingDO.getId());
        nursingDetailResponse.setAge(nursingDO.getAge());
        nursingDetailResponse.setName(nursingDO.getName());
        nursingDetailResponse.setGender(nursingDO.getGender());
        nursingDetailResponse.setWorkYear(nursingDO.getWorkYear());
        nursingDetailResponse.setSpecialty(nursingDO.getSpecialty());
        nursingDetailResponse.setRemark(nursingDO.getRemark());
        nursingDetailResponse.setSettleWay(nursingDO.getSettleWay());
        nursingDetailResponse.setSettleTime(nursingDO.getSettleTime());
        nursingDetailResponse.setSponsor(nursingDO.getSponsor());
        nursingDetailResponse.setSponsorPrice(nursingDO.getSponsorPrice());
        nursingDetailResponse.setIdCardPic(nursingDO.getIdCardPic());
        nursingDetailResponse.setCertificationPic(nursingDO.getCertificationPic());
        nursingDetailResponse.setStatus(nursingDO.getStatus());
        nursingDetailResponse.setMobile(nursingDO.getMobile());

        return nursingDetailResponse;
    }

    public static NursingDO buildNursingDO(NursingPagingRequest request) {
        if (request == null) {
            return null;
        }
        NursingDO nursingDO = new NursingDO();
        nursingDO.setName(request.getName());
        nursingDO.setStatus(request.getStatus());
        return nursingDO;
    }

    public static PageResponse<NursingPagingResponse> buildNursingPagingResponse(PageResponse<NursingDO> pageResponse) {
        if (pageResponse == null) {
            return null;
        }
        PageResponse<NursingPagingResponse> response = new PageResponse<>();
        response.setTotal(pageResponse.getTotal());
        response.setCurrent(pageResponse.getCurrent());
        response.setSize(pageResponse.getSize());
        response.setRecords(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(pageResponse.getRecords())) {
            response.setRecords(buildNursingPagingResponse(pageResponse.getRecords()));
        }
        return response;
    }

    public static List<NursingPagingResponse> buildNursingPagingResponse(List<NursingDO> nursingList) {
        if (CollectionUtils.isEmpty(nursingList)) {
            return Collections.emptyList();
        }
        List<NursingPagingResponse> rolePagingResponse = Lists.newLinkedList();
        for (NursingDO nursingDO : nursingList) {
            rolePagingResponse.add(buildNursingPagingResponse(nursingDO));
        }
        return rolePagingResponse;
    }

    public static NursingPagingResponse buildNursingPagingResponse(NursingDO nursingDO) {
        if (nursingDO == null) {
            return null;
        }
        NursingPagingResponse nursingPagingResponse = new NursingPagingResponse();
        nursingPagingResponse.setId(nursingDO.getId());
        nursingPagingResponse.setAge(nursingDO.getAge());
        nursingPagingResponse.setName(nursingDO.getName());
        nursingPagingResponse.setGender(nursingDO.getGender());
        nursingPagingResponse.setWorkYear(nursingDO.getWorkYear());
        nursingPagingResponse.setSpecialty(nursingDO.getSpecialty());
        nursingPagingResponse.setRemark(nursingDO.getRemark());
        nursingPagingResponse.setSettleWay(nursingDO.getSettleWay());
        nursingPagingResponse.setSettleTime(nursingDO.getSettleTime());
        nursingPagingResponse.setSponsor(nursingDO.getSponsor());
        nursingPagingResponse.setSponsorPrice(nursingDO.getSponsorPrice());
        nursingPagingResponse.setIdCardPic(nursingDO.getIdCardPic());
        nursingPagingResponse.setCertificationPic(nursingDO.getCertificationPic());
        nursingPagingResponse.setStatus(nursingDO.getStatus());
        nursingPagingResponse.setMobile(nursingDO.getMobile());

        return nursingPagingResponse;
    }

    public static NursingSimpleResponse toNursingSimpleResponse(NursingDO nursingDO) {
        NursingSimpleResponse nursingSimpleResponse = new NursingSimpleResponse();
        nursingSimpleResponse.setId(nursingDO.getId());
        nursingSimpleResponse.setName(nursingDO.getName());
        nursingSimpleResponse.setGender(nursingDO.getGender());
        nursingSimpleResponse.setAge(nursingDO.getAge());
        nursingSimpleResponse.setWorkYear(nursingDO.getWorkYear());
        nursingSimpleResponse.setStatus(nursingDO.getStatus());
        nursingSimpleResponse.setNursingStar(nursingDO.getNursingStar());
        nursingSimpleResponse.setMobile(nursingDO.getMobile());
        return nursingSimpleResponse;
    }
}
