package com.avatar.hospital.chaperone.response.baccount;

import com.avatar.hospital.chaperone.database.baccount.enums.MenuType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/10
 */
@Data
public class MenuPagingResponse implements Serializable {

    /**
     * 菜单权限ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 前端菜单英文名称
     */
    private String frontName;

    /**
     * 上级菜单 顶级节点默认为null
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 组件路径
     */
    private String componentUrl;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 菜单权限字符串
     */
    private String menuKey;

    /**
     * 菜单权限类型
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.MenuType
     */
    private Integer type;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.MenuStatus
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createBy;

    /**
     * 更新者ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

}
