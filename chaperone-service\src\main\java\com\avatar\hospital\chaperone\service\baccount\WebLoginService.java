package com.avatar.hospital.chaperone.service.baccount;

import com.avatar.hospital.chaperone.response.baccount.PhoneNumberPasswordWebLoginResponse;
import com.avatar.hospital.chaperone.request.baccount.PhoneNumberPasswordWebLoginRequest;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public interface WebLoginService {

    /**
     * B端手机号码/密码登录
     *
     * @param request -
     * @return -
     */
    PhoneNumberPasswordWebLoginResponse login(PhoneNumberPasswordWebLoginRequest request);

}
