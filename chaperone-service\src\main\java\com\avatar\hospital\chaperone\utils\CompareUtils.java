package com.avatar.hospital.chaperone.utils;

import com.avatar.hospital.chaperone.database.order.enums.OrderStatus;
import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.avatar.hospital.chaperone.service.order.dto.OrderChangeLogDTO;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description: 比较工具类
 * @author: sp0372
 * @create: 2023-10-12 11:51
 **/
public class CompareUtils {
    static {
        // 预加载
        // Register.get()
    }
    /**
     * 输出差异结果
     * @param before
     * @param after
     * @param clazz
     * @return
     * @param <T>
     */
    @SneakyThrows
    public final static <T> List<CompareResult> compare(T before, T after, Class<T> clazz) {
        List<CompareClassMeta> metaList = Register.get(clazz);
        List<CompareResult> result = new LinkedList<>();
        for (CompareClassMeta meta : metaList) {
            Field field = meta.getField();
            field.setAccessible(Boolean.TRUE);
            Object beforeVal = field.get(before);
            Object afterVal = field.get(after);
            if (!eqVale(beforeVal,afterVal) || meta.force) {
                // 枚举类型转换
                if (Objects.nonNull(meta.getEnumConvert())) {
                    beforeVal = meta.getEnumConvert().toDesc(beforeVal);
                    afterVal = meta.getEnumConvert().toDesc(afterVal);
                }
                result.add(CompareResult.build(meta.getColumn(), beforeVal, afterVal));
            }
        }
        return result;
    }

    /**
     * 比较结果变成文字
     * @param before
     * @param after
     * @param clazz
     * @return
     * @param <T> 0 更新前数据 1 更新后数据
     */
    public final static <T extends CompareDTO> String[] compareToStr(T before, T after, Class<T> clazz) {

        List<CompareResult> results = compare(before, after, clazz);
        if (results.isEmpty()) {
            return new String[]{"",""};
        }
        StringBuilder beforeSb = new StringBuilder();
        StringBuilder afterSb = new StringBuilder();
        for (CompareResult result : results) {
            if (!before.getEmpty()) {
                beforeSb.append(result.getColumn())
                        .append(":")
                        .append(result.getBefore())
                        .append(";\n");
            }
            afterSb.append(result.getColumn())
                    .append(":")
                    .append(result.getAfter())
                    .append(";\n");
        }
        return new String[]{beforeSb.toString(),afterSb.toString()};
    }



    public final static <T> Boolean eqVale(T b,T a) {
        if (b instanceof String) {
           String bb = (String) (Objects.isNull(b) ? "" : b);
           String aa = (String) (Objects.isNull(a) ? "" : a);
           return Objects.equals(bb,aa);
        }
        return Objects.equals(b,a);
    }


    // 内部类

    /**
     * 保存解析数据
     */
    public static class Register {
        private final static HashMap<Class,List<CompareClassMeta>> register = new HashMap<>();
        public final static List<CompareClassMeta> get(Class clazz) {
            List<CompareClassMeta> list = register.get(clazz);
            if (Objects.nonNull(list)) {
                return list;
            }
            synchronized (Register.class) {
                list = register.get(clazz);
                if (Objects.isNull(list)) {
                    list = parse(clazz);
                    register.put(clazz,list);
                }
            }
            return list;
        }

        /**
         * 解析类的字节码数据
         * @param clazz
         * @return
         */
        public final static List<CompareClassMeta> parse(Class clazz) {
            Field[] fields = clazz.getDeclaredFields();
            List<CompareClassMeta> list = Arrays.stream(fields)
                    .map(field -> CompareClassMeta.build(field))
                    .filter(meta -> Objects.nonNull(meta))
                    .collect(Collectors.toList());
            return list;
        }
    }

    /**
     * 枚举值
     */
    @Data
    public static class CompareClassMeta {
        private Field field;

        private String column;

        private IEnumConvert enumConvert;

        /**
         * 强制输出 2边内容
         */
        private Boolean force;

        @SneakyThrows
        public static final CompareClassMeta build(Field field) {
            String column = field.getName();
            IEnumConvert enumConvert = null;
            Boolean force = Boolean.FALSE;
            if (field.isAnnotationPresent(CompareAlias.class)) {
                CompareAlias annotation = field.getAnnotation(CompareAlias.class);
                if (annotation.ignore()) {
                    // 忽略对比该字段
                    return null;
                }
                String value = annotation.value();
                column = StringUtils.isBlank(value) ? column : value;
                // 转换
                Class<? extends IEnumConvert> enumClazz = annotation.clazz();
                if (!Objects.equals(enumClazz,IEnumConvert.class)) {
                    IEnumConvert[] enumConstants = enumClazz.getEnumConstants();
                    enumConvert = enumConstants[0];
                }
                force = annotation.force();
            }
            CompareClassMeta obj = new CompareClassMeta();
            obj.setField(field);
            obj.setColumn(column);
            obj.setEnumConvert(enumConvert);
            obj.setForce(force);
            return obj;
        }

    }

    /**
     * 比较结果
     */
    @Data
    public static class CompareResult {
        /**
         * 有差异的字段
         */
        private String column;
        /**
         * 修改前
         */
        private Object before;
        /**
         * 修改后
         */
        private Object after;

        public static final CompareResult build(String column,Object before,Object after) {
            CompareResult obj = new CompareResult();
            obj.setColumn(column);
            obj.setBefore(before);
            obj.setAfter(after);
            return obj;
        }

    }

    // 测试

    @Data
    public static class DataTest implements CompareDTO{
        @CompareAlias("姓名")
        private String name;

        @CompareAlias(value = "年龄" )
        private Integer age;

        @CompareAlias(value = "陪护单状态",clazz = OrderStatus.class)
        private Integer orderStatus;

        @Override
        public Boolean getEmpty() {
            return Boolean.FALSE;
        }
    }

    public static void main(String[] args) {
        DataTest before = new DataTest();
        before.setAge(10);
        before.setName("11");
        before.setOrderStatus(-1);

        DataTest after = new DataTest();
        after.setOrderStatus(20);

        List<CompareResult> compare = compare(before, after, DataTest.class);
        String[] str = compareToStr(before, after, DataTest.class);
        System.out.printf("");
    }
}
