package com.avatar.hospital.chaperone.enums;


import com.avatar.hospital.chaperone.template.exception.IErrorCode;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public enum ErrorCode implements IErrorCode {

    /**
     * 参数错误
     */
    PARAMETER_ERROR("parameter is error", "参数错误", "参数错误"),


    /**
     * 数据新增错误
     */
    INSERT_ERROR("insert error", "数据新增错误", "数据新增错误"),

    /**
     * 数据更新错误
     */
    UPDATE_ERROR("update error", "数据更新错误", "数据更新错误"),

    /**
     * 系统异常
     */
    SYSTEM_ERROR("system error", "系统异常", "网络异常,请稍后重试!"),

    /**
     * 导出数量超过限制
     */
    EXPORT_LIMIT_ERROR("export limit error", "导出数量超过最大数量(30000)限制,请过滤筛选条件,分段导出!", "导出数量超过最大数量(30000)限制,请过滤筛选条件,分段导出!"),

    /**
     * 操作他人数据 权限异常
     */
    DATA_AUTHORITY_ERROR("data authority error", "数据权限异常", "数据权限异常"),

    IDEMPOTENT_ERROR("idempotent error", "请勿重复提交", "请勿重复提交"),

    //================================================consumerAccount==================================================

    /**
     * 文件为空
     */
    FILE_EMPTY_ERROR("file empty error", "文件为空", "文件为空"),

    /**
     * 文件格式不正确
     */
    FILE_FORMAT_ERROR("file format error", "文件格式不正确", "文件格式不正确"),

    /**
     * 文件类型不允许上传
     */
    FILE_TYPE_NOT_ALLOW_UPLOAD_ERROR("file type not allow upload error", "文件类型不允许上传", "文件类型不允许上传"),

    /**
     * 文件上传失败
     */
    FILE_UPLOAD_ERROR("file upload error", "文件上传失败", "文件上传失败"),

    //================================================consumerAccount==================================================

    /**
     * 请登录
     */
    CONSUMER_ACCOUNT_NOT_LOGIN_ERROR("account not login error", "请登录", "请登录"),

    /**
     * 登录失败
     */
    CONSUMER_ACCOUNT_LOGIN_ERROR("account login error", "登录失败", "登录失败"),

    /**
     * 用户状态未启用
     */
    CONSUMER_ACCOUNT_STATUS_NOT_ENABLED("account status not enabled", "用户状态未启用", "用户状态未启用"),

    /**
     * 用户不存在
     */
    CONSUMER_ACCOUNT_NOT_EXIST("account not exist", "用户不存在", "用户不存在"),

    /**
     * 用户没有权限
     */
    CONSUMER_ACCOUNT_NOT_PERMISSION_ERROR("account not permission error", "您没有权限", "您没有权限"),

    /**
     * 用户性别错误
     */
    CONSUMER_ACCOUNT_SEX_ERROR("account sex error", "性别错误", "性别错误"),

    /**
     * 用户出生年月日填写错误
     */
    CONSUMER_ACCOUNT_BIRTHDAY_ERROR("account birthday error", "出生年月日错误", "出生年月日错误"),

    /**
     * 支付openId为绑定
     */
    CONSUMER_ACCOUNT_PAY_OPENID_ERROR("account account pay openId error", "支付openid未绑定", "支付openid未绑定"),


    //================================================webAccount=======================================================

    /**
     * 请登录
     */
    WEB_ACCOUNT_NOT_LOGIN_ERROR("account not login error", "请登录", "请登录"),

    /**
     * 密码长度不能少于8位
     */
    WEB_ACCOUNT_PASSWORD_LENGTH_ERROR("account password length error", "密码长度不能少于8位", "密码长度不能少于8位"),

    /**
     * 用户名或密码错误
     */
    WEB_ACCOUNT_USERNAME_OR_PASSWORD_ERROR("username or password error", "用户名或密码错误", "用户名或密码错误"),

    /**
     * 用户没有权限
     */
    WEB_ACCOUNT_NOT_PERMISSION_ERROR("account not permission error", "您没有权限", "您没有权限"),

    /**
     * 用户不存在
     */
    WEB_ACCOUNT_NOT_EXIST("account not exist", "用户不存在", "用户不存在"),

    /**
     * 用户手机号码已存在
     */
    WEB_ACCOUNT_PHONE_NUMBER_EXIST("account phone number exist", "手机号码已存在", "手机号码已存在"),

    /**
     * 用户状态未启用
     */
    WEB_ACCOUNT_STATUS_NOT_ENABLED("account status not enabled", "用户状态未启用", "用户状态未启用"),


    //================================================role=============================================================

    /**
     * 角色名称已存在
     */
    ROLE_NAME_EXIST("role name exist", "角色名称已存在", "角色名称已存在"),

    /**
     * 角色权限字符串已存在
     */
    ROLE_KEY_EXIST("role key exist", "角色权限字符串已存在", "角色权限字符串已存在"),

    /**
     * 角色不存在
     */
    ROLE_NOT_EXIST("role not exist", "角色不存在", "角色不存在"),


    //================================================menu=============================================================

    /**
     * 上级菜单权限不存在
     */
    MENU_PARENT_NOT_EXIST("menu parent not exist", "上级菜单权限不存在", "上级菜单权限不存在"),

    /**
     * 菜单权限不存在
     */
    MENU_NOT_EXIST("menu not exist", "菜单权限不存在", "菜单权限不存在"),

    /**
     * 菜单权限状态未启用
     */
    MENU_PARENT_STATUS_NOT_ENABLED("menu parent status not enabled", "菜单权限状态未启用", "菜单权限状态未启用"),


    //================================================organization=====================================================

    /**
     * 组织机构不存在
     */
    ORGANIZATION_NOT_EXIST("organization not exist", "组织机构不存在", "组织机构不存在"),

    /**
     * 上级组织机构不存在
     */
    ORGANIZATION_PARENT_NOT_EXIST("organization parent not exist", "上级组织机构不存在", "上级组织机构不存在"),

    /**
     * 上级组织机构状态未启用
     */
    ORGANIZATION_PARENT_STATUS_NOT_ENABLED("organization parent status not enabled", "上级组织机构状态未启用", "上级组织机构状态未启用"),

    /**
     * 医院不存在
     */
    HOSPITAL_NOT_EXIST("hospital not exist", "医院不存在", "医院不存在"),

    /**
     * 用户名或密码错误
     */
    USERNAME_OR_PASSWORD_ERROR("username or password error", "用户名或密码错误", "用户名或密码错误"),

    //================================================order=====================================================

    ORDER_NOT_EXIST("order not exist", "陪护单不存在", "陪护单不存在"),
    ORDER_STATUS_ERROR("order status error", "当前陪护单状态,不支持该操作", "当前陪护单状态,不支持该操作"),
    ORDER_ALREADY_CONFIRM_ERROR("order already confirm error", "陪护单已确认,无法取消", "陪护单已确认,无法取消"),
    ORDER_NOT_APPLY_SETTLE_ERROR("order not apply settle error", "陪护单不是结算提交状态,无法确认结算", "陪护单不是结算提交状态,无法确认结算"),

    ORDER_BILL_NOT_EXIST("order bill not exist", "账单不存在", "账单不存在"),
    ORDER_NOT_BIND_NURSING_ERROR("order not bind nursing", "陪护单未绑定护工", "陪护单未绑定护工"),
    ORDER_NOT_BIND_ITEM_ERROR("order not bind item", "陪护单未设置套餐", "陪护单未设置套餐"),
    ORDER_NOT_SET_BILL_ERROR("order set bill item", "陪护单未设置账单方式", "陪护单未设置账单方式"),

    ORDER_CASH_EXTRACT_PRICE_GT_0_ERROR("order cash extract price gt 0", "提取金额需要大于O", "提取金额需要大于O"),
    ORDER_BILL_TYPE_ERROR("order bill type error", "账单类型错误", "账单类型错误"),
    ORDER_BILL_SUB_TYPE_ERROR("order bill sub type error", "账单子类型错误", "账单子类型错误"),

    ORDER_BILL_PRICE_MIN_0_ERROR("order bill price min 0 error", "账单最小金额为0", "账单最小金额为0"),

    ORDER_RATE_ERROR("order rate error", "订单费率设置失错误,比例相加需要为100", "订单费率设置失错误,比例相加需要为100"),
    ORDER_BILL_ALREADY_PAY_ERROR("order bill already pay error", "账单已支付", "账单已支付"),
    ORDER_PAY_CREATE_ERROR("order pay create error", "支付单创建失败,请稍后重试!", "支付单创建失败,请稍后重试!"),
    ORDER_PAY_NOT_EXIST("order pay not exist", "支付单不存在", "支付单不存在"),

    ORDER_QUERY_ACCOUNT_BY_MOBILE_NOT_EXIST("order query account by mobile not exist", "根据手机号查询不到用户", "根据手机号查询不到用户"),

    ORDER_ITEM_MODIFY_ITEM_LIST_IS_NOT_NULL("order item modify item list is not null", "套餐列表不能为空", "套餐列表不能为空"),
    //================================================item=====================================================
    ITEM_NOT_EXIST("item not exist", "套餐不存在", "套餐不存在"),

    /**
     * 参数错误
     */
    ITEM_REMARK_LENGTH("item remark length", "套餐描述最大字符数量255", "套餐描述最大字符数量255"),


    //================================================nursing=====================================================
    /**
     * 医护信息不存在
     */
    NURSING_NOT_EXIST("nursing not exist", "医护信息不存在", "医护信息不存在"),
    CASH_EXTRACT_PRICE_GT_BALANCE("cash extract price gt balance", "提现金额大于现金余额", "提现金额大于现金余额"),


    //================================================nursingDay=====================================================
    /**
     * 排班不存在
     */
    NURSING_DAY_NOT_EXIST("nursing day not exist", "排班不存在", "排班不存在"),

    /**
     * 医护不处于空闲状态
     */
    NURSING_STATUS_NOT_FREE("nursing status not free", "医护不处于空闲状态", "医护不处于空闲状态"),

    /**
     * 医护不处于空闲状态
     */
    NURSING_STATUS_NOT_LEAVE("nursing status not leave", "医护不处于请假状态", "医护不处于请假状态"),

    /**
     * 医护不处于有效状态
     */
    NURSING_STATUS_NOT_NORMAL("nursing status not normal", "医护不处于有效状态", "医护不处于有效状态"),
    /**
     * 替班医护信息不存在
     */
    NURSING_REPLACE_NOT_EXIST("nursing replace not exist", "替班医护信息不存在", "替班医护信息不存在"),

    /**
     * 替班医护不处于空闲状态
     */
    NURSING_REPLACE_NOT_FREE("nursing replace not free", "替班医护不处于空闲状态", "替班医护不处于空闲状态"),

    /**
     * 替班医护请假中
     */
    NURSING_REPLACE_ON_LEAVE("nursing replace on leave", "替班医护请假中", "替班医护请假中"),

    /**
     * 替班医护陪护单不存在
     */
    NURSING_ORDER_REPLACE_NOT_EXIST("nursing order replace not exist", "替班医护陪护单不存在", "替班医护陪护单不存在"),

    //================================================nursingOrder=====================================================
    /**
     * 医护仍有排班进行中
     */
    NURSING_ORDER_IN_PROGRESS("nursing order in progress", "医护仍有排班进行中", "医护仍有排班进行中"),


    //================================================projectCode=====================================================
    PROJECT_CODE_NOT_CONFIG("project code not config", "编号未配置", "编号未配置"),

    //================================================part=====================================================
    PROJECT_PART_NOT_EXIST("project part not exist", "配件不存在", "配件不存在"),
    PROJECT_PART_APPLY_AUTH_STATUS_ERROR("project part apply auth status error", "备件申请单状态不是待审核", "备件申请单状态不是待审核"),
    PROJECT_PART_BATCH_STATUS_NOT_VALID("project part batch status not valid", "配件已入库无法编辑", "配件已入库无法编辑"),
    PROJECT_PART_STOCK_APPLY_NOT_EXIST("project part stock apply not exist", "配件入库申请单不存在", "配件入库申请单不存在"),

    PROJECT_PART_STOCK_APPLY_STATUS_NOT_VALID("project part stock apply status not valid", "配件入库申请单状态无效", "配件入库申请单状态无效"),
    PROJECT_PART_APPLY_NOT_EXIST("project part apply not exist", "备件出库申请单不存在", "备件出库申请单不存在"),
    PROJECT_PART_BATCH_NOT_EXIST("project part batch not exist", "部分配件不存在", "部分配件不存在"),
    PROJECT_PARAM_PART_BATCH_NOT_EXIST("project param part batch not exist", "配件批次参数必填", "配件批次参数必填"),
    PROJECT_PARAM_PART_NUM_LE("project param part num le", "备件数量不足", "备件数量不足"),

    PROJECT_DEVICE_NOT_EXIST("project device not exist", "设备不存在", "设备不存在"),

    //================================================plan=====================================================
    PROJECT_PLAN_NOT_EXIST("project plan not exist", "计划不存在", "计划不存在"),

    PROJECT_PLAN_TASK_NOT_EXIST("project plan task not exist", "计划任务不存在", "计划任务不存在"),


    //================================================报修单=====================================================
    REPAIR_FORM_NOT_EXIST("repair from not exist", "报修单不存在", "报修单不存在"),
    REPAIR_FORM_STATUS_NOT_SUPPORT_FEED("repair from status not support feed", "保修单状态不支持反馈", "保修单状态不支持反馈"),
    ;
    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String message;

    /**
     * 错误描述
     */
    private final String describe;


    ErrorCode(String code, String message, String describe) {
        this.code = code;
        this.message = message;
        this.describe = describe;
    }

    public static Map<String, String> toMap() {
        ErrorCode[] values = values();
        Map<String, String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (ErrorCode value : values) {
            map.put(value.getErrorCode(), value.getErrorMessage());
        }
        return map;
    }

    @Override
    public Object getErrorData() {
        return null;
    }

    @Override
    public String getErrorCode() {
        return code;
    }

    @Override
    public String getErrorMessage() {
        return message;
    }
}
