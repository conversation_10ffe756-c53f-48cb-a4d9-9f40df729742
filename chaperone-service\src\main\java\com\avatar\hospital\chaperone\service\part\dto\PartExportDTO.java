package com.avatar.hospital.chaperone.service.part.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.avatar.hospital.chaperone.utils.ExportUtil;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 13:15
 */
@Data
@ExcelIgnoreUnannotated
public class PartExportDTO {
    private static final long serialVersionUID = -5481496257106547961L;

    @ExcelProperty(value = "备品备件编号")
    private String code;

    @ExcelProperty(value = "批次编号")
    private String batchCode;

    @ExcelProperty(value = "备件名称")
    private String name;

    @ExcelProperty(value = "单价",converter = ExportUtil.Amount.class)
    private Integer price;

    @ExcelProperty(value = "备品备件状态",converter = ExportUtil.PartStatus.class)
    private Integer status;

    @ExcelProperty(value = "入库时间",converter = ExportUtil.LocalDateTimeStr.class)
    private LocalDateTime entryTime;

    @ExcelProperty(value = "关联单据编号")
    private Long stockApplyId;

    @ExcelProperty(value = "出资方类型",converter = ExportUtil.InvestorType.class)
    private Integer investorType;

    @ExcelProperty(value = "出资方名称")
    private String investor;
}
