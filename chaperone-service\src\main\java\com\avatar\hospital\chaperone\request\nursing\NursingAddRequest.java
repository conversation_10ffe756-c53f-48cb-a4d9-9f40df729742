package com.avatar.hospital.chaperone.request.nursing;

import com.avatar.hospital.chaperone.database.nursing.enums.Gender;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingAddRequest implements Serializable {

    /**
     * 所属院区
     */
    private List<Long> orgIds;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别 -1 未知 0 女 1 男
     *
     * @see Gender
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 服务年限
     */
    private Integer workYear;

    /**
     * 特长介绍
     */
    private String specialty;

    /**
     * 备注
     */
    private String remark;

    /**
     * 费用结算方式，1 按月结算 2 一次性结算
     *
     * @see com.avatar.hospital.chaperone.database.nursing.enums.NursingSettletWay
     */
    private Integer settleWay;

    /**
     * 费用结算时间（工作满x月后结算）
     */
    private String settleTime;

    /**
     * 介绍人
     */
    private String sponsor;

    /**
     * 介绍费金额,单位分
     */
    private Integer sponsorPrice;

    /**
     * 证件照
     */
    private String idCardPic;

    /**
     * 资质与证明
     */
    private List<String> certificationPicList;

    /**
     * 状态 0 审核中 1 生效 2 失效
     *
     * @see com.avatar.hospital.chaperone.database.nursing.enums.NursingStatus
     */
    private Integer status;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 手机号 非必填
     */
    private String mobile;
}
