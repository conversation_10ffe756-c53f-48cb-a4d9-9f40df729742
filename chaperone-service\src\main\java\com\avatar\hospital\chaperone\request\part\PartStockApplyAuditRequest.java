package com.avatar.hospital.chaperone.request.part;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 13:15
 */
@Data
public class PartStockApplyAuditRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = -719989091067374641L;
    /**
     * 主键ID
     */
    private Long id;


    /**
     * 状态（(1 - 通过 ， 2 - 驳回）
     *
     * @see com.avatar.hospital.chaperone.database.part.enums.PartStockApplyStatusType
     */
    private Integer status;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
