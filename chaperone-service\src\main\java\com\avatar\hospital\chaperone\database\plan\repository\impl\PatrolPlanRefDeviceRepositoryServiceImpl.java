package com.avatar.hospital.chaperone.database.plan.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanRefDeviceDO;
import com.avatar.hospital.chaperone.database.plan.mapper.PatrolPlanRefDeviceMapper;
import com.avatar.hospital.chaperone.database.plan.repository.PatrolPlanRefDeviceRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 巡检计划关联设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class PatrolPlanRefDeviceRepositoryServiceImpl extends ServiceImpl<PatrolPlanRefDeviceMapper, PatrolPlanRefDeviceDO>
        implements PatrolPlanRefDeviceRepositoryService {
    @Override
    public boolean update2delete(PlanRequest request) {

        LambdaUpdateWrapper<PatrolPlanRefDeviceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PatrolPlanRefDeviceDO::getPlanId, request.getId());
        updateWrapper.set(PatrolPlanRefDeviceDO::getUpdateBy, request.getOperator());
        updateWrapper.set(PatrolPlanRefDeviceDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.setSql(" deleted = id");
        update(updateWrapper);
        return true;
    }

    @Override
    public List<Long> getRefIds(Long planId) {

        LambdaQueryWrapper<PatrolPlanRefDeviceDO> queryWrapper = queryWrapper();
        queryWrapper.eq(PatrolPlanRefDeviceDO::getPlanId, planId);
        queryWrapper.eq(PatrolPlanRefDeviceDO::getDeleted, DeletedEnum.NO.getStatus());
        List<PatrolPlanRefDeviceDO> stockApplyRefPartBatchDOS = this.list(queryWrapper);
        return stockApplyRefPartBatchDOS.stream().map(o -> o.getDeviceId()).collect(Collectors.toList());
    }

    @Override
    public Set<Long> getRefIds(Set<Long> planIds) {
        LambdaQueryWrapper<PatrolPlanRefDeviceDO> queryWrapper = queryWrapper();
        queryWrapper.in(PatrolPlanRefDeviceDO::getPlanId, planIds);
        queryWrapper.eq(PatrolPlanRefDeviceDO::getDeleted, DeletedEnum.NO.getStatus());
        List<PatrolPlanRefDeviceDO> refOrgDOS = this.list(queryWrapper);
        return refOrgDOS.stream().map(o -> o.getDeviceId()).collect(Collectors.toSet());

    }

    private LambdaQueryWrapper<PatrolPlanRefDeviceDO> queryWrapper() {
        LambdaQueryWrapper<PatrolPlanRefDeviceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatrolPlanRefDeviceDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}
