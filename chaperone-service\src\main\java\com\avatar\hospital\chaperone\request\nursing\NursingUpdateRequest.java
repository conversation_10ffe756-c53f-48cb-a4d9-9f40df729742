package com.avatar.hospital.chaperone.request.nursing;

import com.avatar.hospital.chaperone.database.nursing.enums.Gender;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingUpdateRequest implements Serializable {

    /**
     * 组织机构ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 所属院区
     */
    private List<Long> orgIds;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别 -1 未知 0 女 1 男
     *
     * @see Gender
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 服务年限
     */
    private Integer workYear;

    /**
     * 特长介绍
     */
    private String specialty;

    /**
     * 备注
     */
    private String remark;

    /**
     * 费用结算方式，1 按月结算 2 一次性结算
     *
     * @see com.avatar.hospital.chaperone.database.nursing.enums.NursingSettletWay
     */
    private Integer settleWay;

    /**
     * 费用结算时间（工作满x月后结算）
     */
    private String settleTime;

    /**
     * 介绍人
     */
    private String sponsor;

    /**
     * 介绍费金额,单位分
     */
    private Integer sponsorPrice;

    /**
     * 证件照
     */
    private String idCardPic;

    /**
     * 资质与证明
     */
    private List<String> certificationPicList;

    /**
     * 状态 0 审核中 1 生效 2 失效
     *
     * @see com.avatar.hospital.chaperone.database.nursing.enums.NursingStatus
     */
    private Integer status;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 护工星级 1 ~ 5 星
     */
    private Integer nursingStar;

    /**
     * 手机号 非必填
     */
    private String mobile;
}
