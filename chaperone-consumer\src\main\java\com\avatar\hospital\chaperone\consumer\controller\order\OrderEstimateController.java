package com.avatar.hospital.chaperone.consumer.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.consumer.validator.order.OrderValidator;
import com.avatar.hospital.chaperone.database.order.enums.OrderEstimateSource;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.OrderEstimateService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * C端陪护单-评价
 * @program: hospital-chaperone
 * @description: 订单|C端
 * @author: sp0372
 * @create: 2023-10-13 15:04
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/order/estimate")
public class OrderEstimateController {
    private final OrderEstimateService orderEstimateService;

    /**
     * 订单评价
     */
    @PostMapping("create")
    public SingleResponse<OrderEstimateIdResponse> create(@Validated @RequestBody OrderEstimateSaveRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.createEstimateValidator(request);
            return orderEstimateService.create(request);
        });
    }

    /**
     * 订单评价查询
     */
    @PostMapping("get")
    public SingleResponse<List<OrderEstimateResponse>> get(@Validated @RequestBody OrderEstimateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            request.setSource(OrderEstimateSource.USER.getStatus());
            return orderEstimateService.get(request);
        });
    }
}
