package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.database.order.enums.OrderBillSubType;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillType;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillFindAllRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 账单类型:参考，bill_type
     * @see OrderBillType
     */
    private Integer billType;

    public static final OrderBillFindAllRequest build(Long orderId) {
        OrderBillFindAllRequest obj = new OrderBillFindAllRequest();
        obj.setOrderId(orderId);
        return obj;
    }
    public static final OrderBillFindAllRequest build(Long orderId,Integer billType) {
        OrderBillFindAllRequest obj = new OrderBillFindAllRequest();
        obj.setOrderId(orderId);
        obj.setBillType(billType);
        return obj;
    }

}
