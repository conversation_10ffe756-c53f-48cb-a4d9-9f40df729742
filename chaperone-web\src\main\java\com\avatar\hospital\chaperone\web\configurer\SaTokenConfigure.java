package com.avatar.hospital.chaperone.web.configurer;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaHttpMethod;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.router.SaRouterStaff;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.session.SaSessionCustomUtil;
import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import com.avatar.hospital.chaperone.constant.StpConstant;
import com.avatar.hospital.chaperone.web.component.WebAccountPermissionComponent;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
@Slf4j
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SaInterceptor(handler -> {
            // 匿名可访问
            SaRouter.match("/**")
                    .notMatch("/api/v1/web/login")
                    .check(r -> StpUtil.checkLogin());

            // 需要权限可访问
            // SaRouter.match(SaHttpMethod.GET, SaHttpMethod.OPTIONS).match("/api/v1/web/role/paging").check(r -> StpUtil.checkPermission("order.manage.designateNursing"));

        })).addPathPatterns("/**");
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/v1/web/**")
                //.allowedOrigins("*") //浏览器允许所有的域访问 / 注意 * 不能满足带有cookie的访问,Origin 必须是全匹配
                .allowedMethods("*")
                .allowedOriginPatterns("*")
                .allowCredentials(true)
                .allowedHeaders("*")
                .maxAge(3600);
        log.info("loading WebMvcConfigurer");
    }

    @Bean
    public StpInterfaceImpl stpInterfaceImpl(WebAccountPermissionComponent webAccountPermissionComponent) {
        return new SaTokenConfigure.StpInterfaceImpl(webAccountPermissionComponent);
    }

    @RequiredArgsConstructor
    public static class StpInterfaceImpl implements StpInterface {

        private final WebAccountPermissionComponent webAccountPermissionComponent;

        @Override
        public List<String> getPermissionList(Object loginId, String loginType) {
            List<String> permissionList = Lists.newLinkedList();
            for (String roleKey : getRoleList(loginId, loginType)) {
                SaSession roleSession = SaSessionCustomUtil.getSessionById(StpConstant.ROLE_PERMISSION_KEY_PREFIX + roleKey);
                List<String> authorityList = roleSession.get(StpConstant.PERMISSION_LIST, () -> webAccountPermissionComponent.getAccountPermissionList(roleKey));
                permissionList.addAll(authorityList);
            }
            return permissionList;
        }

        @Override
        public List<String> getRoleList(Object loginId, String loginType) {
            SaSession session = StpUtil.getSessionByLoginId(loginId);
            return session.get(StpConstant.ROLE_LIST, () -> webAccountPermissionComponent.getAccountRoleList(Long.valueOf(loginId.toString())));
        }
    }

}
