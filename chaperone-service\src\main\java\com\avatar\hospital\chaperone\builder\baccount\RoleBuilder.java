package com.avatar.hospital.chaperone.builder.baccount;

import com.avatar.hospital.chaperone.database.baccount.dataobject.MenuDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.request.baccount.RoleAddRequest;
import com.avatar.hospital.chaperone.request.baccount.RolePagingRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleMenuResponse;
import com.avatar.hospital.chaperone.response.baccount.RolePagingResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/11
 */
public class RoleBuilder {

    public static RoleDO buildRoleDO(RoleAddRequest request) {
        if (request == null) {
            return null;
        }
        RoleDO roleDO = new RoleDO();
        roleDO.setName(request.getName());
        roleDO.setRoleKey(request.getRoleKey());
        roleDO.setStatus(request.getStatus());
        roleDO.setSort(request.getSort());
        roleDO.setRemark(request.getRemark());
        roleDO.setCreateBy(request.getCreateBy());
        roleDO.setUpdateBy(request.getUpdateBy());
        return roleDO;
    }

    public static RoleDO buildRoleDO(RoleUpdateRequest request) {
        if (request == null) {
            return null;
        }
        RoleDO roleDO = new RoleDO();
        roleDO.setId(request.getId());
        roleDO.setName(request.getName());
        roleDO.setRoleKey(request.getRoleKey());
        roleDO.setStatus(request.getStatus());
        roleDO.setSort(request.getSort());
        roleDO.setRemark(request.getRemark());
        roleDO.setUpdateBy(request.getUpdateBy());
        return roleDO;
    }

    public static RoleDO buildRoleDO(RolePagingRequest request) {
        if (request == null) {
            return null;
        }
        RoleDO roleDO = new RoleDO();
        roleDO.setName(request.getName());
        return roleDO;
    }

    public static PageResponse<RolePagingResponse> buildRolePagingResponse(PageResponse<RoleDO> pageResponse) {
        if (pageResponse == null) {
            return null;
        }
        PageResponse<RolePagingResponse> response = new PageResponse<>();
        response.setTotal(pageResponse.getTotal());
        response.setCurrent(pageResponse.getCurrent());
        response.setSize(pageResponse.getSize());
        response.setRecords(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(pageResponse.getRecords())) {
            response.setRecords(buildRolePagingResponse(pageResponse.getRecords()));
        }
        return response;
    }

    public static List<RolePagingResponse> buildRolePagingResponse(List<RoleDO> roleList) {
        if (CollectionUtils.isEmpty(roleList)) {
            return Collections.emptyList();
        }
        List<RolePagingResponse> rolePagingResponse = Lists.newLinkedList();
        for (RoleDO roleDO : roleList) {
            rolePagingResponse.add(buildRolePagingResponse(roleDO));
        }
        return rolePagingResponse;
    }

    public static RolePagingResponse buildRolePagingResponse(RoleDO roleDO) {
        if (roleDO == null) {
            return null;
        }
        RolePagingResponse rolePagingResponse = new RolePagingResponse();
        rolePagingResponse.setId(roleDO.getId());
        rolePagingResponse.setName(roleDO.getName());
        rolePagingResponse.setRoleKey(roleDO.getRoleKey());
        rolePagingResponse.setStatus(roleDO.getStatus());
        rolePagingResponse.setSort(roleDO.getSort());
        rolePagingResponse.setRemark(roleDO.getRemark());
        rolePagingResponse.setCreateBy(roleDO.getCreateBy());
        rolePagingResponse.setUpdateBy(roleDO.getUpdateBy());
        rolePagingResponse.setCreatedAt(roleDO.getCreatedAt());
        rolePagingResponse.setUpdatedAt(roleDO.getUpdatedAt());
        return rolePagingResponse;
    }

    public static RoleDetailResponse buildRoleDetailResponse(RoleDO roleDO) {
        if (roleDO == null) {
            return null;
        }
        RoleDetailResponse roleDetailResponse = new RoleDetailResponse();
        roleDetailResponse.setId(roleDO.getId());
        roleDetailResponse.setName(roleDO.getName());
        roleDetailResponse.setRoleKey(roleDO.getRoleKey());
        roleDetailResponse.setStatus(roleDO.getStatus());
        roleDetailResponse.setSort(roleDO.getSort());
        roleDetailResponse.setRemark(roleDO.getRemark());
        roleDetailResponse.setCreateBy(roleDO.getCreateBy());
        roleDetailResponse.setUpdateBy(roleDO.getUpdateBy());
        roleDetailResponse.setCreatedAt(roleDO.getCreatedAt());
        roleDetailResponse.setUpdatedAt(roleDO.getUpdatedAt());
        return roleDetailResponse;
    }

    public static List<RoleMenuResponse.MenuData> buildRoleMenuResponse(List<MenuDO> menuDOList) {
        if (CollectionUtils.isEmpty(menuDOList)) {
            return Collections.emptyList();
        }
        List<RoleMenuResponse.MenuData> menuDataList = Lists.newArrayListWithCapacity(menuDOList.size());
        menuDOList.forEach(menuDO -> menuDataList.add(buildRoleMenuResponse(menuDO)));
        return menuDataList;
    }

    public static RoleMenuResponse.MenuData buildRoleMenuResponse(MenuDO menuDO) {
        if (menuDO == null) {
            return null;
        }
        RoleMenuResponse.MenuData menuData = new RoleMenuResponse.MenuData();
        menuData.setId(menuDO.getId());
        menuData.setName(menuDO.getName());
        menuData.setFrontName(menuDO.getFrontName());
        menuData.setParentId(menuDO.getParentId());
        menuData.setMenuKey(menuDO.getMenuKey());
        menuData.setType(menuDO.getType());
        menuData.setStatus(menuDO.getStatus());
        menuData.setSort(menuDO.getSort());
        menuData.setRemark(menuDO.getRemark());
        return menuData;
    }
}
