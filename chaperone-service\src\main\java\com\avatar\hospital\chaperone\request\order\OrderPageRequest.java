package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

/**
 * @program: hospital-chaperone
 * @description: c端列表
 * @author: sp0372
 * @create: 2023-10-11 16:50
 **/
@Data
public class OrderPageRequest extends PageRequest implements OperatorReq {

    /**
     * 陪护单ID
     */
    private Long id;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 操作用户
     */
    private Operator operatorUser;

}
