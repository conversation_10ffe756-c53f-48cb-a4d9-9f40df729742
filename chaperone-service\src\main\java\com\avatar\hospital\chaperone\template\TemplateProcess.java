package com.avatar.hospital.chaperone.template;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.template.exception.BaseException;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.exception.IErrorCode;
import org.slf4j.Logger;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public class TemplateProcess {

    public static final String PARAMETER_ERROR = "parameter is error";

    public static final String SERVER_ERROR = "server error";

    public static <Res> SingleResponse<Res> doProcess(Logger log, Supplier<Res> supplier) {
        try {
            return SingleResponse.of(supplier.get());
        } catch (BaseException exception) {
            log.warn("{}[]TemplateProcess[] doProcess baseException errorCode:{},errorMessage:{},{}", getLogClassSimpleName(log), exception.getErrorCode(), exception.getErrorMessage(), getStackTraceAsString(exception));
            return buildFailResponse(exception);
        } catch (IllegalArgumentException exception) {
            log.warn("{}[]TemplateProcess[] doProcess illegalArgumentException error:{}", getLogClassSimpleName(log), exception.getMessage());
            return SingleResponse.buildFailure(PARAMETER_ERROR, exception.getMessage());
        } catch (Throwable exception) {
            log.error("{}[]TemplateProcess[] doProcess error:{}", getLogClassSimpleName(log), getStackTraceAsString(exception));
            return SingleResponse.buildFailure(SERVER_ERROR, exception.getMessage());
        }
    }

    public static <Res> SingleResponse<Res> doProcess(Logger log, Supplier<Res> supplier, Runnable runnable) {
        try {
            return doProcess(log, supplier);
        } finally {
            runnable.run();
        }
    }

    public static <Res> SingleResponse<Res> doProcess(Logger log, Supplier<Res> supplier, Supplier<IErrorCode> iErrorCodeSupplier) {
        try {
            return SingleResponse.of(supplier.get());
        } catch (BaseException exception) {
            log.warn("{}[]TemplateProcess[] doProcess baseException errorCode:{},errorMessage:{}", getLogClassSimpleName(log), exception.getErrorCode(), exception.getErrorMessage());
            return buildFailResponse(exception);
        } catch (IllegalArgumentException exception) {
            log.warn("{}[]TemplateProcess[] doProcess illegalArgumentException error:{}", getLogClassSimpleName(log), exception.getMessage());
            return SingleResponse.buildFailure(PARAMETER_ERROR, exception.getMessage());
        } catch (Throwable exception) {
            log.error("{}[]TemplateProcess[] doProcess error:{}", getLogClassSimpleName(log), getStackTraceAsString(exception));
            if (Objects.nonNull(iErrorCodeSupplier)) {
                return buildFailResponse(BusinessException.buildBusinessException(iErrorCodeSupplier.get()));
            }
            return SingleResponse.buildFailure(SERVER_ERROR, exception.getMessage());
        }
    }

    public static <Res> SingleResponse<Res> doProcess(Logger log, Supplier<Res> supplier, Runnable runnable, Supplier<IErrorCode> iErrorCodeSupplier) {
        try {
            return doProcess(log, supplier, iErrorCodeSupplier);
        } finally {
            runnable.run();
        }
    }

    public static <Res> SingleResponse<Res> doProcess(Logger log, String methodName, Supplier<Res> supplier) {
        try {
            return SingleResponse.of(supplier.get());
        } catch (BaseException exception) {
            log.warn("{}[]TemplateProcess[] doProcess baseException methodName:{},errorCode:{},errorMessage:{}", getLogClassSimpleName(log), methodName, exception.getErrorCode(), exception.getErrorMessage());
            return buildFailResponse(exception);
        } catch (IllegalArgumentException exception) {
            log.warn("{}[]TemplateProcess[] doProcess illegalArgumentException methodName:{},error:{}", getLogClassSimpleName(log), methodName, exception.getMessage());
            return SingleResponse.buildFailure(PARAMETER_ERROR, exception.getMessage());
        } catch (Throwable exception) {
            log.error("{}[]TemplateProcess[] doProcess methodName:{},error:{}", getLogClassSimpleName(log), methodName, getStackTraceAsString(exception));
            return SingleResponse.buildFailure(SERVER_ERROR, exception.getMessage());
        }
    }

    public static <Res> SingleResponse<Res> doProcess(Logger log, String methodName, Supplier<Res> supplier, Runnable runnable) {
        try {
            return doProcess(log, methodName, supplier);
        } finally {
            runnable.run();
        }
    }

    private static SingleResponse buildFailResponse(BaseException baseException) {
        SingleResponse failSingleResponse = new SingleResponse();
        if (null != baseException.getErrorCode()) {
            failSingleResponse.setErrCode(baseException.getErrorCode());
        }
        if (null != baseException.getErrorMessage()) {
            failSingleResponse.setErrMessage(baseException.getErrorMessage());
        }
        if (null != baseException.getErrorData()) {
            failSingleResponse.setData(baseException.getErrorData());
        }
        return failSingleResponse;
    }

    private static String getStackTraceAsString(Throwable throwable) {
        StringWriter stringWriter = new StringWriter();
        throwable.printStackTrace(new PrintWriter(stringWriter));
        return stringWriter.toString();
    }

    private static String getLogClassSimpleName(Logger log) {
        String classSimpleName = log.getName();
        if (null == classSimpleName) {
            return null;
        }
        return classSimpleName.subSequence(classSimpleName.lastIndexOf(".") + 1, classSimpleName.length()).toString();
    }

}
