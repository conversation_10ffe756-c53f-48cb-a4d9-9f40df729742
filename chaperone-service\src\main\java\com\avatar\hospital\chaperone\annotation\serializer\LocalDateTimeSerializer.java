package com.avatar.hospital.chaperone.annotation.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @author: spx-8055
 * @date: 2020/12/25
 */
public class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {
    private static final ZoneId zoneId = ZoneId.systemDefault();
    private static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final Boolean enableTimestamp = true;

    @Override
    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
        } else {
            ZonedDateTime zdt = value.atZone(zoneId);
            if (enableTimestamp) {
                long time = Date.from(zdt.toInstant()).getTime();
                gen.writeObject(time);
            } else {
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_PATTERN);
                gen.writeObject(zdt.format(dateTimeFormatter));

            }
        }
    }
}
