package com.avatar.hospital.chaperone.service.item.impl;

import com.avatar.hospital.chaperone.builder.item.ItemBuilder;
import com.avatar.hospital.chaperone.database.item.dataobject.ItemDO;
import com.avatar.hospital.chaperone.database.item.dataobject.ItemOrgDO;
import com.avatar.hospital.chaperone.database.item.repository.ItemOrgRepositoryService;
import com.avatar.hospital.chaperone.database.item.repository.ItemRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.item.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.item.ItemCResponse;
import com.avatar.hospital.chaperone.response.item.ItemIdResponse;
import com.avatar.hospital.chaperone.response.item.ItemResponse;
import com.avatar.hospital.chaperone.service.item.ItemService;
import com.avatar.hospital.chaperone.service.item.consts.ItemConst;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description: 套餐数据
 * @author: sp0372
 * @create: 2023-10-12 15:51
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ItemServiceImpl implements ItemService {
    private final ItemRepositoryService itemRepositoryService;
    private final ItemOrgRepositoryService itemOrgRepositoryService;
    @Override
    public PageResponse<ItemCResponse> pagingToC(ItemCPageRequest request) {
        Long total = itemRepositoryService.countByC(request.getOrgId(),request.getServerType());
        if (total <= 0) {
            return PageResponse.build(total,request.getPageIndexLong(),request.getPageSizeLong(),Collections.emptyList(),ItemCResponse::buildByItemDO);
        }
        List<ItemDO> list = itemRepositoryService.listByC(request.getOrgId(),request.getServerType(),request.offset(),request.getPageSize());
        return PageResponse.build(total,request.getPageIndexLong(),request.getPageSizeLong(),list,ItemCResponse::buildByItemDO);
    }

    @Override
    public ItemCResponse getById(ItemRequest request) {
        ItemDO itemDO = itemRepositoryService.getById(request.getItemId());
        AssertUtils.isNotNull(itemDO, ErrorCode.ITEM_NOT_EXIST);

        LambdaQueryWrapper<ItemOrgDO> itemOrgQueryWrapper = new LambdaQueryWrapper();
        itemOrgQueryWrapper.eq(ItemOrgDO::getItemId,itemDO.getId());
        List<ItemOrgDO> itemOrgDOList = itemOrgRepositoryService.list(itemOrgQueryWrapper);
        return ItemCResponse.build(itemDO,itemOrgDOList);
    }

    @Override
    public ItemIdResponse create(ItemCreateRequest request) {
        long itemId = itemRepositoryService.create(ItemBuilder.buildItemDOByCreate(request),
                ItemBuilder.buildItemOrgDOByCreate(request));
        return ItemIdResponse.buildById(itemId);
    }

    @Override
    public ItemIdResponse modify(ItemModifyRequest request) {
        Long itemId = request.getId();
        ItemDO itemDO = itemRepositoryService.getById(itemId);
        AssertUtils.isNotNull(itemDO,ErrorCode.ITEM_NOT_EXIST);

        LambdaQueryWrapper<ItemOrgDO> orgQueryWrapper = new LambdaQueryWrapper();
        orgQueryWrapper.eq(ItemOrgDO::getItemId,itemId);
        orgQueryWrapper.eq(ItemOrgDO::getDeleted, ItemConst.NO_DELETED);
        List<ItemOrgDO> itemOrgList = itemOrgRepositoryService.list(orgQueryWrapper);
        itemRepositoryService.modify(ItemBuilder.buildItemDOByModify(request),
                ItemBuilder.buildItemOrgDOByModify(itemOrgList,request));
        return ItemIdResponse.buildById(itemId);
    }

    @Override
    public ItemResponse detail(ItemRequest request) {
        ItemDO itemDO = itemRepositoryService.getById(request.getItemId());
        AssertUtils.isNotNull(itemDO, ErrorCode.ITEM_NOT_EXIST);

        LambdaQueryWrapper<ItemOrgDO> itemOrgQueryWrapper = new LambdaQueryWrapper();
        itemOrgQueryWrapper.eq(ItemOrgDO::getItemId,itemDO.getId());
        itemOrgQueryWrapper.eq(ItemOrgDO::getDeleted,DelUtils.NO_DELETED);
        List<ItemOrgDO> itemOrgDOList = itemOrgRepositoryService.list(itemOrgQueryWrapper);
        return ItemResponse.build(itemDO,itemOrgDOList);
    }

    @Override
    public PageResponse<ItemResponse> paging(ItemPageRequest request) {
        Page<ItemDO> page = request.ofPage();
        LambdaQueryWrapper<ItemDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(ItemDO::getId);
        queryWrapper.eq(Objects.nonNull(request.getId()),ItemDO::getId,request.getId());
        queryWrapper.eq(Objects.nonNull(request.getDisplay()),ItemDO::getDisplay,request.getDisplay());
        queryWrapper.likeLeft(Objects.nonNull(request.getName()),ItemDO::getName,request.getName());
        page = itemRepositoryService.page(page, queryWrapper);
        return PageResponse.build(page,ItemResponse::buildByItemDO);
    }

    @Override
    public ItemResponse on(ItemRequest request) {
        Long itemId = request.getItemId();
        ItemDO itemDO = itemRepositoryService.getById(itemId);
        AssertUtils.isNotNull(itemDO, ErrorCode.ITEM_NOT_EXIST);

        ItemDO updateEntity = ItemBuilder.buildItemDOByNo(request);
        itemRepositoryService.updateById(updateEntity);

        return ItemResponse.buildById(itemId);
    }

    @Override
    public ItemResponse off(ItemRequest request) {
        Long itemId = request.getItemId();

        ItemDO itemDO = itemRepositoryService.getById(itemId);
        AssertUtils.isNotNull(itemDO, ErrorCode.ITEM_NOT_EXIST);

        ItemDO updateEntity = ItemBuilder.buildItemDOByOff(request);
        itemRepositoryService.updateById(updateEntity);

        return ItemResponse.buildById(itemId);
    }

    @Override
    public ItemResponse onDisplay(ItemRequest request) {
        Long itemId = request.getItemId();
        ItemDO itemDO = itemRepositoryService.getById(itemId);
        AssertUtils.isNotNull(itemDO, ErrorCode.ITEM_NOT_EXIST);

        ItemDO updateEntity = ItemBuilder.buildItemDOByNoDisplay(request);
        itemRepositoryService.updateById(updateEntity);

        return ItemResponse.buildById(itemId);
    }

    @Override
    public ItemResponse offDisplay(ItemRequest request) {
        Long itemId = request.getItemId();

        ItemDO itemDO = itemRepositoryService.getById(itemId);
        AssertUtils.isNotNull(itemDO, ErrorCode.ITEM_NOT_EXIST);

        ItemDO updateEntity = ItemBuilder.buildItemDOByOffDisplay(request);
        itemRepositoryService.updateById(updateEntity);

        return ItemResponse.buildById(itemId);
    }

    @Override
    public List<ItemResponse> listByIds(List<Long> itemIdList) {
        if (CollectionUtils.isEmpty(itemIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ItemDO> queryWrapper = queryWrapper();
        queryWrapper.in(ItemDO::getId,itemIdList);
        List<ItemDO> list = itemRepositoryService.list(queryWrapper);
        return list.stream()
                .map(ItemBuilder::buidItemResponseByItemDO)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, String> mapByIds(List<Long> itemIdList) {
        if (CollectionUtils.isEmpty(itemIdList)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<ItemDO> queryWrapper = queryWrapper();
        queryWrapper.select(ItemDO::getId,ItemDO::getName);
        queryWrapper.in(ItemDO::getId,itemIdList);
        List<ItemDO> list = itemRepositoryService.list(queryWrapper);
        return list.stream()
                .collect(Collectors.toMap(ItemDO::getId,ItemDO::getName));
    }

    @Override
    public List<ItemResponse> findAll(Integer hour) {
        LambdaQueryWrapper<ItemDO> queryWrapper = queryWrapper();
        queryWrapper.eq(ItemDO::getChargingTime,hour);
        List<ItemDO> list = itemRepositoryService.list(queryWrapper);
        return list.stream()
                .map(ItemBuilder::buidItemResponseByItemDO)
                .collect(Collectors.toList());
    }

    private LambdaQueryWrapper<ItemDO> queryWrapper() {
        LambdaQueryWrapper<ItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
