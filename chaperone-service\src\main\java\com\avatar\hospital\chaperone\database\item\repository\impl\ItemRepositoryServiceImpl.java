package com.avatar.hospital.chaperone.database.item.repository.impl;

import com.avatar.hospital.chaperone.database.item.dataobject.ItemDO;
import com.avatar.hospital.chaperone.database.item.dataobject.ItemOrgDO;
import com.avatar.hospital.chaperone.database.item.mapper.ItemMapper;
import com.avatar.hospital.chaperone.database.item.repository.ItemOrgRepositoryService;
import com.avatar.hospital.chaperone.database.item.repository.ItemRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 套餐表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
@RequiredArgsConstructor
public class ItemRepositoryServiceImpl extends ServiceImpl<ItemMapper, ItemDO> implements ItemRepositoryService {
    private final ItemOrgRepositoryService itemOrgRepositoryService;
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long create(ItemDO itemDO, List<ItemOrgDO> itemOrgDOList) {
        long itemId = IdUtils.getId();
        itemDO.setId(itemId);
        itemOrgDOList.forEach(itemOrg -> itemOrg.setItemId(itemId));

        AssertUtils.isTrue(save(itemDO), ErrorCode.INSERT_ERROR);
        if (Objects.nonNull(itemOrgDOList)) {
            AssertUtils.isTrue(itemOrgRepositoryService.saveBatch(itemOrgDOList), ErrorCode.INSERT_ERROR);
        }
        return itemId;
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long modify(ItemDO itemDO, List<ItemOrgDO> itemOrgDOList) {
        AssertUtils.isTrue(updateById(itemDO), ErrorCode.UPDATE_ERROR);
        if (Objects.nonNull(itemOrgDOList)) {
            AssertUtils.isTrue(itemOrgRepositoryService.saveOrUpdateBatch(itemOrgDOList), ErrorCode.UPDATE_ERROR);
        }
        return itemDO.getId();
    }

    @Override
    public Long countByC(Long orgId, Integer serverType) {
        return baseMapper.countByC(orgId,serverType);
    }

    @Override
    public List<ItemDO> listByC(Long orgId, Integer serverType, Integer offset, Integer pageSize) {
        return baseMapper.listByC(orgId,serverType,offset,pageSize);
    }
}
