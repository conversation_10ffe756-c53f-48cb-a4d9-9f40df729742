package com.avatar.hospital.chaperone.service.repair;


import com.avatar.hospital.chaperone.request.repair.*;
import com.avatar.hospital.chaperone.response.repair.*;
import com.avatar.hospital.chaperone.response.PageResponse;

import java.util.List;
import java.util.Map;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:29
 **/
public interface RepairFormFeedbackService {
    // B 端

    /**
     * 历史反馈结果查询分页查询
     */
    PageResponse<RepairFormFeedbackResponse> paging(RepairFormFeedbackPageRequest request);

    // C端

    /**
     * 历史反馈结果查询
     */
    PageResponse<RepairFormFeedbackResponse> pagingForC(RepairFormFeedbackPageRequest request);

    /**
     * 反馈结果
     *    * 需要备件申请单
     */
    RepairFormFeedbackIdResponse create(RepairFormFeedbackCreateRequest request);

    // 内部

    /**
     * 获取最新的一条
     * @param formIdList
     * @return
     */
    Map<Long, RepairFormFeedbackResponse> getLastRepairFormFeedback(List<Long> formIdList);
}
