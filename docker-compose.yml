version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: hospital-chaperone-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root123456}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-hospital_chaperone}
      MYSQL_USER: ${MYSQL_USER:-chaperone}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-chaperone123}
      TZ: Asia/Shanghai
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./document/sql:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf.d:/etc/mysql/conf.d
    networks:
      - chaperone-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: hospital-chaperone-redis
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    networks:
      - chaperone-network
    command: redis-server /etc/redis/redis.conf

  # 后台管理系统
  chaperone-web:
    build:
      context: .
      dockerfile: Dockerfile.web
    container_name: hospital-chaperone-web
    restart: unless-stopped
    environment:
      ENV: ${ENV:-prod}
      DB_MYSQL_URL: mysql
      DB_REDIS_URL: redis
      DB_REDIS_PORT: 6379
      TZ: Asia/Shanghai
    ports:
      - "${WEB_PORT:-8081}:8081"
    volumes:
      - web_logs:/app/logs
      - ./docker/web/config:/app/config
    networks:
      - chaperone-network
    depends_on:
      - mysql
      - redis
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 客户端系统
  chaperone-consumer:
    build:
      context: .
      dockerfile: Dockerfile.consumer
    container_name: hospital-chaperone-consumer
    restart: unless-stopped
    environment:
      ENV: ${ENV:-prod}
      DB_MYSQL_URL: mysql
      DB_REDIS_URL: redis
      DB_REDIS_PORT: 6379
      TZ: Asia/Shanghai
    ports:
      - "${CONSUMER_PORT:-8080}:8080"
    volumes:
      - consumer_logs:/app/logs
      - ./docker/consumer/config:/app/config
    networks:
      - chaperone-network
    depends_on:
      - mysql
      - redis
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: hospital-chaperone-nginx
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - chaperone-network
    depends_on:
      - chaperone-web
      - chaperone-consumer

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  web_logs:
    driver: local
  consumer_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  chaperone-network:
    driver: bridge
