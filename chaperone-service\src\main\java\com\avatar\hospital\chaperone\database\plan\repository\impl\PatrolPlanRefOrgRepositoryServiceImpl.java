package com.avatar.hospital.chaperone.database.plan.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanRefOrgDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanRefOrgDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.mapper.PatrolPlanRefOrgMapper;
import com.avatar.hospital.chaperone.database.plan.repository.PatrolPlanRefOrgRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 巡检计划关联部门 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class PatrolPlanRefOrgRepositoryServiceImpl extends ServiceImpl<PatrolPlanRefOrgMapper, PatrolPlanRefOrgDO> implements PatrolPlanRefOrgRepositoryService {
    @Override
    public boolean update2delete(PlanRequest request) {

        LambdaUpdateWrapper<PatrolPlanRefOrgDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PatrolPlanRefOrgDO::getPlanId, request.getId());
        updateWrapper.set(PatrolPlanRefOrgDO::getUpdateBy, request.getOperator());
        updateWrapper.set(PatrolPlanRefOrgDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.setSql(" deleted = id");
        update(updateWrapper);
        return true;
    }

    @Override
    public List<Long> getRefIds(Long planId) {

        LambdaQueryWrapper<PatrolPlanRefOrgDO> queryWrapper = queryWrapper();
        queryWrapper.eq(PatrolPlanRefOrgDO::getPlanId, planId);
        queryWrapper.eq(PatrolPlanRefOrgDO::getDeleted, DeletedEnum.NO.getStatus());
        List<PatrolPlanRefOrgDO> refOrgDOS = this.list(queryWrapper);
        return refOrgDOS.stream().map(o -> o.getOrgId()).collect(Collectors.toList());
    }

    @Override
    public Set<Long> getRefIds(Set<Long> planIds) {
        LambdaQueryWrapper<PatrolPlanRefOrgDO> queryWrapper = queryWrapper();
        queryWrapper.in(PatrolPlanRefOrgDO::getPlanId, planIds);
        queryWrapper.eq(PatrolPlanRefOrgDO::getDeleted, DeletedEnum.NO.getStatus());
        List<PatrolPlanRefOrgDO> refOrgDOS = this.list(queryWrapper);
        return refOrgDOS.stream().map(o -> o.getOrgId()).collect(Collectors.toSet());

    }

    @Override
    public Set<Long> getPlanByOrg(Set<Long> orgIds) {
        LambdaQueryWrapper<PatrolPlanRefOrgDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanRefOrgDO::getDeleted, DeletedEnum.NO.getStatus());
        queryWrapper.orderByDesc(PlanRefOrgDO::getId);
        queryWrapper.in(PlanRefOrgDO::getOrgId, orgIds);

        List<PatrolPlanRefOrgDO> refOrgDOS = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(refOrgDOS)) {
            return Sets.newHashSet();
        }
        return refOrgDOS.stream().map(PlanRefOrgDO::getPlanId).collect(Collectors.toSet());
    }

    private LambdaQueryWrapper<PatrolPlanRefOrgDO> queryWrapper() {
        LambdaQueryWrapper<PatrolPlanRefOrgDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PatrolPlanRefOrgDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}
