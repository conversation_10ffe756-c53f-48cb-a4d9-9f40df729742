package com.avatar.hospital.chaperone.database.order.mapper;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderEstimateDO;
import com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsNursingModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单-评价; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderEstimateMapper extends BaseMapper<OrderEstimateDO> {

    List<StatisticsNursingModel> statisticsNursing(@Param("start") LocalDateTime start,
                                                   @Param("end") LocalDateTime end);
}
