package com.avatar.hospital.chaperone.consumer.validator.nursing;

import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.nursing.NursingDayPagingRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;

/**
 * @author:sp0420
 * @Description:
 */
public class NursingDayValidator {
    public static void pagingValidate(NursingDayPagingRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageIndex(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageSize(), ErrorCode.PARAMETER_ERROR);
    }
}
