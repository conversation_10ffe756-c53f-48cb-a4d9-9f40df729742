package com.avatar.hospital.chaperone.database.plan.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/26 19:55
 */
@Getter
public enum CircleType {
    NONE(-1, "未知"),
    DAY(1, "天"),
    MONTH(2, "月"),
    ;

    private final Integer code;

    private final String describe;


    CircleType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static CircleType of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (CircleType type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
