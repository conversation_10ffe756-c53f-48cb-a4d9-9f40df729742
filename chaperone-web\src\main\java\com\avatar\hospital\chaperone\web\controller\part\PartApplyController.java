package com.avatar.hospital.chaperone.web.controller.part;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.request.part.PartApplyPageRequest;
import com.avatar.hospital.chaperone.request.part.PartApplyPassRequest;
import com.avatar.hospital.chaperone.request.part.PartApplyRefuseRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartApplyIdResponse;
import com.avatar.hospital.chaperone.response.part.PartApplyResponse;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.service.part.PartApplyService;
import com.avatar.hospital.chaperone.service.part.consts.PartKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * B端保修单-备件审核
 *
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 13:41
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/part-apply")
public class PartApplyController {
    private final PartApplyService applyService;

    /**
     * 查询
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<PartApplyResponse>> paging(@RequestBody PartApplyPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return applyService.paging(request);
        });
    }

    /**
     * 审核通过
     * @param request
     * @return
     */
    @Idempotent(value = PartKey.PART_PASS + " + #request.id")
    @PostMapping("pass")
    public SingleResponse<PartApplyIdResponse> pass(@RequestBody PartApplyPassRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return applyService.pass(request);
        });
    }

    /**
     * 审核拒绝
     * @param request
     * @return
     */
    @Idempotent(value = PartKey.PART_REJECT + " + #request.id")
    @PostMapping("reject")
    public SingleResponse<PartApplyIdResponse> refuse(@RequestBody PartApplyRefuseRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return applyService.refuse(request);
        });
    }

}
