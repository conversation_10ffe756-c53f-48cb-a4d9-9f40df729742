package com.avatar.hospital.chaperone.service.order.impl;

import com.avatar.hospital.chaperone.builder.order.OrderBuilder;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderBillDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderInvoiceDO;
import com.avatar.hospital.chaperone.database.order.repository.OrderBillRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderInvoiceRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.order.OrderInvoiceCreateRequest;
import com.avatar.hospital.chaperone.request.order.OrderInvoicePageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderInvoiceIdResponse;
import com.avatar.hospital.chaperone.response.order.OrderInvoiceResponse;
import com.avatar.hospital.chaperone.service.order.OrderInvoiceService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-18 19:11
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderInvoiceServiceImpl implements OrderInvoiceService {
    private final OrderInvoiceRepositoryService orderInvoiceRepositoryService;
    private final OrderBillRepositoryService orderBillRepositoryService;
    @Override
    public OrderInvoiceIdResponse create(OrderInvoiceCreateRequest request) {
        OrderBillDO bill = orderBillRepositoryService.getById(request.getBillId());
        AssertUtils.isNotNull(bill,ErrorCode.ORDER_BILL_NOT_EXIST);

        OrderInvoiceDO invoice = OrderBuilder.createOrderInvoiceByCreate(request);
        invoice.setOrderId(bill.getOrderId());
        Long id = orderInvoiceRepositoryService.add(invoice);
        AssertUtils.isNotNull(id, ErrorCode.INSERT_ERROR);
        return OrderInvoiceIdResponse.build(id);
    }

    @Override
    public PageResponse<OrderInvoiceResponse> paging(OrderInvoicePageRequest request) {
        Page<OrderInvoiceDO> page = request.ofPage();
        LambdaQueryWrapper<OrderInvoiceDO> queryWrapper = queryWrapper();
        queryWrapper.orderByDesc(OrderInvoiceDO::getId);
        page = orderInvoiceRepositoryService.page(page,queryWrapper);
        return PageResponse.build(page,OrderBuilder::toOrderInvoiceResponse);
    }

    @Override
    public OrderInvoiceResponse getByBillId(Long billId) {
        LambdaQueryWrapper<OrderInvoiceDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderInvoiceDO::getBillId,billId);
        OrderInvoiceDO orderInvoiceDO = orderInvoiceRepositoryService.getOne(queryWrapper);
        return OrderBuilder.toOrderInvoiceResponse(orderInvoiceDO);
    }

    @Override
    public Map<Long, OrderInvoiceResponse> getByBillIdList(List<Long> billId) {
        if (CollUtils.isEmpty(billId)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<OrderInvoiceDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrderInvoiceDO::getBillId,billId);
        List<OrderInvoiceDO> orderInvoiceDOList = orderInvoiceRepositoryService.list(queryWrapper);
        if(CollUtils.isEmpty(orderInvoiceDOList)){
            return new HashMap<>();
        }
        Map<Long, OrderInvoiceResponse> result = orderInvoiceDOList.stream()
                .map(invoice -> OrderBuilder.toOrderInvoiceResponse(invoice))
                .collect(Collectors.toMap(OrderInvoiceResponse::getBillId, Function.identity(), (v1, v2) -> v1.getId() > v2.getId() ? v1 : v2));
        return result;
    }

    private LambdaQueryWrapper<OrderInvoiceDO> queryWrapper() {
        LambdaQueryWrapper<OrderInvoiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderInvoiceDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
