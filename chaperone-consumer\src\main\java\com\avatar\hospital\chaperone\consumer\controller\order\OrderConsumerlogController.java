package com.avatar.hospital.chaperone.consumer.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.consumer.validator.order.OrderValidator;
import com.avatar.hospital.chaperone.request.order.OrderConsumerlogCPageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderConsumerLogCResponse;
import com.avatar.hospital.chaperone.service.order.OrderConsumerlogService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * C端陪护单-消费明细
 * @program: hospital-chaperone
 * @description: 订单|C端
 * @author: sp0372
 * @create: 2023-10-13 15:04
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/order/consumerlog")
public class OrderConsumerlogController {
    private final OrderConsumerlogService orderConsumerlogService;

    /**
     * 查询
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<OrderConsumerLogCResponse>> paging(@RequestBody OrderConsumerlogCPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrderValidator.pagingForConsumerlog(request);
            return orderConsumerlogService.pagingForC(request);
        });
    }
}
