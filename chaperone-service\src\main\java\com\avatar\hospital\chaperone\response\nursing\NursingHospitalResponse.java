package com.avatar.hospital.chaperone.response.nursing;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingHospitalResponse implements Serializable {
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 护工id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long nursingId;
}
