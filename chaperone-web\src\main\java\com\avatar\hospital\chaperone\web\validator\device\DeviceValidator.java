package com.avatar.hospital.chaperone.web.validator.device;

import com.avatar.hospital.chaperone.database.device.enums.DeviceStatus;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingStatus;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.device.DeviceAddRequest;
import com.avatar.hospital.chaperone.request.device.DevicePagingRequest;
import com.avatar.hospital.chaperone.request.device.DeviceUpdateRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;

import java.util.Objects;

/**
 * @author:sp0420
 * @Description:
 */
public class DeviceValidator {
    private static final Integer DEFAULT_STATUS = DeviceStatus.ENABLE.getStatus();

    public static void addValidate(DeviceAddRequest request) {
        if (Objects.isNull(request.getStatus())){
            request.setStatus(DEFAULT_STATUS);
        }
        // 设置创建人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setCreateBy(accountId);
    }

    public static void updateValidate(DeviceUpdateRequest request) {
        if (Objects.isNull(request.getStatus())){
            request.setStatus(DEFAULT_STATUS);
        }
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void pagingValidate(DevicePagingRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageIndex(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageSize(), ErrorCode.PARAMETER_ERROR);
    }
}
