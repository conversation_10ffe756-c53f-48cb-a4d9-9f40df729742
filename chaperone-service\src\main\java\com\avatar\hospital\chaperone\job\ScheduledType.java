package com.avatar.hospital.chaperone.job;

import lombok.Getter;

/**
 * Description:
 *  订单日志事件
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum ScheduledType {
    PATROL_PLAN_TASK_DAY("PATROL_PLAN_TASK_DAY", "巡检计划任务创建(每天执行一次)","scheduled:patrol_plan_task_day"),
    MAINTENANCE_PLAN_TASK_DAY("MAINTENANCE_PLAN_TASK_DAY", "巡检计划任务创建(每天执行一次)","scheduled:maintenance_plan_task_day"),
    CONSUMER_LOG_BY_EVERY_HOUR("CONSUMER_LOG_BY_EVERY_HOUR", "消费记录创建(每个小时执行一次)","scheduled:consumer_log_by_every_hour"),
    SETTLE_BILL_BY_EVERY_HOUR("SETTLE_BILL_BY_EVERY_HOUR", "结算账单创建(每个小时执行一次)","scheduled:settle_bill_by_every_hour"),
    EXPIRE_CANCEL_BY_EVERY_HOUR("EXPIRE_CANCEL_BY_EVERY_HOUR", "过期自动取消(每个小时执行一次)","scheduled:expire_cancel_by_every_hour"),
    ORDER_BILL_BY_EVERY_MONTH("ORDER_BILL_BY_EVERY_MONTH", "周期账单(每个月1号凌晨执行一次)","scheduled:order_bill_by_every_month"),
    ORDER_BILL_BY_EVERY_SEASON("ORDER_BILL_BY_EVERY_SEASON", "周期账单(每个季度开始1号凌晨执行一次)","scheduled:order_bill_by_every_season"),
    ORDER_BILL_BY_EVERY_YEAR("ORDER_BILL_BY_EVERY_YEAR", "周期账单(每年1月1号凌晨执行一次)","scheduled:order_bill_by_every_year"),
    ORDER_NURSING_BY_EVERY_MONTH("ORDER_NURSING_BY_EVERY_MONTH", "订单护工排班(每年1月27号凌晨执行一次)","scheduled:order_nursing_by_every_month"),
    ORDER_PAY_BY_EVERY_MINUTES("ORDER_PAY_BY_EVERY_MINUTES", "支付单支付情况,每分钟30秒执行一次","scheduled:order_pay_by_every_minutes"),
    ORDER_PAY_MESSAGE_BY_EVERY_DAY("ORDER_PAY_MESSAGE_BY_EVERY_DAY", "订单逾期提醒每天下午15点提醒一次","scheduled:order_pay_message_by_every_day"),
    ORDER_PAY_USER_SYNC_EVERY_DAY("ORDER_PAY_USER_SYNC_EVERY_DAY", "微信公众号用户同步每天3,13点同步一次","scheduled:order_pay_user_sync_by_every_day"),
    ;

    private final String code;

    private final String describe;


    private final String lockKey;


    ScheduledType(String code, String describe,String lockKey) {
        this.code = code;
        this.describe = describe;
        this.lockKey = lockKey;
    }

    @Override
    public String toString() {
        return describe + "("+code+")"
                + ",lockKey:" + lockKey;
    }
}
