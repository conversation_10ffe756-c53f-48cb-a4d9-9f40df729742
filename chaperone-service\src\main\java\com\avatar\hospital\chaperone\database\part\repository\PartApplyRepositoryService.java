package com.avatar.hospital.chaperone.database.part.repository;

import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartDO;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 备件使用申请单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface PartApplyRepositoryService extends IService<PartApplyDO> {

    Map<Long, List<PartApplyModel>> findMapByFeedbackIdList(List<Long> feedbackIdList);

    /**
     * 更新备件
     * @param updateApply
     * @param batchId
     * @param partList
     */
    void pass(PartApplyDO updateApply, Long batchId, List<PartDO> partList);

    /**
     * 拒绝
     * @param updateEntity
     * @param newRepairForm
     */
    void refuse(PartApplyDO updateEntity, RepairFormDO newRepairForm);
}
