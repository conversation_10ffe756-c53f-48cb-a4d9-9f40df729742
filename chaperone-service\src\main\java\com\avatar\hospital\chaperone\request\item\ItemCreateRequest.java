package com.avatar.hospital.chaperone.request.item;

import com.avatar.hospital.chaperone.database.item.enums.ItemDisplay;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 15:47
 **/
@Data
public class ItemCreateRequest implements OperatorReq,Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 套餐类型：参考，server_type
     * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
     */
    @NotNull
    private Integer serverType;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 服务价格(单位：分/天)
     */
    @Min(value = 0)
    private Integer price;

    /**
     * 护工星级 1 ~ 5 星
     */
    @Size(min = 1,max = 5)
    private Integer nursingStar;

    /**
     * 详细描述
     */
    private String remark;

    /**
     * 封面图
     */
    private String coverPicture;

    /**
     * 物业管理费比例
     */
    private Integer rateCertifiedProperty;

    /**
     * 院方管理费比例
     */
    private Integer rateHospital;

    /**
     * 护工费比例
     */
    private Integer rateNursing;

    /**
     * 机构ID
     */
    private List<Long> orgIdList;

    /**
     * 时间节点 0 ~ 23
     */
    @Size(min = 0,max = 24)
    private Integer chargingTime;

    /**
     * 操作用户
     */
    private Operator operatorUser;


    /**
     * 是否外显示 1 是 0 不是
     * @see ItemDisplay
     */
    private Integer display;
}
