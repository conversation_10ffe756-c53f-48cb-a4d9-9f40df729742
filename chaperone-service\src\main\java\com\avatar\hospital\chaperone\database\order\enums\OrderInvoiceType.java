package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import lombok.Getter;

import java.util.Objects;

/**
 * Description:
 *  付费凭证类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderInvoiceType implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    // 1 电子收据 2 电子发票 3 实体收据
    E_RECEIPT(1, "电子收据"),
    E_INVOICE(2, "电子发票"),
    RECEIPT(3, "实体收据"),
    ;

    private final Integer status;

    private final String describe;


    OrderInvoiceType(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderInvoiceType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderInvoiceType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        OrderInvoiceType e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }
}
