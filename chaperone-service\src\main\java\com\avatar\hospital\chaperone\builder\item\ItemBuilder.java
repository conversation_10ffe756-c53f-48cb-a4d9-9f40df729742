package com.avatar.hospital.chaperone.builder.item;

import com.avatar.hospital.chaperone.database.item.dataobject.ItemDO;
import com.avatar.hospital.chaperone.database.item.dataobject.ItemOrgDO;
import com.avatar.hospital.chaperone.database.item.enums.ItemDisplay;
import com.avatar.hospital.chaperone.database.item.enums.ItemStatus;
import com.avatar.hospital.chaperone.request.item.ItemCreateRequest;
import com.avatar.hospital.chaperone.request.item.ItemModifyRequest;
import com.avatar.hospital.chaperone.request.item.ItemRequest;
import com.avatar.hospital.chaperone.response.item.ItemResponse;
import com.avatar.hospital.chaperone.service.item.consts.ItemConst;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.avatar.hospital.chaperone.utils.ObjUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-13 10:44
 **/
public class ItemBuilder {
    public static ItemDO buildItemDOByCreate(ItemCreateRequest request) {
        if (request == null) {
            return null;
        }
        ItemDO itemDO = new ItemDO();
        itemDO.setServerType(request.getServerType());
        itemDO.setName(request.getName());
        itemDO.setPrice(request.getPrice());
        // 刚开始创建是下架
        itemDO.setStatus(ItemStatus.OFF.getStatus());
        itemDO.setNursingStar(request.getNursingStar());
        itemDO.setRemark(request.getRemark());
        itemDO.setCoverPicture(request.getCoverPicture());
        itemDO.setRateCertifiedProperty(request.getRateCertifiedProperty());
        itemDO.setRateHospital(request.getRateHospital());
        itemDO.setRateNursing(request.getRateNursing());
        itemDO.setChargingTime(request.getChargingTime());
        itemDO.setCreateBy(request.getOperator());
        itemDO.setUpdateBy(request.getOperator());
        itemDO.setDeleted(ItemConst.NO_DELETED);
        itemDO.setDisplay(ItemDisplay.NO.getStatus());
        if (Objects.nonNull(request.getDisplay())) {
            itemDO.setDisplay(request.getDisplay());
        }
        return itemDO;
    }

    public static List<ItemOrgDO> buildItemOrgDOByCreate(ItemCreateRequest request) {
        if (request == null || Objects.isNull(request.getOrgIdList())) {
            return null;
        }
        List<ItemOrgDO> list = request.getOrgIdList().stream()
                .map(orgId -> buildItemOrgByOrgId(orgId,request.getOperator()))
                .collect(Collectors.toList());
        return list;
    }

    public static ItemOrgDO buildItemOrgByOrgId(Long orgId, Long operator) {
        ItemOrgDO itemOrgDO = new ItemOrgDO();
        itemOrgDO.setOrgId(orgId);
        itemOrgDO.setCreateBy(operator);
        itemOrgDO.setUpdateBy(operator);
        itemOrgDO.setDeleted(ItemConst.NO_DELETED);
        return itemOrgDO;
    }

    public static ItemOrgDO buildItemOrgByOrgId(Long itemId,Long orgId, Long operator) {
        ItemOrgDO itemOrgDO = new ItemOrgDO();
        itemOrgDO.setItemId(itemId);
        itemOrgDO.setOrgId(orgId);
        itemOrgDO.setCreateBy(operator);
        itemOrgDO.setUpdateBy(operator);
        itemOrgDO.setDeleted(ItemConst.NO_DELETED);
        return itemOrgDO;
    }

    public static ItemDO buildItemDOByOff(ItemRequest request) {
        ItemDO itemDO = new ItemDO();
        itemDO.setId(request.getItemId());
        itemDO.setStatus(ItemStatus.OFF.getStatus());
        itemDO.setUpdateBy(request.getOperator());
        return itemDO;
    }

    public static ItemDO buildItemDOByOffDisplay(ItemRequest request) {
        ItemDO itemDO = new ItemDO();
        itemDO.setId(request.getItemId());
        itemDO.setDisplay(ItemDisplay.NO.getStatus());
        itemDO.setUpdateBy(request.getOperator());
        return itemDO;
    }

    public static ItemDO buildItemDOByNo(ItemRequest request) {
        ItemDO itemDO = new ItemDO();
        itemDO.setId(request.getItemId());
        itemDO.setStatus(ItemStatus.NO.getStatus());
        itemDO.setUpdateBy(request.getOperator());
        return itemDO;
    }

    public static ItemDO buildItemDOByNoDisplay(ItemRequest request) {
        ItemDO itemDO = new ItemDO();
        itemDO.setId(request.getItemId());
        itemDO.setDisplay(ItemDisplay.YES.getStatus());
        itemDO.setUpdateBy(request.getOperator());
        return itemDO;
    }

    public static ItemResponse buidItemResponseByItemDO(ItemDO itemDO) {
        ItemResponse obj = new ItemResponse();
        obj.setId(itemDO.getId());
        obj.setServerType(itemDO.getServerType());
        obj.setName(itemDO.getName());
        obj.setPrice(itemDO.getPrice());
        obj.setStatus(itemDO.getStatus());
        obj.setNursingStar(itemDO.getNursingStar());
        obj.setRemark(itemDO.getRemark());
        obj.setCoverPicture(itemDO.getCoverPicture());
        obj.setRateCertifiedProperty(itemDO.getRateCertifiedProperty());
        obj.setRateHospital(itemDO.getRateHospital());
        obj.setRateNursing(itemDO.getRateNursing());
        obj.setChargingTime(itemDO.getChargingTime());
        return obj;
    }

    /**
     * 更新实体数据
     * @param request
     * @return
     */
    public static ItemDO buildItemDOByModify(ItemModifyRequest request) {
        ItemDO updateEntity = new ItemDO();
        updateEntity.setId(request.getId());
        ObjUtils.settingNotNull(updateEntity::setServerType,request.getServerType());
        ObjUtils.settingNotNull(updateEntity::setName,request.getName());
        ObjUtils.settingNotNull(updateEntity::setPrice,request.getPrice());
        ObjUtils.settingNotNull(updateEntity::setNursingStar,request.getNursingStar());
        ObjUtils.settingNotNull(updateEntity::setRemark,request.getRemark());
        ObjUtils.settingNotNull(updateEntity::setCoverPicture,request.getCoverPicture());
        ObjUtils.settingNotNull(updateEntity::setChargingTime,request.getChargingTime());
        ObjUtils.settingNotNull(updateEntity::setDisplay,request.getDisplay());
        updateEntity.setUpdateBy(request.getOperator());
        updateEntity.setUpdatedAt(LocalDateTime.now());
        return updateEntity;
    }

    public static List<ItemOrgDO> buildItemOrgDOByModify(List<ItemOrgDO> itemOrgList, ItemModifyRequest request) {
        if (Objects.isNull(request.getOrgIdList())) {
            return null;
        }
        long delVal = DelUtils.delVersion();
        Long operator = request.getOperator();
        Long itemId = request.getId();

        Map<Long, ItemOrgDO> dbOrgMap = itemOrgList.stream()
                .peek(item -> item.setDeleted(delVal))
                .collect(Collectors.toMap(ItemOrgDO::getOrgId, Function.identity()));
        Set<Long> orgIdProcess = new HashSet<>();
        List<ItemOrgDO> result = request.getOrgIdList().stream()
                .map(orgId -> {
                    orgIdProcess.add(orgId);
                    ItemOrgDO itemOrgDO = dbOrgMap.get(orgId);
                    if (Objects.isNull(itemOrgDO)) {
                        return ItemBuilder.buildItemOrgByOrgId(itemId,orgId, operator);
                    }
                    itemOrgDO.setDeleted(OrderConst.NO_DELETED);
                    return itemOrgDO;
                }).collect(Collectors.toList());
        dbOrgMap.forEach((orgId,itemOrg) -> CollUtils.add(!orgIdProcess.contains(orgId),result,itemOrg));
        return result;
    }
}
