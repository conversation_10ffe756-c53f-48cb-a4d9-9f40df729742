package com.avatar.hospital.chaperone.request.repair;

import com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:50
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormFeedbackCreateRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 报修单ID
     */
    @NotNull
    private Long repairFormId;

    /**
     * 反馈结果类型: 1 完成任务 2 需要协助 3 需要使用备件
     */
    @NotNull
    private Integer type;

    /**
     * 备件信息
     */
    private PartApplyModel part;

    /**
     * 情况说明
     */
    private String remark;

    /**
     * 附件URL,数组
     */
    private String attachments;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
