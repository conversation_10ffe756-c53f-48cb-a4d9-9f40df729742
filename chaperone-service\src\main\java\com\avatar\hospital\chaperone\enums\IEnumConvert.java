package com.avatar.hospital.chaperone.enums;

import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 枚举值转换
 * @author: sp0372
 * @create: 2023-10-12 13:52
 **/
public interface IEnumConvert<P> {

    String UNKONW_TIP = "未知";

    /**
     * 参数转为描述
     * @param p
     * @return
     */
    default String toDesc(P p) {
        if (Objects.isNull(p)) {
            return UNKONW_TIP;
        }
        return convertDesc(p);
    }

    /**
     *
     * @param p
     * @return
     */
    String convertDesc(P p);

}
