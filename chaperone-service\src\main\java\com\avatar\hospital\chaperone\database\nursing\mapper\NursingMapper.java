package com.avatar.hospital.chaperone.database.nursing.mapper;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.response.nursing.NursingDetailResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingHospitalResponse;
import com.avatar.hospital.chaperone.response.nursing.NursingPagingResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 护工-记录护工基本信息; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Mapper
public interface NursingMapper extends BaseMapper<NursingDO> {

    Long countByB(Map<String, Object> criteria);

    List<NursingPagingResponse> listByB(Map<String, Object> criteria);

    NursingDetailResponse findByBId(@Param("id") Long id);

    List<NursingHospitalResponse> listHospitalByB(@Param("nursingIds") List<Long> nursingIds);
}
