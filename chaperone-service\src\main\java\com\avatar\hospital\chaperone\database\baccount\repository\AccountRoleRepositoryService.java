package com.avatar.hospital.chaperone.database.baccount.repository;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountRoleDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户角色关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface AccountRoleRepositoryService extends IService<AccountRoleDO> {

    /**
     * 根据roleId批量查询
     *
     * @param roleIds -
     * @return -
     */
    List<AccountRoleDO> findByRoleIds(Set<Long> roleIds);

    /**
     * 根据accountId批量查询
     *
     * @param accountIds -
     * @return -
     */
    List<AccountRoleDO> findByAccountIds(Set<Long> accountIds);

    /**
     * 根据roleId批量删除
     *
     * @param roleIds -
     * @return -
     */
    Boolean deleteByRoleIds(Set<Long> roleIds);

    /**
     * 根据accountId批量删除
     *
     * @param accountIds -
     * @return -
     */
    Boolean deleteByAccountIds(Set<Long> accountIds);
}
