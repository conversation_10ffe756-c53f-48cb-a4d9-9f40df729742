# 医院陪护系统 - 快速部署指南

## 🚀 一键部署

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- Maven 3.6+
- JDK 1.8+

### Linux/macOS 部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd hospital-chaperone

# 2. 给脚本执行权限
chmod +x deploy.sh

# 3. 一键部署
./deploy.sh deploy
```

### Windows 部署

```cmd
# 1. 克隆项目
git clone <repository-url>
cd hospital-chaperone

# 2. 一键部署
deploy.bat deploy
```

## 📋 部署命令

| 命令 | 说明 |
|------|------|
| `./deploy.sh deploy` | 完整部署（构建+启动） |
| `./deploy.sh build` | 仅构建应用和镜像 |
| `./deploy.sh start` | 仅启动服务 |
| `./deploy.sh stop` | 停止服务 |
| `./deploy.sh restart` | 重启服务 |
| `./deploy.sh logs` | 查看日志 |
| `./deploy.sh status` | 查看服务状态 |

## 🌐 访问地址

部署成功后，可通过以下地址访问：

- **后台管理系统**: http://localhost:8081
- **客户端API**: http://localhost:8080
- **MySQL数据库**: localhost:3306
- **Redis缓存**: localhost:6379
- **Nginx代理**: http://localhost:80

## 🔧 配置文件

### 环境变量配置 (.env)

```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=root123456
MYSQL_DATABASE=hospital_chaperone
MYSQL_USER=chaperone
MYSQL_PASSWORD=chaperone123

# 端口配置
WEB_PORT=8081
CONSUMER_PORT=8080
MYSQL_PORT=3306
REDIS_PORT=6379
```

### 应用配置

- **后台系统配置**: `docker/web/config/application-docker.yaml`
- **客户端配置**: `docker/consumer/config/application-docker.yaml`
- **MySQL配置**: `docker/mysql/conf.d/my.cnf`
- **Redis配置**: `docker/redis/redis.conf`
- **Nginx配置**: `docker/nginx/conf.d/default.conf`

## 📊 服务监控

### 健康检查

```bash
# 后台管理系统
curl http://localhost:8081/actuator/health

# 客户端系统
curl http://localhost:8080/actuator/health
```

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs chaperone-web
docker-compose logs chaperone-consumer
docker-compose logs mysql
docker-compose logs redis

# 实时跟踪日志
docker-compose logs -f chaperone-web
```

### 查看服务状态

```bash
# 查看容器状态
docker-compose ps

# 查看资源使用情况
docker stats
```

## 🛠️ 常用运维命令

### 服务管理

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重启特定服务
docker-compose restart chaperone-web
```

### 数据备份

```bash
# 备份MySQL数据
docker-compose exec mysql mysqldump -u root -p hospital_chaperone > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份Redis数据
docker-compose exec redis redis-cli BGSAVE
```

### 数据恢复

```bash
# 恢复MySQL数据
docker-compose exec -T mysql mysql -u root -p hospital_chaperone < backup_20231101_120000.sql
```

## 🔍 故障排查

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8081
   
   # 修改.env文件中的端口配置
   WEB_PORT=8082
   ```

2. **容器启动失败**
   ```bash
   # 查看容器日志
   docker-compose logs container_name
   
   # 查看容器详细信息
   docker inspect container_name
   ```

3. **数据库连接失败**
   ```bash
   # 检查MySQL容器状态
   docker-compose ps mysql
   
   # 测试数据库连接
   docker-compose exec mysql mysql -u chaperone -p hospital_chaperone
   ```

### 重置环境

```bash
# 停止并删除所有容器
docker-compose down

# 删除数据卷（注意：会丢失所有数据）
docker-compose down -v

# 删除镜像
docker rmi hospital-chaperone-web:latest
docker rmi hospital-chaperone-consumer:latest

# 重新部署
./deploy.sh deploy
```

## 📁 项目结构

```
hospital-chaperone/
├── docker/                    # Docker配置文件
│   ├── mysql/conf.d/          # MySQL配置
│   ├── redis/                 # Redis配置
│   ├── nginx/                 # Nginx配置
│   ├── web/config/            # 后台系统配置
│   └── consumer/config/       # 客户端系统配置
├── docs/                      # 项目文档
├── document/sql/              # 数据库初始化脚本
├── chaperone-web-start/       # 后台管理系统
├── chaperone-consumer-start/  # 客户端系统
├── docker-compose.yml         # Docker Compose配置
├── Dockerfile.web            # 后台系统镜像构建文件
├── Dockerfile.consumer       # 客户端系统镜像构建文件
├── .env                      # 环境变量配置
├── deploy.sh                 # Linux/macOS部署脚本
├── deploy.bat                # Windows部署脚本
└── README-DEPLOY.md          # 部署指南
```

## 🔐 安全配置

### 生产环境建议

1. **修改默认密码**
   ```bash
   # 生成强密码
   openssl rand -base64 32
   
   # 更新.env文件
   MYSQL_ROOT_PASSWORD=your_generated_password
   MYSQL_PASSWORD=your_generated_password
   ```

2. **限制网络访问**
   ```bash
   # 使用防火墙限制访问
   sudo ufw allow 80
   sudo ufw allow 443
   sudo ufw deny 3306  # 禁止外部直接访问数据库
   ```

3. **启用SSL/TLS**
   - 配置SSL证书
   - 更新Nginx配置
   - 强制HTTPS访问

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看详细部署文档：`docs/05-Docker部署文档.md`
2. 检查日志输出：`docker-compose logs`
3. 查看故障排查章节
4. 联系技术支持团队

## 📝 更新日志

- **v1.0.0**: 初始版本，支持Docker容器化部署
- 支持一键部署脚本
- 支持多环境配置
- 支持服务监控和健康检查

---

**注意**: 首次部署可能需要较长时间下载Docker镜像和构建应用，请耐心等待。
