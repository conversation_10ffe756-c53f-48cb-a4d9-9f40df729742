package com.avatar.hospital.chaperone.database.baccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrganizationLevel {

    /**
     * 物业
     */
    TENEMENT(1, "物业"),

    /**
     * 医院
     */
    HOSPITAL(2, "医院"),

    /**
     * 科室
     */
    ADMINISTRATIVE_OFFICE(3, "科室"),

    /**
     * 床位
     */
    BED(4, "床位"),


    ;

    private final Integer level;

    private final String describe;


    OrganizationLevel(Integer level, String describe) {
        this.level = level;
        this.describe = describe;
    }

    public static OrganizationLevel of(Integer level) {
        if (level == null) {
            return null;
        }
        for (OrganizationLevel organizationLevel : values()) {
            if (level.equals(organizationLevel.getLevel())) {
                return organizationLevel;
            }
        }
        return null;
    }
}
