package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderEstimateDO;
import com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsEstimateModel;
import com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsNursingModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单-评价; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderEstimateRepositoryService extends IService<OrderEstimateDO> {

    OrderEstimateDO getByOrderId(Long orderId, Integer source);

    /**
     * 统计星数量
     * @param start
     * @param end
     * @return
     */
    StatisticsEstimateModel statisticsStar(LocalDateTime start, LocalDateTime end);

    /**
     * 统计护工排行
     * @param start
     * @param end
     * @return
     */
    List<StatisticsNursingModel> statisticsNursing(LocalDateTime start, LocalDateTime end);
}
