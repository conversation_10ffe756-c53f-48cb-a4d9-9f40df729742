package com.avatar.hospital.chaperone.consumer.controller.organization;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAdministrativeOfficeQueryRequest;
import com.avatar.hospital.chaperone.response.caccount.ConsumerOrganizationHospitalResponse;
import com.avatar.hospital.chaperone.service.baccount.OrganizationService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description: C端组织机构查询
 *
 * <AUTHOR>
 * @since 2023/10/31
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/organization")
public class ConsumerOrganizationController {

    private final OrganizationService organizationService;


    /**
     * C端查询医院层级组织机构列表
     *
     * @return -
     */
    @GetMapping(value = "/hospital-organization-list")
    public SingleResponse<List<ConsumerOrganizationHospitalResponse>> consumerHospitalOrganizationList() {
        return TemplateProcess.doProcess(log, organizationService::consumerHospitalOrganizationList);
    }

    /**
     * C端查询医院层级组织机构列表
     *
     * @return -
     */
    @GetMapping(value = "/administrative-office-organization-list")
    public SingleResponse<List<ConsumerOrganizationHospitalResponse>> consumerAdministrativeOfficeOrganizationList(ConsumerAdministrativeOfficeQueryRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
            AssertUtils.isNotNull(request.getHospitalOrganizationId(), ErrorCode.PARAMETER_ERROR);
            return organizationService.consumerAdministrativeOfficeOrganizationList(request);
        });
    }

}
