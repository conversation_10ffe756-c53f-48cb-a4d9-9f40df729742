package com.avatar.hospital.chaperone.database.plan.dataobject;

import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanTaskDO;
import com.avatar.hospital.chaperone.database.plan.enums.CircleType;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import static com.avatar.hospital.chaperone.utils.DateUtils.DAY_INT_PATTERN;

/**
 * <p>
 * 巡检计划任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_patrol_plan_task")
public class PatrolPlanTaskDO extends PlanTaskDO {

    public static String generateName(String planName, CircleType circleType, Integer num) {
        StringBuilder sb = new StringBuilder();
        sb.append(planName).append(circleType.getDescribe()).append("任务-");

        switch (circleType) {
            case DAY:
                sb.append(DateUtils.dateTimeStr(DAY_INT_PATTERN)).append("-").append(num);
                break;
            case MONTH:
                sb.append(DateUtils.monthStartStr()).append("-").append(num);
                break;
        }
        return sb.toString();
    }
}
