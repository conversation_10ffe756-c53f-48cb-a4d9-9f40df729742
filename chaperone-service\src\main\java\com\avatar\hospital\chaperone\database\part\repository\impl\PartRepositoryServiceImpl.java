package com.avatar.hospital.chaperone.database.part.repository.impl;

import com.avatar.hospital.chaperone.database.part.dataobject.PartDO;
import com.avatar.hospital.chaperone.database.part.enums.PartStatusType;
import com.avatar.hospital.chaperone.database.part.mapper.PartMapper;
import com.avatar.hospital.chaperone.database.part.repository.PartRepositoryService;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 备品备件明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class PartRepositoryServiceImpl extends ServiceImpl<PartMapper, PartDO> implements PartRepositoryService {

    @Override
    public List<PartDO> listN(Long batchId,Integer quantity) {
        Page<PartDO> page = PageDTO.of(1, quantity);
        LambdaQueryWrapper<PartDO> queryWrapper = queryWrapper();
        queryWrapper.eq(PartDO::getStatus, PartStatusType.STOCK.getCode());
        queryWrapper.eq(PartDO::getSparePartBatchId, batchId);
        page = page(page, queryWrapper);
        return page.getRecords();
    }

    private LambdaQueryWrapper<PartDO> queryWrapper() {
        LambdaQueryWrapper<PartDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PartDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
