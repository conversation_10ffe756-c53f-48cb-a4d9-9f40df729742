package com.avatar.hospital.chaperone.service.order;

import com.avatar.hospital.chaperone.TestApplication;
import com.avatar.hospital.chaperone.request.order.OrderRequest;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-06 10:55
 **/
@SpringBootTest(classes = TestApplication.class)
public class OrderServiceTest {
    @Autowired
    private OrderService orderService;

    @Test
    public void testConfirm() {
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.setOrderId(281034038190850048L);
        orderRequest.setOperatorUser(OrderConst.JOB_OPERATOR);
        orderService.confirm(orderRequest);
    }
}
