package com.avatar.hospital.chaperone.service.plan;

import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.plan.PlanVO;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 10:48
 */
public interface PlanService {
    Long create(PlanRequest request);

    Boolean update(PlanRequest request);

    PageResponse<PlanVO> paging(QueryRequest request);

    PlanVO detail(PlanType planType, Long id);

    void execute(PlanType planType);

    Boolean abandon(PlanRequest request);

}
