package com.avatar.hospital.chaperone.database.repair.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 13:23
 **/
@Getter
public enum RepairFormStatus implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    INIT(1, "未指派"),
    HAS_EXECUTOR(2, "有执行人"),
    NOT_FINISH(3, "未完成"),
    AUTH(4, "待审核"),
    FINISH(5, "已完成"),
    ;

    private final Integer status;

    private final String describe;


    RepairFormStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static RepairFormStatus of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (RepairFormStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        RepairFormStatus e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    public static  final Map<Integer, String> toMap() {
        RepairFormStatus[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (RepairFormStatus value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

}
