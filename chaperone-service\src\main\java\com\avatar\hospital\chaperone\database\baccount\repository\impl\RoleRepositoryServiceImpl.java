package com.avatar.hospital.chaperone.database.baccount.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountRoleDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleMenuDO;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.baccount.mapper.RoleMapper;
import com.avatar.hospital.chaperone.database.baccount.repository.AccountRoleRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleMenuRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.DefaultUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RoleRepositoryServiceImpl extends ServiceImpl<RoleMapper, RoleDO> implements RoleRepositoryService {

    private final AccountRoleRepositoryService accountRoleRepositoryService;
    private final RoleMenuRepositoryService roleMenuRepositoryService;

    @Override
    public Long add(RoleDO roleDO) {
        if (roleDO == null) {
            return null;
        }
        roleDO.setId(IdUtils.getId());
        if (!save(roleDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        return roleDO.getId();
    }

    @Override
    public Boolean incrementUpdate(RoleDO roleDO) {
        if (roleDO == null) {
            return false;
        }
        LambdaUpdateWrapper<RoleDO> updateWrapper = updateWrapper();
        updateWrapper.set(roleDO.getName() != null, RoleDO::getName, roleDO.getName());
        updateWrapper.set(roleDO.getRoleKey() != null, RoleDO::getRoleKey, roleDO.getRoleKey());
        updateWrapper.set(roleDO.getStatus() != null, RoleDO::getStatus, roleDO.getStatus());
        updateWrapper.set(roleDO.getSort() != null, RoleDO::getSort, roleDO.getSort());
        updateWrapper.set(roleDO.getRemark() != null, RoleDO::getRemark, roleDO.getRemark());
        updateWrapper.set(roleDO.getUpdateBy() != null, RoleDO::getUpdateBy, roleDO.getUpdateBy());

        updateWrapper.eq(RoleDO::getId, roleDO.getId());

        return update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByIds(Set<Long> ids, Long updateBy) {
        if (CollectionUtils.isEmpty(ids) || updateBy == null) {
            return true;
        }
        LambdaUpdateWrapper<RoleDO> updateWrapper = updateWrapper();
        updateWrapper.set(RoleDO::getDeleted, System.currentTimeMillis());
        updateWrapper.set(RoleDO::getUpdateBy, updateBy);
        if (ids.size() == 1) {
            updateWrapper.eq(RoleDO::getId, ids.toArray()[0]);
        }
        else {
            updateWrapper.in(RoleDO::getId, ids);
        }
        if (!update(updateWrapper)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        // 删除关联数据
        List<AccountRoleDO> accountRoleList = accountRoleRepositoryService.findByRoleIds(ids);
        if (CollectionUtils.isNotEmpty(accountRoleList)) {
            if (!Boolean.TRUE.equals(accountRoleRepositoryService.deleteByRoleIds(
                    accountRoleList.stream().map(AccountRoleDO::getRoleId).collect(Collectors.toSet())
            ))) {
                throw BusinessException.of(ErrorCode.UPDATE_ERROR);
            }
        }
        List<RoleMenuDO> roleMenuList = roleMenuRepositoryService.findByRoleIds(ids);
        if (CollectionUtils.isNotEmpty(roleMenuList)) {
            if (!Boolean.TRUE.equals(roleMenuRepositoryService.deleteByRoleIds(
                    roleMenuList.stream().map(RoleMenuDO::getRoleId).collect(Collectors.toSet())
            ))) {
                throw BusinessException.of(ErrorCode.UPDATE_ERROR);
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean allocationMenu(Long roleId, Set<Long> menuIds) {
        if (roleId == null || CollectionUtils.isEmpty(menuIds)) {
            return false;
        }
        List<RoleMenuDO> roleMenuList = roleMenuRepositoryService.findByRoleIds(Sets.newHashSet(roleId));
        if (CollectionUtils.isNotEmpty(roleMenuList)) {
            if (!Boolean.TRUE.equals(roleMenuRepositoryService.deleteByRoleIds(Sets.newHashSet(roleId)))) {
                throw BusinessException.of(ErrorCode.UPDATE_ERROR);
            }
        }

        List<RoleMenuDO> insertRoleMenuList = Lists.newArrayListWithCapacity(menuIds.size());
        menuIds.forEach(menuId -> {
            RoleMenuDO roleMenuDO = new RoleMenuDO();
            roleMenuDO.setRoleId(roleId);
            roleMenuDO.setMenuId(menuId);
            insertRoleMenuList.add(roleMenuDO);
        });

        if (!roleMenuRepositoryService.saveBatch(insertRoleMenuList)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        return true;
    }

    @Override
    public RoleDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<RoleDO> queryWrapper = queryWrapper();
        queryWrapper.eq(RoleDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<RoleDO> findByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<RoleDO> queryWrapper = queryWrapper();
        if (ids.size() == 1) {
            queryWrapper.eq(RoleDO::getId, ids.toArray()[0]);
        }
        else {
            queryWrapper.in(RoleDO::getId, ids);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public PageResponse<RoleDO> paging(Integer pageIndex, Integer pageSize, RoleDO roleDO) {
        int index = DefaultUtils.ifNullDefault(pageIndex, 1);
        int size = DefaultUtils.ifNullDefault(pageSize, 10);
        LambdaQueryWrapper<RoleDO> queryWrapper = queryWrapper();
        if (roleDO != null) {
            queryWrapper.eq(roleDO.getName() != null, RoleDO::getName, roleDO.getName());
        }
        queryWrapper.orderByDesc(RoleDO::getSort);
        IPage<RoleDO> page = baseMapper.selectPage(new Page<>(index, size), queryWrapper);
        return PageResponse.build(page.getTotal(), page.getCurrent(), page.getSize(), page.getRecords());
    }

    @Override
    public RoleDO findByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        LambdaQueryWrapper<RoleDO> queryWrapper = queryWrapper();
        queryWrapper.eq(RoleDO::getName, name);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public RoleDO findByRoleKey(String roleKey) {
        if (StringUtils.isBlank(roleKey)) {
            return null;
        }
        LambdaQueryWrapper<RoleDO> queryWrapper = queryWrapper();
        queryWrapper.eq(RoleDO::getRoleKey, roleKey);
        return baseMapper.selectOne(queryWrapper);
    }

    private LambdaQueryWrapper<RoleDO> queryWrapper() {
        LambdaQueryWrapper<RoleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RoleDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<RoleDO> updateWrapper() {
        LambdaUpdateWrapper<RoleDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RoleDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
