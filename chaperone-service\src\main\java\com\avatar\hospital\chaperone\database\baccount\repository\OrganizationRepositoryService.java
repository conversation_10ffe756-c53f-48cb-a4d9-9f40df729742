package com.avatar.hospital.chaperone.database.baccount.repository;

import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * B端组织机构表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface OrganizationRepositoryService extends IService<OrganizationDO> {

    /**
     * 添加组织机构
     *
     * @param organizationDO -
     * @return 组织机构ID
     */
    Long add(OrganizationDO organizationDO);

    /**
     * 更新组织机构
     *
     * @param organizationDO -
     * @return -
     */
    Boolean incrementUpdate(OrganizationDO organizationDO);

    /**
     * 根据ID逻辑删除数据
     *
     * @param ids      -
     * @param updateBy -
     * @return -
     */
    Boolean deleteByIds(Set<Long> ids, Long updateBy);

    /**
     * 根据ID查询
     *
     * @param id -
     * @return -
     */
    OrganizationDO findById(Long id);

    /**
     * 根据ID批量查询
     *
     * @param ids -
     * @return -
     */
    List<OrganizationDO> findByIds(Set<Long> ids);

    /**
     * 查询所有组织架构
     *
     * @return -
     */
    List<OrganizationDO> listAll();

    /**
     * 根据id&level批量查询
     *
     * @param ids   -
     * @param level -
     * @return -
     */
    List<OrganizationDO> findByIdsAndLevel(Set<Long> ids, Integer level);

    /**
     * 根据id&level&status批量查询
     *
     * @param ids    -
     * @param level  -
     * @param types  -
     * @param status -
     * @return -
     */
    List<OrganizationDO> findByIdsAndLevelAndTypesAndStatus(Set<Long> ids, Integer level, Set<Integer> types, Integer status);

    /**
     * 根据parentId查询
     *
     * @param ids -
     * @return -
     */
    List<OrganizationDO> findByParentId(Set<Long> ids);

    /**
     * 根据level&type&status查询
     *
     * @param level  -
     * @param types  -
     * @param status -
     * @return -
     */
    List<OrganizationDO> findByLevelAndTypesAndStatus(Integer level, Set<Integer> types, Integer status);

    /**
     * 根据id&level&type查询
     *
     * @param ids   -
     * @param level -
     * @param types -
     * @return -
     */
    List<OrganizationDO> findByIdsAndLevelAndTypes(Set<Long> ids, Integer level, Set<Integer> types);

    /**
     * 根据parentId&level&status查询
     *
     * @param parentId -
     * @param level    -
     * @param status   -
     * @return -
     */
    List<OrganizationDO> findByParentIdAndLevelAndStatus(Long parentId, Integer level, Integer status);
}
