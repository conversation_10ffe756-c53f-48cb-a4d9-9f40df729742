package com.avatar.hospital.chaperone.consumer.utils;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Slf4j
public class ConsumerAccountUtils {

    /**
     * 获取当前请求用户的accountId 没有获取到不抛出异常 返回空
     *
     * @return -
     */
    public static Long getCurrentAccountId() {
        try {
            return StpUtil.getLoginIdAsLong();
        } catch (NotLoginException notLoginException) {
            log.warn("ConsumerAccountUtils getCurrentAccountId notLogin");
        } catch (Exception e) {
            log.error("ConsumerAccountUtils getCurrentAccountId notLogin error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 获取当前请求用户的accountId 没有获取到抛出异常
     *
     * @return -
     */
    public static Long getCurrentAccountIdAndThrow() {
        try {
            return StpUtil.getLoginIdAsLong();
        } catch (NotLoginException notLoginException) {
            log.warn("ConsumerAccountUtils getCurrentAccountIdAndThrow notLogin");
            throw notLoginException;
        } catch (Exception e) {
            log.error("ConsumerAccountUtils getCurrentAccountIdAndThrow notLogin error:{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.of(ErrorCode.CONSUMER_ACCOUNT_NOT_LOGIN_ERROR);
        }
    }
}
