package com.avatar.hospital.chaperone.database.part.dataobject.model;


import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 备件统计消耗模型
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
public class PartStatisticsModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 组织机构ID
     */
    private Long orgId;

    /**
     * 组织机构名称
     */
    private String orgName;

    /**
     * 组织机构类型 1 物业 2 医院
     */
    private Integer investorType;

    /**
     * 使用数量
     */
    private Integer quantity;

    /**
     * 使用价格
     */
    private Integer price;
}
