package com.avatar.hospital.chaperone.builder.device;

import com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO;
import com.avatar.hospital.chaperone.database.emergencylog.dataobject.EmergencyHandlingLogDO;
import com.avatar.hospital.chaperone.request.device.DeviceAddRequest;
import com.avatar.hospital.chaperone.request.device.DeviceUpdateRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogAddRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.device.DevicePagingResponse;
import com.avatar.hospital.chaperone.response.emergency.EmergencyLogPagingResponse;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * @author:sp0420
 * @Description:
 */
public class DeviceBuilder {

    public static ProjectDeviceDO buildEmergencyLogDO(DeviceAddRequest request) {
        if (request == null) {
            return null;
        }
        ProjectDeviceDO date = new ProjectDeviceDO();

//        date.setCode(request.getCode());
        date.setName(request.getName());
        date.setOrgDepartmentId(request.getOrgDepartmentId());
        date.setOrgId(request.getOrgId());
        date.setStatus(request.getStatus());
        date.setAddress(request.getAddress());
        date.setSystemType(request.getSystemType());
        date.setRemark(request.getRemark());
        date.setCreateBy(request.getCreateBy());
        date.setUpdateBy(request.getCreateBy());
        date.setCreatedAt(LocalDateTime.now());
        date.setUpdatedAt(LocalDateTime.now());
        date.setDeleted(DelUtils.NO_DELETED);
        return date;
    }

    public static ProjectDeviceDO buildEmergencyLogDO(DeviceUpdateRequest request) {
        if (request == null) {
            return null;
        }
        ProjectDeviceDO date = new ProjectDeviceDO();
        date.setId(request.getId());
        date.setCode(request.getCode());
        date.setName(request.getName());
        date.setOrgDepartmentId(request.getOrgDepartmentId());
        date.setOrgId(request.getOrgId());
        date.setStatus(request.getStatus());
        date.setAddress(request.getAddress());
        date.setSystemType(request.getSystemType());
        date.setRemark(request.getRemark());
        date.setUpdateBy(request.getUpdateBy());
        date.setUpdatedAt(LocalDateTime.now());
        date.setDeleted(DelUtils.NO_DELETED);
        return date;
    }

    public static PageResponse<DevicePagingResponse> buildEmergencyLogDO(PageResponse<ProjectDeviceDO> pageResponse) {
        if (pageResponse == null) {
            return null;
        }

        PageResponse<DevicePagingResponse> response = new PageResponse<>();
        response.setTotal(pageResponse.getTotal());
        response.setCurrent(pageResponse.getCurrent());
        response.setSize(pageResponse.getSize());
        response.setRecords(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(pageResponse.getRecords())) {
            response.setRecords(buildEmergencyLogDO(pageResponse.getRecords()));
        }
        return response;
    }

    private static List<DevicePagingResponse> buildEmergencyLogDO(List<ProjectDeviceDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        List<DevicePagingResponse> list = Lists.newLinkedList();
        for (ProjectDeviceDO logDO : records) {
            list.add(buildEmergencyLogDO(logDO));
        }
        return list;
    }

    public static DevicePagingResponse buildEmergencyLogDO(ProjectDeviceDO logDO) {
        if (logDO == null) {
            return null;
        }
        DevicePagingResponse logPagingResponse = new DevicePagingResponse();
        logPagingResponse.setId(logDO.getId());
        logPagingResponse.setCode(logDO.getCode());
        logPagingResponse.setName(logDO.getName());
        logPagingResponse.setOrgDepartmentId(logDO.getOrgDepartmentId());
        logPagingResponse.setOrgId(logDO.getOrgId());
        logPagingResponse.setStatus(logDO.getStatus());
        logPagingResponse.setAddress(logDO.getAddress());
        logPagingResponse.setRemark(logDO.getRemark());
        logPagingResponse.setSystemType(logDO.getSystemType());
        return logPagingResponse;
    }

}
