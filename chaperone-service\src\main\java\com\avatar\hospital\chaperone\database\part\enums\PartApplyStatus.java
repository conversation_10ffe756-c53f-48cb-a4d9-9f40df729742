package com.avatar.hospital.chaperone.database.part.enums;

import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @description
 * @date 2023/10/26 19:55
 */
@Getter
public enum PartApplyStatus {
    NONE(-1, "未知"),
    INIT(1, "待审核"),
    PASS(2, "允许使用"),
    REJECT(3, "不允许使用"),
    ;

    private final Integer code;

    private final String describe;


    PartApplyStatus(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static PartApplyStatus of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (PartApplyStatus type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
