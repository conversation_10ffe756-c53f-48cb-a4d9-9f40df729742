package com.avatar.hospital.chaperone.component.wx;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountOpenIdDO;
import com.avatar.hospital.chaperone.database.caccount.repository.AccountOpenIdRepositoryService;
import com.avatar.hospital.chaperone.database.message.dataobject.MessageDO;
import com.avatar.hospital.chaperone.database.message.enums.MessageChannel;
import com.avatar.hospital.chaperone.database.message.enums.MessageSendFlag;
import com.avatar.hospital.chaperone.database.message.repository.MessageRepositoryService;
import com.avatar.hospital.chaperone.properties.WxTemplateMessageProperties;
import com.binarywang.spring.starter.wxjava.mp.properties.WxMpProperties;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description: 微信模板消息发送
 *
 * <AUTHOR>
 * @since 2023/10/17
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WxTemplateMessageSendComponent {

    private final WxMpProperties wxMpProperties;
    private final WxMpService wxMpService;
    private final WxMaService wxMaService;
    private final WxTemplateMessageProperties wxTemplateMessageProperties;
    private final AccountOpenIdRepositoryService accountOpenIdRepositoryService;
    private final MessageRepositoryService messageRepositoryService;

    /**
     * 发送固定模板消息
     *
     * @param accountId         -
     * @param templateMessageId 模板消息ID
     */
    public void sendImmobilization(Long accountId, String templateMessageId) {
        log.info("WxTemplateMessageSendComponent sendImmobilization accountId:{},templateMessageId:{}", accountId, templateMessageId);
        if (accountId == null || StringUtils.isBlank(templateMessageId)) {
            return;
        }
        List<WxTemplateMessageProperties.TemplateData> templateDataList = wxTemplateMessageProperties.getTemplateData(templateMessageId);
        if (CollectionUtils.isEmpty(templateDataList)) {
            log.warn("WxTemplateMessageSendComponent sendImmobilization templateData empty templateMessageId:{}", templateMessageId);
            return;
        }
        AccountOpenIdDO accountOpenIdDO = accountOpenIdRepositoryService.findByAccountIdAndAppId(accountId, wxMaService.getWxMaConfig().getAppid());
        if (accountOpenIdDO == null || StringUtils.isBlank(accountOpenIdDO.getOpenId())) {
            log.warn("WxTemplateMessageSendComponent sendImmobilization accountOpenIdDO is null accountId:{},templateMessageId:{}", accountId, templateMessageId);
            return;
        }
        WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
        wxMpTemplateMessage.setTemplateId(templateMessageId);
        wxMpTemplateMessage.setToUser(accountOpenIdDO.getOpenId());
        templateDataList.forEach(templateDaa -> wxMpTemplateMessage.addData(new WxMpTemplateData(templateDaa.getName(), templateDaa.getValue())));

        Long messageRecordId = null;
        Integer sendFlag = MessageSendFlag.FAIL.getStatus();
        String errorMessage = "", result = "";
        try {
            messageRecordId = addMessageRecord(wxMpTemplateMessage);

            result = wxMpService.getTemplateMsgService().sendTemplateMsg(wxMpTemplateMessage);
            log.info("WxTemplateMessageSendComponent sendImmobilization result:{}", result);

            sendFlag = MessageSendFlag.SUCCESS.getStatus();
        } catch (Exception e) {
            log.error("WxTemplateMessageSendComponent sendImmobilization error:{}", Throwables.getStackTraceAsString(e));
            errorMessage = e.getMessage();
        }
        // 更新消息记录
        if (messageRecordId != null) {
            updateMessageRecord(messageRecordId, sendFlag, result, errorMessage);
        }
    }

    /**
     * 发放模板消息
     *
     * @param accountId         -
     * @param templateMessageId 模板消息ID
     * @param templateDataList  -
     */
    public void sendMessage(Long accountId, String templateMessageId, List<WxTemplateMessageProperties.TemplateData> templateDataList) {
        log.info("WxTemplateMessageSendComponent sendMessage accountId:{},templateMessageId:{},templateDataList:{}", accountId, templateMessageId, templateDataList);
        if (accountId == null || StringUtils.isBlank(templateMessageId) || CollectionUtils.isEmpty(templateDataList)) {
            return;
        }
        AccountOpenIdDO accountOpenIdDO = accountOpenIdRepositoryService.findByAccountIdAndAppId(accountId,wxMpProperties.getAppId());
        if (accountOpenIdDO == null || StringUtils.isBlank(accountOpenIdDO.getOpenId())) {
            log.warn("WxTemplateMessageSendComponent sendMessage accountOpenIdDO is null accountId:{},templateMessageId:{}", accountId, templateMessageId);
            return;
        }
        WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
        wxMpTemplateMessage.setTemplateId(templateMessageId);
        wxMpTemplateMessage.setToUser(accountOpenIdDO.getOpenId());
        templateDataList.forEach(templateDaa -> wxMpTemplateMessage.addData(new WxMpTemplateData(templateDaa.getName(), templateDaa.getValue())));
        Long messageRecordId = null;
        Integer sendFlag = MessageSendFlag.FAIL.getStatus();
        String errorMessage = "", result = MessageSendFlag.FAIL.getDescribe();
        try {
            messageRecordId = addMessageRecord(wxMpTemplateMessage);

            result = wxMpService.getTemplateMsgService().sendTemplateMsg(wxMpTemplateMessage);
            log.info("WxTemplateMessageSendComponent sendMessage result:{}", result);

            sendFlag = MessageSendFlag.SUCCESS.getStatus();
        } catch (Exception e) {
            log.error("WxTemplateMessageSendComponent sendMessage error:{}", Throwables.getStackTraceAsString(e));
            errorMessage = e.getMessage();
        }
        // 更新消息记录
        if (messageRecordId != null) {
            updateMessageRecord(messageRecordId, sendFlag, result, errorMessage);
        }
    }

    /**
     * 保存消息发放记录
     *
     * @param wxMpTemplateMessage -
     * @return 消息ID
     */
    private Long addMessageRecord(WxMpTemplateMessage wxMpTemplateMessage) {
        Long messageId = null;
        try {
            MessageDO messageDO = new MessageDO();
            messageDO.setTemplateId(wxMpTemplateMessage.getTemplateId());
            messageDO.setOpenId(wxMpTemplateMessage.getToUser());
            messageDO.setChannel(MessageChannel.WECHAT.getChannel());
            messageDO.setContent(JSON.toJSONString(wxMpTemplateMessage));
            messageDO.setSendFlag(MessageSendFlag.INT.getStatus());
            messageDO.setDeleted(0L);

            messageId = messageRepositoryService.add(messageDO);
            if (messageId == null) {
                log.warn("WxTemplateMessageSendComponent addMessageRecord fail wxMpTemplateMessage:{}", JSON.toJSONString(wxMpTemplateMessage));
            }
        } catch (Exception e) {
            log.error("WxTemplateMessageSendComponent addMessageRecord wxMpTemplateMessage:{},error:{}", JSON.toJSONString(wxMpTemplateMessage), Throwables.getStackTraceAsString(e));
        }
        return messageId;
    }

    /**
     * 更新消息记录
     *
     * @param messageId    -
     * @param sendFlag     -
     * @param result       -
     * @param errorMessage -
     */
    private void updateMessageRecord(Long messageId, Integer sendFlag, String result, String errorMessage) {
        Boolean updateResult = messageRepositoryService.updateMessageRecord(messageId, sendFlag, result, errorMessage);
        if (Boolean.FALSE.equals(updateResult)) {
            log.warn("WxTemplateMessageSendComponent updateMessageRecord fail messageId:{}", messageId);
        }
    }

}
