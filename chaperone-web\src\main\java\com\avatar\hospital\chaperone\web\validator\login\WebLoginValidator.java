package com.avatar.hospital.chaperone.web.validator.login;

import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.PhoneNumberPasswordWebLoginRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public class WebLoginValidator {

    public static void loginValidate(PhoneNumberPasswordWebLoginRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getPhoneNumber(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getPassword(), ErrorCode.PARAMETER_ERROR);
    }

}
