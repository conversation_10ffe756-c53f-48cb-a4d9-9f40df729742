package com.avatar.hospital.chaperone.annotation.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.*;
import java.time.format.DateTimeFormatter;


/**
 * @author:
 * 支持时间格式：
 * 字符串：
 *       "yyyy-MM-dd HH:mm:ss"
 *       "yyyy-MM-ddTHH:mm:ss.SSSZ"
 *       "yyyy-MM-ddTHH:mm:ss"
 *       "yyyy-mm-dd"
 * 数字：时间戳 毫秒或者秒
 *
 * @date: 2020/12/25
 */
public class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
    private static final Logger logger = LoggerFactory.getLogger(LocalDateTimeDeserializer.class);
    private static ZoneId zoneId = ZoneId.systemDefault();
    private static String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";
    private static String dateFormat = "yyyy-MM-dd";
    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    @Override
    public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {

        if (parser.hasTokenId(JsonToken.VALUE_NUMBER_INT.id())) {
            Long timestamp = parser.getValueAsLong();
            if (timestamp == 0) {
                return null;
            }
            return timestamp.toString().length() == 10 ? Instant.ofEpochSecond(timestamp).atZone(zoneId).toLocalDateTime()
                    : Instant.ofEpochMilli(timestamp).atZone(zoneId).toLocalDateTime();

        } else if (parser.hasTokenId(JsonToken.VALUE_STRING.id())) {
            String string = parser.getText().trim();
            if (string.length() == 0) {
                return null;
            } else if (string.contains("T")) {
                try {
                    if (string.length() > 10 && string.charAt(10) == 'T') {
                        return string.endsWith("Z") ? LocalDateTime.ofInstant(Instant.parse(string), ZoneOffset.UTC)
                                : LocalDateTime.parse(string, DEFAULT_FORMATTER);
                    } else {
                        logger.error("{} convert to LocalDateTime failed", string);
                        throw new DateTimeException(string + " convert to LocalDateTime failed");
                    }
                } catch (DateTimeException var12) {
                    logger.error("{} convert to LocalDateTime failed", string);
                    throw new DateTimeException(string + " convert to LocalDateTime failed");
                }

            } else {
                if(NumberUtils.isNumber(string)){
                    Long timestamp = parser.getValueAsLong();
                    return timestamp.toString().length() == 10 ? Instant.ofEpochSecond(timestamp).atZone(zoneId).toLocalDateTime()
                            : Instant.ofEpochMilli(timestamp).atZone(zoneId).toLocalDateTime();

                }else{
                    try {
                        return string.length() == 10 ? LocalDateTime.parse(string, DateTimeFormatter.ofPattern(dateFormat))
                                : LocalDateTime.parse(string, DateTimeFormatter.ofPattern(dateTimeFormat));
                    } catch (Exception e) {
                        logger.error("{} convert to LocalDateTime failed", string);
                        throw new DateTimeException(string + " convert to LocalDateTime failed");
                    }
                }
            }
        }else {
            String string = parser.getText().trim();
            if (parser.isExpectedStartArrayToken()) {
                JsonToken t = parser.nextToken();
                if (t == JsonToken.END_ARRAY) {
                    return null;
                }
                LocalDateTime result;
                if ((t == JsonToken.VALUE_STRING || t == JsonToken.VALUE_EMBEDDED_OBJECT)
                        && context.isEnabled(DeserializationFeature.UNWRAP_SINGLE_VALUE_ARRAYS)) {
                    result = this.deserialize(parser, context);
                    if (parser.nextToken() != JsonToken.END_ARRAY) {
                        logger.error("{} convert to LocalDateTime failed", string);
                        throw new DateTimeException(string + " convert to LocalDateTime failed");
                    }

                    return result;
                }

                if (t == JsonToken.VALUE_NUMBER_INT) {
                    int year = parser.getIntValue();
                    int month = parser.nextIntValue(-1);
                    int day = parser.nextIntValue(-1);
                    int hour = parser.nextIntValue(-1);
                    int minute = parser.nextIntValue(-1);
                    t = parser.nextToken();
                    if (t == JsonToken.END_ARRAY) {
                        result = LocalDateTime.of(year, month, day, hour, minute);
                    } else {
                        int second = parser.getIntValue();
                        t = parser.nextToken();
                        if (t == JsonToken.END_ARRAY) {
                            result = LocalDateTime.of(year, month, day, hour, minute, second);
                        } else {
                            int partialSecond = parser.getIntValue();
                            if (partialSecond < 1000 && !context.isEnabled(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS)) {
                                partialSecond *= 1000000;
                            }

                            if (parser.nextToken() != JsonToken.END_ARRAY) {
                                throw context.wrongTokenException(parser, this.handledType(), JsonToken.END_ARRAY, "Expected array to end");
                            }

                            result = LocalDateTime.of(year, month, day, hour, minute, second, partialSecond);
                        }
                    }

                    return result;
                }
                context.reportInputMismatch(this.handledType(), "Unexpected token (%s) within Array, expected VALUE_NUMBER_INT", new Object[]{t});
            }

            if (parser.hasToken(JsonToken.VALUE_EMBEDDED_OBJECT)) {
                return (LocalDateTime) parser.getEmbeddedObject();
            } else {
                logger.error("{} convert to LocalDateTime failed", string);
                throw new DateTimeException(string + " convert to LocalDateTime failed");
            }
        }
    }

    @Override
    public Class<?> handledType() {
        return LocalDateTime.class;
    }
}