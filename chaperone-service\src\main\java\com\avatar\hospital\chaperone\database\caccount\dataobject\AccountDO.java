package com.avatar.hospital.chaperone.database.caccount.dataobject;

import com.avatar.hospital.chaperone.database.baccount.enums.AccountStatus;
import com.avatar.hospital.chaperone.database.caccount.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * C端用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_c_account")
public class AccountDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户名称
     */
    private String nickName;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 性别
     *
     * @see com.avatar.hospital.chaperone.database.caccount.enums.AccountSex
     */
    private Integer sex;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 用户头像地址
     */
    private String avatarUrl;


    /**
     * 入院日期
     */
    private LocalDate admissionTime;


    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.caccount.enums.AccountStatus
     */
    private Integer status;

    /**
     * 账号状态是否可用
     *
     * @return -
     */
    public boolean enable() {
        return AccountStatus.ENABLE.getStatus().equals(status);
    }

}
