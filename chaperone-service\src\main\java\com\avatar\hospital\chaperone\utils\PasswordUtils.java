package com.avatar.hospital.chaperone.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
public class PasswordUtils {

    /**
     * 密码加密
     *
     * @param password 明文密码
     * @return 加密后的密码
     */
    public static String encrypt(String password) {
        if (StringUtils.isBlank(password)) {
            return null;
        }
        return getMd5Str(password);
    }

    /**
     * 密码比对
     *
     * @param inputPassword    明文密码
     * @param dataBasePassword 加密后的密码
     * @return -
     */
    public static boolean compare(String inputPassword, String dataBasePassword) {
        if (inputPassword == null || dataBasePassword == null) {
            return false;
        }
        return getMd5Str(inputPassword).equals(dataBasePassword);
    }

    /**
     * 设置密码时 密码长度校验
     *
     * @param password 明文密码
     * @return -
     */
    public static boolean passwordLengthValidate(String password) {
        if (StringUtils.isBlank(password)) {
            return false;
        }
        return password.length() >= 8;
    }

    private static String getMd5Str(String str) {
        return DigestUtils.md5DigestAsHex(str.getBytes(StandardCharsets.UTF_8)).toUpperCase();
    }
}
