package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.order.dto.OrderConsumerlogExtraNursingInfoDTO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单-消耗明细表(天，套餐);
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_order_consumerlog")
public class OrderConsumerlogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 日期,格式yyyyMMdd
     */
      private Integer date;

      /**
     * 订单ID
     */
      private Long orderId;

      /**
     * 关联套餐
     */
      private Long itemId;
    /**
     * 关联套餐名称
     */
    private String itemName;

  /**
   * 总金额(原始)：分
   */
      private Integer totalPriceSource;

      /**
     * 总金额：分
     */
      private Integer totalPrice;

      /**
     * 物业分成比例
     */
      private Integer rateCertifiedProperty;

      /**
     * 物业金额
     */
      private Integer priceCertifiedProperty;

      /**
     * 医院分成比例
     */
      private Integer rateHospital;

      /**
     * 医院金额
     */
      private Integer priceHospital;

      /**
     * 护工分成比例
     */
      private Integer rateNursing;

      /**
     * 护工金额
     */
      private Integer priceNursing;

      /**
     * 版本， 有效版本 1
     */
      private Long version;

    /**
     * 护工ID
     */
      @Deprecated
    private Long nursingId;

    /**
     * 护工名称
     */
    @Deprecated
    private String nursingName;

    /**
     *  护工扩展信息
     * @see OrderConsumerlogExtraNursingInfoDTO
     */
    private String extraNursingInfo;
}
