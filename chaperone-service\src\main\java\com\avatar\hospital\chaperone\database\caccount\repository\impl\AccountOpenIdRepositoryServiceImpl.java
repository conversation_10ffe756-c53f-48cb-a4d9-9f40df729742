package com.avatar.hospital.chaperone.database.caccount.repository.impl;

import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountOpenIdDO;
import com.avatar.hospital.chaperone.database.caccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.caccount.mapper.AccountOpenIdMapper;
import com.avatar.hospital.chaperone.database.caccount.repository.AccountOpenIdRepositoryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * C端用户关联三方用户openId表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Slf4j
@Service
public class AccountOpenIdRepositoryServiceImpl extends ServiceImpl<AccountOpenIdMapper, AccountOpenIdDO> implements AccountOpenIdRepositoryService {

    @Override
    public Boolean add(AccountOpenIdDO accountOpenIdDO) {
        if (accountOpenIdDO == null) {
            return false;
        }
        return save(accountOpenIdDO);
    }

    @Override
    public Boolean deleteByOpenIdAndAppId(String openId, String appId) {
        if (StringUtils.isBlank(openId) || StringUtils.isBlank(appId)) {
            return false;
        }
        LambdaUpdateWrapper<AccountOpenIdDO> updateWrapper = updateWrapper();
        updateWrapper.set(AccountOpenIdDO::getDeleted, System.currentTimeMillis());
        updateWrapper.eq(AccountOpenIdDO::getOpenId, openId);
        updateWrapper.eq(AccountOpenIdDO::getAppId, appId);
        return update(updateWrapper);
    }

    @Override
    public AccountOpenIdDO findByOpenIdAndAppId(String openId, String appId) {
        if (StringUtils.isBlank(openId) || StringUtils.isBlank(appId)) {
            return null;
        }
        LambdaQueryWrapper<AccountOpenIdDO> queryWrapper = queryWrapper();
        queryWrapper.eq(AccountOpenIdDO::getOpenId, openId);
        queryWrapper.eq(AccountOpenIdDO::getAppId, appId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public AccountOpenIdDO findByAccountIdAndAppId(Long accountId, String appId) {
        if (accountId == null || StringUtils.isBlank(appId)) {
            return null;
        }
        LambdaQueryWrapper<AccountOpenIdDO> queryWrapper = queryWrapper();
        queryWrapper.eq(AccountOpenIdDO::getAccountId, accountId);
        queryWrapper.eq(AccountOpenIdDO::getAppId, appId);
        return baseMapper.selectOne(queryWrapper);
    }

/*    @Override
    public AccountOpenIdDO findByAccountId(Long accountId) {
        if (accountId == null) {
            return null;
        }
        Page<AccountOpenIdDO> page = PageDTO.of(1, 1);
        LambdaQueryWrapper<AccountOpenIdDO> queryWrapper = queryWrapper();
        queryWrapper.eq(AccountOpenIdDO::getAccountId, accountId);
        queryWrapper.orderByDesc(AccountOpenIdDO::getId);
        page = baseMapper.selectPage(page, queryWrapper);
        if (page.getTotal() <= 0) {
            return null;
        }
        AccountOpenIdDO accountOpenIdDO = page.getRecords().get(0);
        return accountOpenIdDO;
    }*/

    @Override
    public List<AccountOpenIdDO> findAllByUnionIdList(List<String> unionIdList) {
        if (CollectionUtils.isEmpty(unionIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AccountOpenIdDO> queryWrapper = queryWrapper();
        queryWrapper.in(AccountOpenIdDO::getUnionId, unionIdList);
        queryWrapper.orderByDesc(AccountOpenIdDO::getUnionId);
        List<AccountOpenIdDO> list =  baseMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public void addBatch(List<AccountOpenIdDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        boolean b = saveBatch(list);
    }

    private LambdaQueryWrapper<AccountOpenIdDO> queryWrapper() {
        LambdaQueryWrapper<AccountOpenIdDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountOpenIdDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<AccountOpenIdDO> updateWrapper() {
        LambdaUpdateWrapper<AccountOpenIdDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AccountOpenIdDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
