<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.part.mapper.PartApplyMapper">

    <select id="findMapByFeedbackIdList" resultType="com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel">
        SELECT a.`repair_form_feedback_id` as feedback_id,
        b.`quantity` as quantity,
        b.`spare_part_batch_id` as batch_id,
        p.`name` as name,
        a.`status` as audit_status,
        a.`audit_remark` as audit_remark
        FROM `t_project_spare_part_apply` a
        LEFT JOIN `t_project_spare_part_apply_ref_part_batch` b on a.`id`= b.`spare_part_apply_id`
        LEFT JOIN `t_project_spare_part_batch` p on b.`spare_part_batch_id` = p.`id`
        WHERE
        a.`deleted`= 0
        and a.`repair_form_feedback_id` in
        <foreach collection="feedbackIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>