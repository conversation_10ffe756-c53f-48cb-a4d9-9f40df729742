package com.avatar.hospital.chaperone.component.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.avatar.hospital.chaperone.properties.OssProperties;
import com.avatar.hospital.chaperone.response.file.FileUploadResponse;
import com.avatar.hospital.chaperone.utils.FileUtils;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import static com.aliyun.oss.crypto.CryptoStorageMethod.ObjectMetadata;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/17
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OssComponent {

    private final OssProperties ossProperties;


    /**
     * 文件上传
     *
     * @param accountId -
     * @param file      -
     * @return -
     */
    public FileUploadResponse uploadFile(Long accountId, MultipartFile file) {
        try {
            String fileExtName = FileUtils.getFileExtName(file.getOriginalFilename());
            if (fileExtName == null) {
                log.warn("OssComponent uploadFile fileExtName is null fileName:{}", file.getOriginalFilename());
                return null;
            }
            String objectName = accountId + "/" + FileUtils.getRandomFileName(fileExtName);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentDisposition("attachment;filename=" + URLEncoder.encode(file.getOriginalFilename(), "UTF-8"));
            getOssClient().putObject(new PutObjectRequest(ossProperties.getBucketName(), objectName, file.getInputStream(),metadata));

            return FileUploadResponse.builder().fileUrl(getDownloadUrl(objectName)).build();
        } catch (Exception e) {
            log.error("OssComponent uploadFile error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }


    /**
     * 获取OSS客户端
     *
     * @return -
     */
    private OSS getOssClient() {
        return new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(), ossProperties.getSecretAccessKey());
    }

    /**
     * 获取文件的下载路径
     *
     * @param objectName 上传的文件路径
     * @return -
     * @throws UnsupportedEncodingException -
     */
    private String getDownloadUrl(String objectName) throws UnsupportedEncodingException {
        String url = this.getUrl();
        if (StringUtils.isNotBlank(ossProperties.getCustomDomain())) {
            url = this.getCustomUrl();
        }
        return URLDecoder.decode(url + objectName, "UTF-8");
    }

    /**
     * 拼接文件访问路径
     *
     * @return -
     */
    private String getCustomUrl() {
        return StringUtils.isBlank(ossProperties.getCustomDomain()) ? this.getUrl() : ossProperties.getCustomDomain() + "/";
    }

    /**
     * 拼接文件访问路径
     *
     * @return -
     */
    private String getUrl() {
        return "https://" + ossProperties.getBucketName() + "." + ossProperties.getEndpoint() + "/";
    }

}
