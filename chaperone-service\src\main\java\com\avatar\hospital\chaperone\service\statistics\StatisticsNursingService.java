package com.avatar.hospital.chaperone.service.statistics;

import com.avatar.hospital.chaperone.request.statistics.StatisticsRequest;
import com.avatar.hospital.chaperone.response.statistics.*;

/**
 *
 * 护工-统计信息
 *
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-10 13:39
 **/
public interface StatisticsNursingService {

    /**
     * 陪护服务时长统计
     *  本月陪护服务总时长（人天）
     *  * 通过护工-订单绑定记录表统计
     * @return
     */
    StatisticsServerTimeResponse getServerTime(StatisticsRequest request);

    /**
     * 用户评价统计
     *      本月度评价5分数量，4分数量，3分数量，2分数量，1分数量
     *          评价表,评价时间
     *       月度评分最高护工排行 top10
     *          评价表,分组
     * @return
     */
    StatisticsEstimateResponse getEstimate(StatisticsRequest request);

    /**
     * 陪护费用统计
     *      本月陪护费用金额统计，总额，应收未收
     *      总额
     *        消费记录
     *      应收未收
     *        账单未付款
     * @return
     */
    StatisticsPriceResponse getPrice(StatisticsRequest request);
}
