package com.avatar.hospital.chaperone.database.plan.repository.base;

import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import org.assertj.core.util.Sets;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/01 11:26
 */
public interface PlanRefRepositoryService {
    boolean update2delete(PlanRequest request);

    List<Long> getRefIds(Long planId);

    Set<Long> getRefIds(Set<Long> planIds);

    default Set<Long> getPlanByAccount(Long accountId) {
        return Sets.newHashSet();
    }

    default Set<Long> getPlanByOrg(Set<Long> orgIds) {
        return Sets.newHashSet();
    }

}
