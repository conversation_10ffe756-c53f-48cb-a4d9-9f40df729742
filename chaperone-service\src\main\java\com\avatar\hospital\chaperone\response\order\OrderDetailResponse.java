package com.avatar.hospital.chaperone.response.order;

import com.avatar.hospital.chaperone.response.nursing.NursingSimpleResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 16:48
 **/
@Data
public class OrderDetailResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单基本信息
     */
    private OrderResponse base;

    /**
     * 账单信息
     */
    private OrderTotalBillResponse bill;

    /**
     * 当前护工信息
     */
    @Deprecated
    private NursingSimpleResponse curNursing;


    /**
     * 预设置护工系信息
     */
    @Deprecated
    private NursingSimpleResponse preNursing;

    /**
     * 当前护工信息列表
     */
    private List<NursingSimpleResponse> curNursingList;


    /**
     * 预设置护工系信息列表
     */
    private List<NursingSimpleResponse> preNursingList;


    /**
     * 订单评价
     */
    private OrderEstimateResponse estimate;
}
