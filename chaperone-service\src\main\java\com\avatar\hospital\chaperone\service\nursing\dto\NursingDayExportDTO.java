package com.avatar.hospital.chaperone.service.nursing.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.avatar.hospital.chaperone.response.nursing.NursingDayOrderResponse;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * @author:sp0420
 * @Description:
 */
@Data
@ExcelIgnoreUnannotated
public class NursingDayExportDTO {


    @ExcelProperty(value = "id")
    private Long id;

    @ExcelProperty(value = "时间")
    private Long date;

    @ExcelProperty(value = "护工工号")
    private Long nursingId;

    @ExcelProperty(value = "护工名称")
    private String nursingName;

    @ExcelProperty(value = "班次状态")
    private String statusDescribe;

    @ExcelProperty(value = "关联陪护单")
    private String orderListToString;
}
