package com.avatar.hospital.chaperone.database.order.mapper;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderBillDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单-账单; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderBillMapper extends BaseMapper<OrderBillDO> {

    List<Long> findAllTotal(@Param("type") Integer type,@Param("subType") Integer subType);

    /**
     * 统计已经付款金额
     * @param start
     * @param end
     * @return
     */
    Long readypay(@Param("start")LocalDateTime start,
                  @Param("end")LocalDateTime end);
}
