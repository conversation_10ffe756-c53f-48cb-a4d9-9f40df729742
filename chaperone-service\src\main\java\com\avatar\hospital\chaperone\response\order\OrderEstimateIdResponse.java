package com.avatar.hospital.chaperone.response.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:06
 **/
@Data
public class OrderEstimateIdResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评价ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    public static final OrderEstimateIdResponse build(Long id) {
        OrderEstimateIdResponse obj = new OrderEstimateIdResponse();
        obj.setId(id);
        return obj;
    }

}
