package com.avatar.hospital.chaperone.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Description: 不是servlet请求的，可以在方法的入口增加TraceLog，用于生成traceId打印日志
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface TraceLog {


}
