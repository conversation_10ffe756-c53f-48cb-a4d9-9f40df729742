package com.avatar.hospital.chaperone.database.nursing.dataobject;

import com.avatar.hospital.chaperone.database.nursing.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 护工-操作记录表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_nursing_log")
public class NursingLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 操作时间
     */
      private String time;

      /**
     * 护工ID
     */
      private Long nursingId;

      /**
     * 修改事件
     */
      private String event;

      /**
     * 修改前
     */
      private String beforeValue;

      /**
     * 修改后
     */
      private String afterValue;


}
