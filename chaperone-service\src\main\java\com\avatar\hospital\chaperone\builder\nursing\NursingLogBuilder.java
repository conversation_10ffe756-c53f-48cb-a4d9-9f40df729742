package com.avatar.hospital.chaperone.builder.nursing;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingLogDO;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingLogStatus;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @author:sp0420
 * @Description:
 */
public class NursingLogBuilder {

    public static List<NursingLogDO> buildNursingLogDO(Long nursingId, Long replaceNursingId, Integer logStatus, Long updateBy) {
        List<NursingLogDO> list = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        LocalDateTime now = LocalDateTime.now();
        Date date = new Date();
        if (NursingLogStatus.REPLACE.getStatus().equals(logStatus)) {
            NursingLogDO nursingLogDO1 = new NursingLogDO();
            nursingLogDO1.setId(IdUtils.getId());
            nursingLogDO1.setTime(simpleDateFormat.format(date));
            nursingLogDO1.setNursingId(nursingId);
            nursingLogDO1.setEvent(NursingLogStatus.REPLACE.getDescribe());
            nursingLogDO1.setBeforeValue(NursingDayStatus.BUSY.getDescribe());
            nursingLogDO1.setAfterValue(NursingDayStatus.FREE.getDescribe());
            nursingLogDO1.setDeleted(DelUtils.NO_DELETED);
            nursingLogDO1.setCreateBy(updateBy);
            nursingLogDO1.setCreatedAt(now);
            nursingLogDO1.setUpdateBy(updateBy);
            nursingLogDO1.setUpdatedAt(now);

            NursingLogDO nursingLogDO2 = new NursingLogDO();
            nursingLogDO2.setId(IdUtils.getId());
            nursingLogDO2.setTime(simpleDateFormat.format(date));
            nursingLogDO2.setNursingId(replaceNursingId);
            nursingLogDO2.setEvent(NursingLogStatus.REPLACE.getDescribe());
            nursingLogDO2.setBeforeValue(NursingDayStatus.FREE.getDescribe());
            nursingLogDO2.setAfterValue(NursingDayStatus.BUSY.getDescribe());
            nursingLogDO2.setDeleted(DelUtils.NO_DELETED);
            nursingLogDO2.setCreateBy(updateBy);
            nursingLogDO2.setCreatedAt(now);
            nursingLogDO2.setUpdateBy(updateBy);
            nursingLogDO2.setUpdatedAt(now);
            list.add(nursingLogDO1);
            list.add(nursingLogDO2);
        }

        if (NursingLogStatus.LEAVE.getStatus().equals(logStatus)) {
            NursingLogDO nursingLogDO = new NursingLogDO();
            nursingLogDO.setId(IdUtils.getId());
            nursingLogDO.setTime(simpleDateFormat.format(date));
            nursingLogDO.setNursingId(nursingId);
            nursingLogDO.setEvent(NursingLogStatus.LEAVE.getDescribe());
            nursingLogDO.setBeforeValue(NursingDayStatus.BUSY.getDescribe());
            nursingLogDO.setAfterValue(NursingDayStatus.LEAVE.getDescribe());
            nursingLogDO.setDeleted(DelUtils.NO_DELETED);
            nursingLogDO.setCreateBy(updateBy);
            nursingLogDO.setCreatedAt(now);
            nursingLogDO.setUpdateBy(updateBy);
            nursingLogDO.setUpdatedAt(now);
            list.add(nursingLogDO);
        }

        if (NursingLogStatus.CANCEL_LEAVE.getStatus().equals(logStatus)) {
            NursingLogDO nursingLogDO = new NursingLogDO();
            nursingLogDO.setId(IdUtils.getId());
            nursingLogDO.setTime(simpleDateFormat.format(date));
            nursingLogDO.setNursingId(nursingId);
            nursingLogDO.setEvent(NursingLogStatus.CANCEL_LEAVE.getDescribe());
            nursingLogDO.setBeforeValue(NursingDayStatus.LEAVE.getDescribe());
            nursingLogDO.setAfterValue(NursingDayStatus.FREE.getDescribe());
            nursingLogDO.setDeleted(DelUtils.NO_DELETED);
            nursingLogDO.setCreateBy(updateBy);
            nursingLogDO.setCreatedAt(now);
            nursingLogDO.setUpdateBy(updateBy);
            nursingLogDO.setUpdatedAt(now);
            list.add(nursingLogDO);
        }

        return list;
    }
}
