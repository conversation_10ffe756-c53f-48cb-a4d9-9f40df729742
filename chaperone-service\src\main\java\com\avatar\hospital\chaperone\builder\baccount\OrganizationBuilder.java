package com.avatar.hospital.chaperone.builder.baccount;

import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationDO;
import com.avatar.hospital.chaperone.request.baccount.OrganizationAddRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationUpdateRequest;
import com.avatar.hospital.chaperone.response.baccount.OrganizationHospitalResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationTreeResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerOrganizationHospitalResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
public class OrganizationBuilder {

    public static OrganizationDO buildOrganizationDO(OrganizationAddRequest request, Integer parentLevel) {
        if (request == null) {
            return null;
        }
        OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setName(request.getName());
        organizationDO.setParentId(request.getParentId());
        organizationDO.setLiaisonName(request.getLiaisonName());
        organizationDO.setLiaisonPhoneNumber(request.getLiaisonPhoneNumber());
        organizationDO.setBankAccountNumber(request.getBankAccountNumber());
        organizationDO.setLevel(parentLevel + 1);
        organizationDO.setType(request.getType());
        organizationDO.setStatus(request.getStatus());
        organizationDO.setSort(request.getSort());
        organizationDO.setCreateBy(request.getCreateBy());
        organizationDO.setUpdateBy(request.getUpdateBy());
        return organizationDO;
    }

    public static OrganizationDO buildOrganizationDO(OrganizationUpdateRequest request) {
        if (request == null) {
            return null;
        }
        OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setId(request.getId());
        organizationDO.setName(request.getName());
        organizationDO.setLiaisonName(request.getLiaisonName());
        organizationDO.setLiaisonPhoneNumber(request.getLiaisonPhoneNumber());
        organizationDO.setBankAccountNumber(request.getBankAccountNumber());
        organizationDO.setStatus(request.getStatus());
        organizationDO.setSort(request.getSort());
        organizationDO.setUpdateBy(request.getUpdateBy());
        return organizationDO;
    }

    public static List<OrganizationTreeResponse> buildOrganizationTreeResponseList(List<OrganizationDO> organizationList) {
        if (CollectionUtils.isEmpty(organizationList)) {
            return Collections.emptyList();
        }
        List<OrganizationTreeResponse> organizationTreeResponseList = Lists.newArrayListWithCapacity(organizationList.size());
        organizationList.forEach(organization -> organizationTreeResponseList.add(buildOrganizationTreeResponse(organization)));
        return organizationTreeResponseList;
    }

    public static OrganizationTreeResponse buildOrganizationTreeResponse(OrganizationDO organization) {
        if (organization == null) {
            return null;
        }
        OrganizationTreeResponse organizationTreeResponse = new OrganizationTreeResponse();
        organizationTreeResponse.setId(organization.getId());
        organizationTreeResponse.setName(organization.getName());
        organizationTreeResponse.setParentId(organization.getParentId());
        organizationTreeResponse.setLiaisonName(organization.getLiaisonName());
        organizationTreeResponse.setLiaisonPhoneNumber(organization.getLiaisonPhoneNumber());
        organizationTreeResponse.setBankAccountNumber(organization.getBankAccountNumber());
        organizationTreeResponse.setLevel(organization.getLevel());
        organizationTreeResponse.setType(organization.getType());
        organizationTreeResponse.setStatus(organization.getStatus());
        organizationTreeResponse.setSort(organization.getSort());
        return organizationTreeResponse;
    }

    public static List<OrganizationTreeResponse> buildOrganizationTreeChildrenList(List<OrganizationTreeResponse> organizationTreeResponseList, Set<Long> organizationIds) {
        if (CollectionUtils.isEmpty(organizationTreeResponseList)) {
            return Collections.emptyList();
        }
        List<OrganizationTreeResponse> resultOrganizationTreeResponseList = Lists.newLinkedList();

        for (OrganizationTreeResponse organizationTreeResponse1 : organizationTreeResponseList) {
            // 添加顶级节点
            if (organizationIds.contains(organizationTreeResponse1.getId())) {
                resultOrganizationTreeResponseList.add(organizationTreeResponse1);
            }
            for (OrganizationTreeResponse organizationTreeResponse2 : organizationTreeResponseList) {
                if (Objects.equals(organizationTreeResponse2.getParentId(), organizationTreeResponse1.getId())) {
                    if (CollectionUtils.isEmpty(organizationTreeResponse1.getChildrenList())) {
                        organizationTreeResponse1.setChildrenList(Lists.newLinkedList());
                    }
                    organizationTreeResponse1.getChildrenList().add(organizationTreeResponse2);
                }
            }
        }
        return resultOrganizationTreeResponseList;
    }

    public static List<OrganizationTreeResponse> buildOrganizationTreeChildrenList(List<OrganizationTreeResponse> organizationTreeResponseList) {
        if (CollectionUtils.isEmpty(organizationTreeResponseList)) {
            return Collections.emptyList();
        }
        List<OrganizationTreeResponse> resultOrganizationTreeResponseList = Lists.newLinkedList();

        for (OrganizationTreeResponse organizationTreeResponse1 : organizationTreeResponseList) {
            // 添加顶级节点
            if (organizationTreeResponse1.getParentId() == null) {
                resultOrganizationTreeResponseList.add(organizationTreeResponse1);
            }
            for (OrganizationTreeResponse organizationTreeResponse2 : organizationTreeResponseList) {
                if (Objects.equals(organizationTreeResponse2.getParentId(), organizationTreeResponse1.getId())) {
                    if (CollectionUtils.isEmpty(organizationTreeResponse1.getChildrenList())) {
                        organizationTreeResponse1.setChildrenList(Lists.newLinkedList());
                    }
                    organizationTreeResponse1.getChildrenList().add(organizationTreeResponse2);
                }
            }
        }
        return resultOrganizationTreeResponseList;
    }

    public static List<OrganizationHospitalResponse> builderOrganizationHospitalResponseList(List<OrganizationDO> organizationList) {
        if (CollectionUtils.isEmpty(organizationList)) {
            return Collections.emptyList();
        }
        List<OrganizationHospitalResponse> organizationHospitalResponseList = Lists.newArrayListWithCapacity(organizationList.size());
        organizationList.forEach(organization -> organizationHospitalResponseList.add(builderOrganizationHospitalResponse(organization)));
        return organizationHospitalResponseList;
    }

    public static OrganizationHospitalResponse builderOrganizationHospitalResponse(OrganizationDO organization) {
        if (organization == null) {
            return null;
        }
        OrganizationHospitalResponse organizationHospitalResponse = new OrganizationHospitalResponse();
        organizationHospitalResponse.setId(organization.getId());
        organizationHospitalResponse.setName(organization.getName());
        organizationHospitalResponse.setParentId(organization.getParentId());
        organizationHospitalResponse.setLiaisonName(organization.getLiaisonName());
        organizationHospitalResponse.setLiaisonPhoneNumber(organization.getLiaisonPhoneNumber());
        organizationHospitalResponse.setBankAccountNumber(organization.getBankAccountNumber());
        organizationHospitalResponse.setLevel(organization.getLevel());
        organizationHospitalResponse.setStatus(organization.getStatus());
        organizationHospitalResponse.setSort(organization.getSort());
        return organizationHospitalResponse;
    }

    public static List<ConsumerOrganizationHospitalResponse> builderConsumerOrganizationHospitalResponseList(List<OrganizationDO> organizationDOList) {
        if (CollectionUtils.isEmpty(organizationDOList)) {
            return null;
        }
        List<ConsumerOrganizationHospitalResponse> consumerOrganizationHospitalResponseList = Lists.newLinkedList();
        organizationDOList.forEach(organizationDO -> {
            ConsumerOrganizationHospitalResponse consumerOrganizationHospitalResponse = new ConsumerOrganizationHospitalResponse();
            consumerOrganizationHospitalResponse.setId(organizationDO.getId());
            consumerOrganizationHospitalResponse.setName(organizationDO.getName());
            consumerOrganizationHospitalResponseList.add(consumerOrganizationHospitalResponse);
        });
        return consumerOrganizationHospitalResponseList;
    }
}
