package com.avatar.hospital.chaperone.consumer.validator.order;

import com.avatar.hospital.chaperone.database.item.enums.ItemServerType;
import com.avatar.hospital.chaperone.database.order.enums.OrderEstimateSource;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.template.util.AssertUtils;

import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 订单校验
 * @author: sp0372
 * @create: 2023-10-13 15:19
 **/
public class OrderValidator {
    /**
     * 订单创建校验
     * @param request
     */
    public static void createValidator(OrderCCreateRequest request) {
        // 这个参数C端不让传过来
        AssertUtils.isTrue(Objects.isNull(request.getAccountPhone()), ErrorCode.PARAMETER_ERROR);
        // 预设置
        request.cleanNull();
        request.setAccountPhone(request.getOperatorUser().getMobile());
        ItemServerType serverType = ItemServerType.of(request.getServerType());
        if (Objects.nonNull(serverType.getOrgId())) {
            request.setOrgId(serverType.getOrgId());
        }
    }

    public static void pagingValidator(OrderCPageRequest request) {

    }

    public static void pagingForConsumerlog(OrderConsumerlogCPageRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getOrderId(), ErrorCode.PARAMETER_ERROR);
        if (Objects.nonNull(request.getMonth())) {
            Integer month = request.getMonth();
            request.setStartTime(month * 100 + 1);
            request.setEndTime(month * 100 + 31);
        }
    }

    public static void createEstimateValidator(OrderEstimateSaveRequest request) {
        request.setSource(OrderEstimateSource.USER.getStatus());
    }
}
