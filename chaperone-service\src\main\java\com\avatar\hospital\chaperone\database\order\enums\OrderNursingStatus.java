package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import lombok.Getter;

import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderNursingStatus implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    NORMAL(1, "有效"),
    INVALID(0, "无效"),
    ;

    private final Integer status;

    private final String describe;


    OrderNursingStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderNursingStatus of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderNursingStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        OrderNursingStatus e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }
}
