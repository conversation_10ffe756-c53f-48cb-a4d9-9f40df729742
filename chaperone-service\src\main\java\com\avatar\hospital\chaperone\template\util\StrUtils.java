package com.avatar.hospital.chaperone.template.util;

import com.avatar.hospital.chaperone.utils.CollUtils;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
public class StrUtils {

    public static final String EMPTY = "";

    public static boolean hasText(String str) {
        return (str != null && !str.isEmpty() && containsText(str));
    }

    private static boolean containsText(CharSequence str) {
        int strLen = str.length();
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return true;
            }
        }
        return false;
    }

    public static String toStr(Long itemId) {
        if (Objects.isNull(itemId)) {
            return EMPTY;
        }
        return String.valueOf(itemId);
    }


    /**
     * list<long> -> str
     * @param list
     * @return
     */
    public static final String longListToStr(List<Long> list) {
        if (CollUtils.isEmpty(list)) {
            return EMPTY;
        }
        String str = Joiner.on(",").join(list);
        return str;
    }

    /**
     * str -> list<long>
     * @param str
     * @return
     */
    public static final List<Long> strToListLong(String str) {
        if (!StrUtils.hasText(str)) {
            return new ArrayList<>();
        }
        List<Long> idList = Splitter.on(",").splitToStream(str)
                .map(id -> Long.valueOf(id))
                .collect(Collectors.toList());
        return idList;
    }
}
