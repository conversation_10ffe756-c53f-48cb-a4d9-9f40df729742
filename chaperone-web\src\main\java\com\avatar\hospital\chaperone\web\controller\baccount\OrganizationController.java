package com.avatar.hospital.chaperone.web.controller.baccount;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.OrganizationAddRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationHospitalRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationTreeRequest;
import com.avatar.hospital.chaperone.request.baccount.OrganizationUpdateRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAdministrativeOfficeQueryRequest;
import com.avatar.hospital.chaperone.response.baccount.OrganizationAddResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationHospitalResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationTreeResponse;
import com.avatar.hospital.chaperone.response.baccount.OrganizationUpdateResponse;
import com.avatar.hospital.chaperone.service.baccount.OrganizationService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import com.avatar.hospital.chaperone.web.validator.baccount.OrganizationValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description: 组织机构接口
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/organization")
public class OrganizationController {

    private final OrganizationService organizationService;


    /**
     * 添加组织机构
     *
     * @param request -
     * @return -
     */
    @PostMapping(value = "")
    public SingleResponse<OrganizationAddResponse> add(@RequestBody OrganizationAddRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("OrganizationController add request:{}", JSON.toJSONString(request));
            OrganizationValidator.addValidate(request);
            return organizationService.add(request);
        });
    }

    /**
     * 更新组织机构
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "")
    public SingleResponse<OrganizationUpdateResponse> update(@RequestBody OrganizationUpdateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("OrganizationController update request:{}", JSON.toJSONString(request));
            OrganizationValidator.updateValidate(request);
            return organizationService.update(request);
        });
    }

    /**
     * 删除组织机构
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "/delete")
    public SingleResponse<OrganizationDeleteResponse> delete(@RequestBody OrganizationDeleteRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("OrganizationController delete request:{}", JSON.toJSONString(request));
            OrganizationValidator.deleteValidate(request);
            return organizationService.delete(request);
        });
    }

    /**
     * 查询组织机构树形结构
     * <p>1.如果是管理员返回所有组织机构</p>
     * <p>2.如果用户有查看组织机构树的角色，返回所有组织机构</p>
     * <p>3.根据当前用户所在的医院&部门层级查询 只能看到自己所在的医院&部门层级和以下节点</p>
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/account-tree")
    public SingleResponse<List<OrganizationTreeResponse>> accountTree(@RequestBody(required = false) OrganizationTreeRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrganizationTreeRequest newRequest = request;
            if (request == null) {
                newRequest = new OrganizationTreeRequest();
            }
            // 设置当前登录用户
            Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
            newRequest.setAccountId(accountId);
            log.info("OrganizationController accountTree request:{}", JSON.toJSONString(newRequest));
            OrganizationValidator.accountTreeValidate(newRequest);
            return organizationService.accountTree(newRequest);
        });
    }

    /**
     * 查询医院层级组织机构列表
     * <p>1.如果是管理员返回所有医院层级列表</p>
     * <p>2.如果用户有查看所有医院层级的角色，返回所有医院层级列表</p>
     * <p>3.根据当前用户所在的医院层级查询 只能看到自己所在的医院层级</p>
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/hospital-organization-list")
    public SingleResponse<List<OrganizationHospitalResponse>> hospitalOrganizationList(@RequestBody(required = false) OrganizationHospitalRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrganizationHospitalRequest newRequest = request;
            if (request == null) {
                newRequest = new OrganizationHospitalRequest();
            }
            // 设置当前登录用户
            Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
            newRequest.setAccountId(accountId);
            log.info("OrganizationController hospitalOrganizationList request:{}", JSON.toJSONString(newRequest));
            OrganizationValidator.hospitalOrganizationListValidate(newRequest);
            return organizationService.hospitalOrganizationList(newRequest);
        });
    }

    /**
     * 查询部门层级组织机构列表
     * <p>1.如果是管理员返回所有部门层级列表</p>
     * <p>2.如果用户有查看所有部门层级的角色，返回所有部门层级列表</p>
     * <p>3.根据当前用户所在的部门层级查询 只能看到自己所在的部门层级</p>
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/department-organization-list")
    public SingleResponse<List<OrganizationHospitalResponse>> departmentOrganizationList(@RequestBody(required = false) OrganizationHospitalRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            OrganizationHospitalRequest newRequest = request;
            if (request == null) {
                newRequest = new OrganizationHospitalRequest();
            }
            // 设置当前登录用户
            Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
            newRequest.setAccountId(accountId);
            log.info("OrganizationController departmentOrganizationList request:{}", JSON.toJSONString(newRequest));
            OrganizationValidator.hospitalOrganizationListValidate(newRequest);
            return organizationService.departmentOrganizationList(newRequest);
        });
    }

    /**
     * B端根据医院ID查询科室列表
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/administrative-office-organization-list")
    public SingleResponse<List<OrganizationHospitalResponse>> webAdministrativeOfficeOrganizationList(WebAdministrativeOfficeQueryRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("OrganizationController webAdministrativeOfficeOrganizationList request:{}", JSON.toJSONString(request));
            AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
            AssertUtils.isNotNull(request.getHospitalOrganizationId(), ErrorCode.PARAMETER_ERROR);
            return organizationService.webAdministrativeOfficeOrganizationList(request);
        });
    }

}
