package com.avatar.hospital.chaperone.database.part.dataobject;

import com.avatar.hospital.chaperone.database.part.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 备品备件入库审批单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_spare_part_stock_apply")
public class PartStockApplyDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 审批单编号
     */
    private String code;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态（1-通过，2-驳回，0-待审批）
     *
     * @see com.avatar.hospital.chaperone.database.part.enums.PartStockApplyStatusType
     */
    private Integer status;

    /**
     * 名称（物品名称+数量）
     */
    private String name;

    /**
     * 出资方类型（1-物业，2-医院）
     */
    private Integer investorType;

    /**
     * 出资方机构ID
     */
    private Long orgId;

    /**
     * 出资方机构名称
     */
    private String investor;

    /**
     * 附件URL,数组
     */
    private String attachments;

    /**
     * 入库时间
     */
    private LocalDateTime entryTime;

    /**
     * 审核人员ID
     */
    private Long auditAccountId;

    /**
     * 审核完成时间
     */
    private LocalDateTime auditCompletedTime;


}
