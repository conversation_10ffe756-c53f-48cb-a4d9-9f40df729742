package com.avatar.hospital.chaperone.database.caccount.repository;

import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountOpenIdDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * C端用户关联三方用户openId表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface AccountOpenIdRepositoryService extends IService<AccountOpenIdDO> {

    /**
     * 添加数据
     *
     * @param accountOpenIdDO -
     * @return -
     */
    Boolean add(AccountOpenIdDO accountOpenIdDO);

    /**
     * 根据openId&appId删除数据
     *
     * @param openId -
     * @param appId  -
     * @return -
     */
    Boolean deleteByOpenIdAndAppId(String openId, String appId);

    /**
     * 根据openId&appId查询
     *
     * @param openId -
     * @param appId  -
     * @return -
     */
    AccountOpenIdDO findByOpenIdAndAppId(String openId, String appId);

    /**
     * 根据accountId&appId查询
     *
     * @param accountId -
     * @param appId     -
     * @return -
     */
    AccountOpenIdDO findByAccountIdAndAppId(Long accountId, String appId);

/*    *//**
     * 根据accountId&appId查询
     *
     * @param accountId -
     * @return -
     *//*
    AccountOpenIdDO findByAccountId(Long accountId);*/

    List<AccountOpenIdDO> findAllByUnionIdList(List<String> unionIdList);

    void addBatch(List<AccountOpenIdDO> addList);
}
