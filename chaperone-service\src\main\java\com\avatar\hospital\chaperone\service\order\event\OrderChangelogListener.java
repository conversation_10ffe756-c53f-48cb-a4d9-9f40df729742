package com.avatar.hospital.chaperone.service.order.event;

import com.avatar.hospital.chaperone.request.order.OrderChangelogSaveRequest;
import com.avatar.hospital.chaperone.service.order.OrderChangelogService;
import com.avatar.hospital.chaperone.service.order.dto.OrderChangeLogDTO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.utils.CompareDTO;
import com.avatar.hospital.chaperone.utils.CompareUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

/**
 * @program: hospital-chaperone
 * @description: 订单修改事件
 * @author: sp0372
 * @create: 2023-10-12 15:05
 **/
@Slf4j
@Service
@RequiredArgsConstructor
@EnableAsync
public class OrderChangelogListener implements ApplicationListener<OrderChangelogEvent> {
    private final OrderChangelogService orderChangelogService;
    @Async
    @Override
    public void onApplicationEvent(OrderChangelogEvent event) {
        OrderChangelogEvent.Holder holder = event.getHolder();
        OrderChangeLogDTO.AbsCompareDTO before = holder.getBefore();
        OrderChangeLogDTO.AbsCompareDTO after = holder.getAfter();
        Class<CompareDTO> clazz = holder.getClazz();
        Operator operator = holder.getOperatorUser();
        // 对比 记录
        String[] compareResult = CompareUtils.compareToStr(before, after, clazz);
        log.info("OrderChangelogListener:{}",compareResult);
        orderChangelogService.add(OrderChangelogSaveRequest.build(after.getOrderId(),operator,holder.getEvent(),compareResult[0],compareResult[1]));
    }
}
