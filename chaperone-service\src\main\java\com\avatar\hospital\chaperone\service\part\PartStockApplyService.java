package com.avatar.hospital.chaperone.service.part;

import com.avatar.hospital.chaperone.request.part.PartStockApplyAuditRequest;
import com.avatar.hospital.chaperone.request.part.PartStockApplyRequest;
import com.avatar.hospital.chaperone.request.part.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartStockApplyVO;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 18:06
 */
public interface PartStockApplyService {
    Long create(PartStockApplyRequest request);

    Boolean update(PartStockApplyRequest request);

    PageResponse<PartStockApplyVO> paging(QueryRequest request);

    PartStockApplyVO detail(Long id);

    Boolean audit(PartStockApplyAuditRequest request);
}
