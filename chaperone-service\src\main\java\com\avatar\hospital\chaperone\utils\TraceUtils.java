package com.avatar.hospital.chaperone.utils;


import com.avatar.hospital.chaperone.constant.TraceConstant;
import org.slf4j.MDC;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
public class TraceUtils {

    public static void setTraceId(ServletRequest request, ServletResponse response) {
        clear();
        String traceId = "";
        if (request instanceof HttpServletRequest) {
            traceId = generateTraceId();
            put(TraceConstant.TRACE_ID, traceId);
        }
        if (response instanceof HttpServletResponse) {
            HttpServletResponse httpServletResponse = (HttpServletResponse) response;
            httpServletResponse.setHeader(TraceConstant.HTTP_HEADER_NAME, traceId);
        }
    }

    public static void setTraceId() {
        if (get(TraceConstant.TRACE_ID) != null) {
            return;
        }
        put(TraceConstant.TRACE_ID, generateTraceId());
    }

    public static String getTranceId() {
        return get(TraceConstant.TRACE_ID);
    }

    public static void put(String key, String value) {
        MDC.put(key, value);
    }

    public static String get(String key) {
        return MDC.get(key);
    }

    public static void remove(String key) {
        MDC.remove(key);
    }

    public static void clear() {
        MDC.clear();
    }

    public static String generateTraceId() {
        return TraceConstant.PREFIX_TRACE_ID + uuid();
    }

    private static String uuid() {
        return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
    }

}
