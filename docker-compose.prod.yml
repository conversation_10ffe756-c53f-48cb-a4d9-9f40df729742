version: '3.8'

services:
  # MySQL 数据库 - 生产环境配置
  mysql:
    image: mysql:8.0
    container_name: hospital-chaperone-mysql-prod
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-hospital_chaperone}
      MYSQL_USER: ${MYSQL_USER:-chaperone}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      TZ: Asia/Shanghai
    ports:
      - "127.0.0.1:${MYSQL_PORT:-3306}:3306"  # 仅本地访问
    volumes:
      - mysql_data_prod:/var/lib/mysql
      - ./document/sql:/docker-entrypoint-initdb.d:ro
      - ./docker/mysql/conf.d:/etc/mysql/conf.d:ro
      - mysql_logs:/var/log/mysql
    networks:
      - chaperone-network-prod
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Redis 缓存 - 生产环境配置
  redis:
    image: redis:7-alpine
    container_name: hospital-chaperone-redis-prod
    restart: always
    environment:
      TZ: Asia/Shanghai
    ports:
      - "127.0.0.1:${REDIS_PORT:-6379}:6379"  # 仅本地访问
    volumes:
      - redis_data_prod:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf:ro
    networks:
      - chaperone-network-prod
    command: redis-server /etc/redis/redis.conf --requirepass ${REDIS_PASSWORD:-redis123456}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-redis123456}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # 后台管理系统 - 生产环境配置
  chaperone-web:
    build:
      context: .
      dockerfile: Dockerfile.web
    image: hospital-chaperone-web:${VERSION:-latest}
    container_name: hospital-chaperone-web-prod
    restart: always
    environment:
      ENV: prod
      DB_MYSQL_URL: mysql
      DB_REDIS_URL: redis
      DB_REDIS_PORT: 6379
      MYSQL_DATABASE: ${MYSQL_DATABASE:-hospital_chaperone}
      MYSQL_USER: ${MYSQL_USER:-chaperone}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123456}
      TZ: Asia/Shanghai
      JAVA_OPTS: ${JAVA_OPTS_WEB:--Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200}
    ports:
      - "127.0.0.1:${WEB_PORT:-8081}:8081"  # 仅本地访问
    volumes:
      - web_logs_prod:/app/logs
      - ./docker/web/config:/app/config:ro
    networks:
      - chaperone-network-prod
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3

  # 客户端系统 - 生产环境配置
  chaperone-consumer:
    build:
      context: .
      dockerfile: Dockerfile.consumer
    image: hospital-chaperone-consumer:${VERSION:-latest}
    container_name: hospital-chaperone-consumer-prod
    restart: always
    environment:
      ENV: prod
      DB_MYSQL_URL: mysql
      DB_REDIS_URL: redis
      DB_REDIS_PORT: 6379
      MYSQL_DATABASE: ${MYSQL_DATABASE:-hospital_chaperone}
      MYSQL_USER: ${MYSQL_USER:-chaperone}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123456}
      TZ: Asia/Shanghai
      JAVA_OPTS: ${JAVA_OPTS_CONSUMER:--Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200}
    ports:
      - "127.0.0.1:${CONSUMER_PORT:-8080}:8080"  # 仅本地访问
    volumes:
      - consumer_logs_prod:/app/logs
      - ./docker/consumer/config:/app/config:ro
    networks:
      - chaperone-network-prod
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3

  # Nginx 反向代理 - 生产环境配置
  nginx:
    image: nginx:alpine
    container_name: hospital-chaperone-nginx-prod
    restart: always
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs_prod:/var/log/nginx
    networks:
      - chaperone-network-prod
    depends_on:
      chaperone-web:
        condition: service_healthy
      chaperone-consumer:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # 日志收集器 (可选)
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: hospital-chaperone-filebeat-prod
    restart: always
    user: root
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./docker/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - web_logs_prod:/var/log/web:ro
      - consumer_logs_prod:/var/log/consumer:ro
      - nginx_logs_prod:/var/log/nginx:ro
      - mysql_logs:/var/log/mysql:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - chaperone-network-prod
    depends_on:
      - chaperone-web
      - chaperone-consumer
    profiles:
      - monitoring

volumes:
  mysql_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/hospital-chaperone}/mysql
  redis_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/hospital-chaperone}/redis
  web_logs_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOGS_PATH:-/var/log/hospital-chaperone}/web
  consumer_logs_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOGS_PATH:-/var/log/hospital-chaperone}/consumer
  nginx_logs_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOGS_PATH:-/var/log/hospital-chaperone}/nginx
  mysql_logs:
    driver: local

networks:
  chaperone-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
