package com.avatar.hospital.chaperone.database.part.dataobject.model;


import lombok.Data;


import java.io.Serializable;

/**
 * <p>
 * 备件使用申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
public class PartApplyModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 反馈ID
     */
    private Long feedbackId;

    /**
     * 备件批次ID
     */
    private Long partBatchId;

    /**
     * 备件数量
     */
    private Integer quantity;

    /**
     * 备件名称
     */
    private String name;

    /**
     * 审核状态（1-待审核，2-允许使用，3-不允许使用）
     */
    private Integer auditStatus;

    /**
     * 审核说明
     */
    private String auditRemark;
}
