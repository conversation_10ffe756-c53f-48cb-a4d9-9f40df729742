package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

/**
 * @program: hospital-chaperone
 * @description: c端列表
 * @author: sp0372
 * @create: 2023-10-11 16:50
 **/
@Data
public class OrderCPageRequest extends PageRequest implements OperatorReq {

    /**
     *  0(不传) 全部 1 陪护中 2 已结算
     */
    private Integer frontStatus;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
