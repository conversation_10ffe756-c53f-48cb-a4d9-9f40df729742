package com.avatar.hospital.chaperone.database.plan.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/26 19:55
 */
@Getter
public enum PlanStatusType {
    NONE(-1, "未知"),
    VALID(1, "生效"),
    ABANDON(2, "作废"),
    ;

    private final Integer code;

    private final String describe;


    PlanStatusType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static PlanStatusType of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (PlanStatusType type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
