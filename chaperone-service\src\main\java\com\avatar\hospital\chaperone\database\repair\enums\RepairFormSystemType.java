package com.avatar.hospital.chaperone.database.repair.enums;

import lombok.Getter;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 13:23
 **/
@Getter
public enum RepairFormSystemType {


    /**
     * 水系统
     */
    ONE(1, "水系统"),

    /**
     * 强电系统
     */
    TWO(2, "强电系统"),

    /**
     * 弱电系统
     */
    THREE(3, "弱电系统"),

    /**
     * 气路系统
     */
    FOUR(4, "气路系统"),

    /**
     * 工程质量投诉
     */
    FIVE(5, "工程质量投诉"),

    /**
     * 其他
     */
    SIX(6, "其他"),

    /**
     * 暖通系统
     */
    SEVEN(7, "暖通系统"),

    /**
     * 建筑系统
     */
    EIGHT(8, "建筑系统"),

    /**
     * 消防系统
     */
    NINE(9, "消防系统"),


    ;

    private final Integer type;

    private final String describe;


    RepairFormSystemType(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }

    public static RepairFormSystemType of(Integer status) {
        if (status == null) {
            return null;
        }
        for (RepairFormSystemType repairFormSystemType : values()) {
            if (status.equals(repairFormSystemType.getType())) {
                return repairFormSystemType;
            }
        }
        return null;
    }
}
