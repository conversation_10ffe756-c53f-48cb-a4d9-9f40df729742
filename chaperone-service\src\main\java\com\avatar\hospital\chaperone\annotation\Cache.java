package com.avatar.hospital.chaperone.annotation;

import com.avatar.hospital.chaperone.enums.ErrorCode;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 缓存
 * <AUTHOR>
 * @since 2023/10/13
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface Cache {

    /**
     * key
     *
     * @return
     */
    String value();

    /**
     * 过期时间 默认30秒
     * @return
     */
    long expire() default 30;
}
