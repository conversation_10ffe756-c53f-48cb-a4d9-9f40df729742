package com.avatar.hospital.chaperone.service.order.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.avatar.hospital.chaperone.database.order.enums.OrderInvoiceNeedCertificate;
import com.avatar.hospital.chaperone.database.order.enums.OrderInvoiceType;
import com.avatar.hospital.chaperone.utils.ExportUtil;
import lombok.Data;

/**
 * @program: hospital-chaperone
 * @description: 账单导出
 * @author: sp0372
 * @create: 2023-10-24 10:29
 **/
@Data
@ExcelIgnoreUnannotated
public class OrderBillExportDTO {
    @ExcelProperty(value = "陪护单号")
    private Long orderId;

    @ExcelProperty(value = "账单编号")
    private Long id;

    @ExcelProperty(value = "账单类型",converter = ExportUtil.BillType.class)
    private Integer billType;

    @ExcelProperty(value = "周期账单类型",converter = ExportUtil.BillSubType.class)
    private Integer billSubType;

    @ExcelProperty(value = "支付状态",converter = ExportUtil.PayStatus.class)
    private Integer payStatus;

    @ExcelProperty(value = "账单金额",converter = ExportUtil.Amount.class)
    private Integer price;

    @ExcelProperty(value = "账单产生时间")
    private String createTime;

    @ExcelProperty(value = "病人姓名")
    private String patientName;

    @ExcelProperty(value = "账单开始时间")
    private String startTime;

    @ExcelProperty(value = "账单结束时间")
    private String endTime;

    @ExcelProperty(value = "护工")
    private String nursingInfo;

    @ExcelProperty(value = "账单支付方式",converter = ExportUtil.TradeType.class)
    private Integer tradeType;

    @ExcelProperty(value = "支付时间",converter = ExportUtil.PayTimeStr.class)
    private String payTime;

    @ExcelProperty(value = "账单包含服务名称")
    private String itemNames;

    /**
     * 是否需要付费凭证,0 不需要，1 需要
     * @see OrderInvoiceNeedCertificate
     */
    @ExcelProperty(value = "是否需要发票",converter = ExportUtil.InvoiceNeedCertificate.class)
    private Integer needCertificate;

    /**
     * 类型: 1 电子收据 2 电子发票 3 实体收据
     * @see OrderInvoiceType
     */
    @ExcelProperty(value = "发票类型",converter = ExportUtil.InvoiceType.class)
    private Integer invoiceType;

    /**
     * 电子邮箱
     */
    @ExcelProperty(value = "电子邮箱")
    private String email;

    /**
     * 纳税人名称
     */
    @ExcelProperty(value = "纳税人名称")
    private String taxpayerName;

    /**
     * 纳税人识别号
     */
    @ExcelProperty(value = "纳税人识别号")
    private String taxpayerNo;

}
