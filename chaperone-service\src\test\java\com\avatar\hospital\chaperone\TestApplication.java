package com.avatar.hospital.chaperone;

import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.annotation.MapperScans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/7
 */
@ComponentScan("com.avatar.hospital.chaperone")
@MapperScans(value = {
        @MapperScan(value = "com.avatar.hospital.chaperone.database.*.mapper"),

})
@SpringBootApplication
public class TestApplication {

    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }

}
