package com.avatar.hospital.chaperone.database.baccount.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleMenuDO;
import com.avatar.hospital.chaperone.database.baccount.mapper.RoleMenuMapper;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleMenuRepositoryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色菜单关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Service
public class RoleMenuRepositoryServiceImpl extends ServiceImpl<RoleMenuMapper, RoleMenuDO> implements RoleMenuRepositoryService {

    @Override
    public List<RoleMenuDO> findByRoleIds(Set<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<RoleMenuDO> queryWrapper = queryWrapper();
        if (roleIds.size() == 1) {
            queryWrapper.eq(RoleMenuDO::getRoleId, roleIds.toArray()[0]);
        }
        else {
            queryWrapper.in(RoleMenuDO::getRoleId, roleIds);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<RoleMenuDO> findByMenuIds(Set<Long> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<RoleMenuDO> queryWrapper = queryWrapper();
        if (menuIds.size() == 1) {
            queryWrapper.eq(RoleMenuDO::getMenuId, menuIds.toArray()[0]);
        }
        else {
            queryWrapper.in(RoleMenuDO::getMenuId, menuIds);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public Boolean deleteByRoleIds(Set<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        LambdaUpdateWrapper<RoleMenuDO> updateWrapper = updateWrapper();
        if (roleIds.size() == 1) {
            updateWrapper.eq(RoleMenuDO::getRoleId, roleIds.toArray()[0]);
        }
        else {
            updateWrapper.in(RoleMenuDO::getRoleId, roleIds);
        }
        return remove(updateWrapper);
    }

    @Override
    public Boolean deleteByMenuIds(Set<Long> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return false;
        }
        LambdaUpdateWrapper<RoleMenuDO> updateWrapper = updateWrapper();
        if (menuIds.size() == 1) {
            updateWrapper.eq(RoleMenuDO::getMenuId, menuIds.toArray()[0]);
        }
        else {
            updateWrapper.in(RoleMenuDO::getMenuId, menuIds);
        }
        return remove(updateWrapper);
    }

    private LambdaQueryWrapper<RoleMenuDO> queryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    private LambdaUpdateWrapper<RoleMenuDO> updateWrapper() {
        return new LambdaUpdateWrapper<>();
    }
}
