package com.avatar.hospital.chaperone.builder.plan;

import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanTaskDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.PatrolPlanTaskDO;
import com.avatar.hospital.chaperone.response.plan.PlanTaskVO;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 10:52
 */
public class MaintenancePlanTaskBuilder {

    public static PlanTaskVO build(MaintenancePlanTaskDO taskDO) {
        PlanTaskVO planVO = new PlanTaskVO();
        BeanUtils.copyProperties(taskDO, planVO);
        planVO.setPlanId(taskDO.getPlanId());
        return planVO;

    }


}
