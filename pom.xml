<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.avatar.hospital</groupId>
    <artifactId>hospital-chaperone</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>hospital-chaperone</name>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.7.6</spring-boot.version>

        <chaperone-web.version>1.0.0-SNAPSHOT</chaperone-web.version>
        <chaperone-consumer.version>1.0.0-SNAPSHOT</chaperone-consumer.version>
        <chaperone-service.version>1.0.0-SNAPSHOT</chaperone-service.version>
        <chaperone-web-start.version>1.0.0-SNAPSHOT</chaperone-web-start.version>
        <chaperone-consumer-start.version>1.0.0-SNAPSHOT</chaperone-consumer-start.version>

        <mysql.version>8.0.15</mysql.version>
        <druid.version>1.1.22</druid.version>
        <mybatis-plus-boot-starter.version>3.5.2</mybatis-plus-boot-starter.version>
        <mybatis-plus-generator.version>3.5.2</mybatis-plus-generator.version>
        <velocity.version>1.7</velocity.version>
        <freemarker.version>2.3.29</freemarker.version>

        <commons-pool2.version>2.11.1</commons-pool2.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-io.version>2.11.0</commons-io.version>
        <commons-collections.version>3.2.2</commons-collections.version>

        <guava.version>31.0.1-jre</guava.version>
        <fastjson.version>1.2.83</fastjson.version>
        <cola-component-dto.version>4.3.2</cola-component-dto.version>

        <redisson.version>3.20.1</redisson.version>

        <sa-token-spring-boot-starter.version>1.36.0</sa-token-spring-boot-starter.version>
        <sa-token-redis.version>1.36.0</sa-token-redis.version>

        <wx-java-miniapp-spring-boot-starter.version>4.5.0</wx-java-miniapp-spring-boot-starter.version>
        <wx-java-mp-spring-boot-starter.version>4.5.0</wx-java-mp-spring-boot-starter.version>
        <wx-java-pay-spring-boot-starter.version>4.5.0</wx-java-pay-spring-boot-starter.version>

        <aliyun-sdk-oss.version>3.15.1</aliyun-sdk-oss.version>

        <easyexcel.version>3.2.1</easyexcel.version>
        <xmlbeans.version>2.6.0</xmlbeans.version>
        <poi-ooxml-schemas.version>4.1.2</poi-ooxml-schemas.version>
        <qr.version>3.4.1</qr.version>
    </properties>

    <modules>
        <module>chaperone-web-start</module>
        <module>chaperone-web</module>
        <module>chaperone-service</module>
        <module>chaperone-consumer</module>
        <module>chaperone-consumer-start</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>private-release</id>
            <url>http://nexus02.supaur.cn/repository/private-release/</url>
        </repository>
        <snapshotRepository>
            <id>private-snapshot</id>
            <url>http://nexus02.supaur.cn/repository/private-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.avatar.hospital</groupId>
                <artifactId>chaperone-web-start</artifactId>
                <version>${chaperone-web-start.version}</version>
            </dependency>
            <dependency>
                <groupId>com.avatar.hospital</groupId>
                <artifactId>chaperone-web</artifactId>
                <version>${chaperone-web.version}</version>
            </dependency>
            <dependency>
                <groupId>com.avatar.hospital</groupId>
                <artifactId>chaperone-consumer</artifactId>
                <version>${chaperone-consumer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.avatar.hospital</groupId>
                <artifactId>chaperone-consumer-start</artifactId>
                <version>${chaperone-consumer-start.version}</version>
            </dependency>
            <dependency>
                <groupId>com.avatar.hospital</groupId>
                <artifactId>chaperone-service</artifactId>
                <version>${chaperone-service.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-dto</artifactId>
                <version>${cola-component-dto.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${sa-token-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa-token-redis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
                <version>${wx-java-miniapp-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-mp-spring-boot-starter</artifactId>
                <version>${wx-java-mp-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-pay-spring-boot-starter</artifactId>
                <version>${wx-java-pay-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>

            <!-- easyExcel -->

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi-ooxml-schemas.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>ooxml-schemas</artifactId>
                <version>${poi-ooxml-schemas.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi-ooxml-schemas.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi-ooxml-schemas.version}</version>
            </dependency>
            <!-- 生成二维码核心组件 -->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${qr.version}</version>
            </dependency>
            <!-- 非web应用无需导入javase依赖包 -->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${qr.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
