package com.avatar.hospital.chaperone.request.repair;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:50
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormPageCRequest extends PageRequest implements OperatorReq {

    /**
     * 状态（1-未指派，2-已指派，3-未完成，4-待审核，5-已完成）
     */
    private Integer status;

    /**
     * 操作用户
     */
    private Operator operatorUser;
}
