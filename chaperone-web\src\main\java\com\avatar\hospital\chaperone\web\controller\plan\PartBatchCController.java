package com.avatar.hospital.chaperone.web.controller.plan;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.part.PartBatchForCPageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.part.PartBatchVO;
import com.avatar.hospital.chaperone.service.part.PartBatchService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * C端工程-备件批次(web)
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-30 14:26
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/c/part/bach")
public class PartBatchCController {

    private final PartBatchService partBatchService;


    /**
     * 查询-分页
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<PartBatchVO>> paging(@RequestBody PartBatchForCPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return partBatchService.pagingForC(request);
        });
    }


}
