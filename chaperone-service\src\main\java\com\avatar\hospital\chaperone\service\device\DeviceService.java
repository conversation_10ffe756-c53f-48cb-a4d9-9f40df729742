package com.avatar.hospital.chaperone.service.device;

import com.avatar.hospital.chaperone.request.device.DeviceAddRequest;
import com.avatar.hospital.chaperone.request.device.DeviceCPagingRequest;
import com.avatar.hospital.chaperone.request.device.DevicePagingRequest;
import com.avatar.hospital.chaperone.request.device.DeviceUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.device.DeviceAddResponse;
import com.avatar.hospital.chaperone.response.device.DevicePagingResponse;
import com.avatar.hospital.chaperone.response.device.DeviceUpdateResponse;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/31 10:16
 */
public interface DeviceService {
    Map<Long, String> findDeviceMap(Set<Long> deviceIds);

    DeviceAddResponse add(DeviceAddRequest request);

    DeviceUpdateResponse update(DeviceUpdateRequest request);

    PageResponse<DevicePagingResponse> paging(DevicePagingRequest request);

    /**
     * 分页获取所有设备
     * @param request
     * @return
     */
    PageResponse<DevicePagingResponse> pagingForC(DeviceCPagingRequest request);
}
