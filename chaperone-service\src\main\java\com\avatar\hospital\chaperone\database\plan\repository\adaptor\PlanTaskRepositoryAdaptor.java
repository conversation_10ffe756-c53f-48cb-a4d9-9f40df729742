package com.avatar.hospital.chaperone.database.plan.repository.adaptor;

import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanTaskDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.repository.MaintenancePlanTaskRepositoryService;
import com.avatar.hospital.chaperone.database.plan.repository.PatrolPlanTaskRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.TaskExecuteRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 巡检计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Component
public class PlanTaskRepositoryAdaptor {

    @Autowired
    public PatrolPlanTaskRepositoryService patrolPlanTaskRepositoryService;
    @Autowired
    public MaintenancePlanTaskRepositoryService maintenancePlanTaskRepositoryService;


    public boolean saveBatch(PlanType planType, List planTasks) {

        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanTaskRepositoryService.saveBatch(planTasks);

        }
        return maintenancePlanTaskRepositoryService.saveBatch(planTasks);
    }

    public List list(PlanType planType, LambdaQueryWrapper queryWrapper) {

        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanTaskRepositoryService.list(queryWrapper);

        }
        return maintenancePlanTaskRepositoryService.list(queryWrapper);
    }

    public Page page(PlanType planType, Page page, LambdaQueryWrapper queryWrapper) {


        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanTaskRepositoryService.page(page, queryWrapper);

        }
        return maintenancePlanTaskRepositoryService.page(page, queryWrapper);
    }

    public PlanTaskDO getById(PlanType planType, Long id) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanTaskRepositoryService.getById(id);

        }
        return maintenancePlanTaskRepositoryService.getById(id);
    }

    public boolean update(PlanType planType, LambdaUpdateWrapper updateWrapper) {

        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanTaskRepositoryService.update(updateWrapper);

        }
        return maintenancePlanTaskRepositoryService.update(updateWrapper);
    }

    public LambdaQueryWrapper queryWrapper(PlanType planType) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanTaskRepositoryService.queryWrapper();

        }
        return maintenancePlanTaskRepositoryService.queryWrapper();
    }

    public boolean update2delete(PlanRequest request) {
        if (PlanType.PATROL.equals(request.getPlanType())) {
            return patrolPlanTaskRepositoryService.update2delete(request);

        }
        return maintenancePlanTaskRepositoryService.update2delete(request);
    }

    public boolean execute(TaskExecuteRequest request) {
        if (PlanType.PATROL.equals(request.getPlanType())) {
            return patrolPlanTaskRepositoryService.execute(request);

        }
        return maintenancePlanTaskRepositoryService.execute(request);
    }

    public boolean expired(PlanType planType, List<Long> taskIds) {
        if (PlanType.PATROL.equals(planType)) {
            return patrolPlanTaskRepositoryService.expired(taskIds);

        }
        return maintenancePlanTaskRepositoryService.expired(taskIds);
    }

}


