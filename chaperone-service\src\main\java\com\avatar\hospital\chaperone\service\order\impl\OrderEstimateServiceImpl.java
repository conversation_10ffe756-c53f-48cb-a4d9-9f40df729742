package com.avatar.hospital.chaperone.service.order.impl;

import com.avatar.hospital.chaperone.builder.order.OrderBuilder;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderConsumerlogDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderEstimateDO;
import com.avatar.hospital.chaperone.database.order.repository.OrderEstimateRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.order.OrderEstimatePageRequest;
import com.avatar.hospital.chaperone.request.order.OrderEstimateRequest;
import com.avatar.hospital.chaperone.request.order.OrderEstimateSaveRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderEstimateIdResponse;
import com.avatar.hospital.chaperone.response.order.OrderEstimateResponse;
import com.avatar.hospital.chaperone.service.order.OrderEstimateService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-18 17:00
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderEstimateServiceImpl implements OrderEstimateService {
    private final OrderEstimateRepositoryService orderEstimateRepositoryService;
    @Override
    public OrderEstimateIdResponse create(OrderEstimateSaveRequest request) {
        long id = IdUtils.getId();
        OrderEstimateDO oldorderEstimateDO = orderEstimateRepositoryService.getByOrderId(request.getOrderId(),request.getSource());
        OrderEstimateDO newOrderEstimateDO = OrderBuilder.createOrderEstimateDOByCreate(request);
        if (Objects.nonNull(oldorderEstimateDO)) {
            newOrderEstimateDO.setId(oldorderEstimateDO.getId());
            AssertUtils.isTrue(orderEstimateRepositoryService.updateById(newOrderEstimateDO), ErrorCode.INSERT_ERROR);
            return OrderEstimateIdResponse.build(id);
        }
        AssertUtils.isTrue(orderEstimateRepositoryService.save(newOrderEstimateDO), ErrorCode.INSERT_ERROR);
        return OrderEstimateIdResponse.build(id);
    }

    @Override
    public List<OrderEstimateResponse> get(OrderEstimateRequest request) {
        LambdaQueryWrapper<OrderEstimateDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderEstimateDO::getOrderId,request.getOrderId());
        queryWrapper.eq(Objects.nonNull(request.getSource()),OrderEstimateDO::getSource,request.getSource());
        List<OrderEstimateDO> list = orderEstimateRepositoryService.list(queryWrapper);
        return OrderBuilder.toOrderEstimateResponseList(list);
    }

    @Override
    public PageResponse<OrderEstimateResponse> paging(OrderEstimatePageRequest request) {
        Page<OrderEstimateDO> page = request.ofPage();
        LambdaQueryWrapper<OrderEstimateDO> queryWrapper = queryWrapper();
        queryWrapper.eq(Objects.nonNull(request.getOrderId()),OrderEstimateDO::getOrderId,request.getOrderId());
        queryWrapper.eq(Objects.nonNull(request.getSource()),OrderEstimateDO::getSource,request.getSource());
        queryWrapper.orderByDesc(OrderEstimateDO::getId);
        page = orderEstimateRepositoryService.page(page, queryWrapper);
        return PageResponse.build(page,OrderBuilder::toOrderEstimateResponse);
    }

    private LambdaQueryWrapper<OrderEstimateDO> queryWrapper() {
        LambdaQueryWrapper<OrderEstimateDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderEstimateDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
