package com.avatar.hospital.chaperone.database.message.enums;

import lombok.Getter;

/**
 * Description:
 * 消息渠道
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum MessageChannel {
    NONE(-1, "未知"),
    WECHAT(1, "微信"),
    ;

    private final Integer channel;

    private final String describe;


    MessageChannel(Integer channel, String describe) {
        this.channel = channel;
        this.describe = describe;
    }

    public static MessageChannel of(Integer channel) {
        if (channel == null) {
            return NONE;
        }
        for (MessageChannel itemStatus : values()) {
            if (channel.equals(itemStatus.getChannel())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
