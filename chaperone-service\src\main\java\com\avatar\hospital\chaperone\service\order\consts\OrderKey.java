package com.avatar.hospital.chaperone.service.order.consts;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-20 17:37
 **/
public class OrderKey {
    /**
     * 现金提取
     */
    public final static String CASH_EXTRACT_PREFIX = "'cash:extract'";
    public final static String CASH_DEPOSIT_PREFIX = "'cash:deposit'";
    /**
     * 订单修改
     */
    public final static String ORDER_MODIFY_PREFIX = "'order:modify:'";

    /**
     * 账单支付
     */
    public final static String BILL_MODIFY_PREFIX = "'bill:modify:'";
    public final static String BILL_EXPORT_PREFIX = "'bill:export'";
    public final static String CONSUMERLOG_EXPORT_PREFIX = "'consumerlog:export'";
    public final static String REPAIR_FORM_EXPORT_PREFIX = "'repairForm:export'";
    /**
     * 账单发票
     */
    public final static String BILL_INVOICE_PREFIX = "'bill:invoice:'";
    /**
     * 套餐修改
     */
    public final static String ITEM_MODIFY_PREFIX = "'item:modify:'";

    /**
     * 微信回调
     */
    public final static String PAY_WX_NOTIFY_PREFIX = "'bill:wx:notify:'";
}
