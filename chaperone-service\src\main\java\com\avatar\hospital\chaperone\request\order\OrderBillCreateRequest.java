package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.database.order.enums.*;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillCreateRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 账单业务id
     *   唯一: 订单号  + 账单类型 + 账单子类型+ 业务ID
     */
    private String bizId;

    /**
     * 总账单ID
     */
    private Long parentBillId;

    /**
     * 账单类型:参考，bill_type
     * @see OrderBillType
     */
    private Integer billType;

    /**
     * 账单子类型:参考，bill_sub_type
     * @see OrderBillSubType
     */
    private Integer billSubType;

    /**
     * 总共应收款金额，单位:分
     */
    private Integer priceReceivable;

    /**
     * 总共应退款金额，单位:分
     */
    private Integer priceRefundable;

    /**
     * 备注,账单说明
     */
    private String remark;

    /**
     * 折扣
     */
    private Integer discount;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    /**
     * 账单开始时间
     * yyyyMMddHHmmss
     */
    private String startTime;

    /**
     * 账单结束时间
     * yyyyMMddHHmmss
     */
    private String endTime;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * json数据
     */
    private String nursingJson;
}
