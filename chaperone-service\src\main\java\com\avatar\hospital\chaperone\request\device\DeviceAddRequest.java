package com.avatar.hospital.chaperone.request.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class DeviceAddRequest implements Serializable {

    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 所属科室
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgDepartmentId;

    /**
     * 所属院区
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;

    /**
     * 状态（1-启用，2-停用，3-维修中）
     */
    private Integer status;

    /**
     * 地址
     */
    private String address;

    /**
     * 备注
     */
    private String remark;

    /**
     * 归属系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他,7-暖通系统，8-建筑系统，9-消防系统）
     */
    private Integer systemType;

    /**
     * 创建者ID
     */
    private Long createBy;
}
