package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderNursingDO;
import com.avatar.hospital.chaperone.database.order.mapper.OrderNursingMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderNursingRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单-关联护工信息; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class OrderNursingRepositoryServiceImpl extends ServiceImpl<OrderNursingMapper, OrderNursingDO> implements OrderNursingRepositoryService {

    @Override
    public List<OrderNursingDO> getByOrderId(@NotNull Long orderId) {
        LambdaQueryWrapper<OrderNursingDO> query = queryWrapper();
        query.eq(OrderNursingDO::getOrderId,orderId);
        query.orderByDesc(OrderNursingDO::getId);
        List<OrderNursingDO> list = list(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list;
    }

    @Override
    public void checkExist(Long orderId) {
        List<OrderNursingDO> nursingDOList = getByOrderId(orderId);
        AssertUtils.isTrue(!CollectionUtils.isEmpty(nursingDOList), ErrorCode.ORDER_NOT_BIND_NURSING_ERROR);
    }

    /**
     * 删除老的护工信息 新增加护工信息
     * @param oldNursingDO
     * @param newNursingDO
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(List<OrderNursingDO> oldNursingDO, List<OrderNursingDO> newNursingDO) {
        if (!CollectionUtils.isEmpty(oldNursingDO)) {
            removeBatchByIds(oldNursingDO);
        }
        saveBatch(newNursingDO);
    }

    @Override
    public Map<Long, Long> findAllByList(List<Long> orderIdList) {
        LambdaQueryWrapper<OrderNursingDO> queryWrapper = queryWrapper();
        queryWrapper.in(OrderNursingDO::getOrderId,orderIdList);
        List<OrderNursingDO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream()
                .collect(Collectors.toMap(OrderNursingDO::getOrderId,OrderNursingDO::getNursingId));
    }

    private LambdaQueryWrapper<OrderNursingDO> queryWrapper() {
        LambdaQueryWrapper<OrderNursingDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderNursingDO::getDeleted, OrderConst.NO_DELETED);
        return queryWrapper;
    }
}
