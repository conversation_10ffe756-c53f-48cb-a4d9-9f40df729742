package com.avatar.hospital.chaperone.job.order;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillSubType;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillType;
import com.avatar.hospital.chaperone.database.order.repository.OrderBillRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderRepositoryService;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.OrderBillService;
import com.avatar.hospital.chaperone.service.order.OrderConsumerlogService;
import com.avatar.hospital.chaperone.service.order.OrderService;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-17 11:04
 **/
@Slf4j
@Component
public class OrderBillExec {
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderBillService orderBillService;
    @Autowired
    private OrderConsumerlogService orderConsumerlogService;
    @Autowired
    private OrderBillRepositoryService orderBillRepositoryService;
    @Autowired
    private OrderRepositoryService orderRepositoryService;

    /**
     * 创建周期账单
     *    还在陪护状态的
     *    已经生成账单总额 - 账单总额
     * @param subType 账单子类型
     * @param startDate yyyyMMdd
     * @param endDate yyyyMMdd
     * @return
     */
    public Boolean createCycle(OrderBillSubType subType, Integer startDate, Integer endDate) {
        log.info("OrderBillExec createCycle start >> subType:{}, startDate:{}, endDate:{}",subType, startDate, endDate);
        List<Long> orderIdList = orderBillRepositoryService.findAllTotal(OrderBillType.CYCLE.getStatus(),subType.getStatus());
        orderIdList.forEach(orderId -> {
            ExceptionUtil.execute(log,() -> createCycle(orderId,startDate,endDate));
        });
        log.info("OrderBillExec createCycle end");
        return Boolean.TRUE;
    }

    /**
     * 创建周期账单
     *    还在陪护状态的
     * @param orderId
     * @param startDate
     * @param endDate
     * @return
     */
    public Long createCycle(Long orderId,Integer startDate,Integer endDate) {
        log.info("OrderBillExec[]createCycle start orderId:{}, startDate:{}, endDate:{}",orderId, startDate, endDate);
        OrderDetailResponse orderDetailResponse = orderService.getById(OrderRequest.buildById(orderId));
        OrderResponse order = orderDetailResponse.getBase();
        OrderTotalBillResponse totalBill = orderDetailResponse.getBill();
        // 查询所有账单列表 统计账单金额
        List<OrderBillResponse> orderBillList = orderBillService.findAll(OrderBillFindAllRequest.build(order.getId()));
        int billPrice = orderBillList.stream()
                .filter(bill -> !Objects.equals(bill.getBillType(),OrderBillType.SETTLE.getStatus()))
                .mapToInt(bill -> bill.getPriceReceivable())
                .sum();
        log.info("OrderBillExec[]createCycle >> billPrice:{}",billPrice);
        // 获取总的内消费金额统计
        OrderConsumeSumPriceRequest sumPriceRequest = new OrderConsumeSumPriceRequest();
        sumPriceRequest.setOrderId(orderId);
        sumPriceRequest.setEndDate(endDate);
        OrderConsumeSumPriceResponse sumPriceResponse = orderConsumerlogService.sumPrice(sumPriceRequest);
        Integer totalPrice = sumPriceResponse.getPrice();
        log.info("OrderBillExec[]createCycle start totalPrice:{}, billPrice:{}",totalPrice, billPrice);

        Integer newPrice = totalPrice - billPrice;
        String billStartTime = DateUtils.max(startDate,order.getRealStartTime());
        String billEndTime = DateUtils.min(endDate,order.getRealEndTime());

        List<OrderNursingSimpleResponse> orderNursingSimpleResponseList = orderConsumerlogService.nursingList(orderId,startDate,endDate);

        OrderBillCreateRequest createBillReq = new OrderBillCreateRequest();
        createBillReq.setOrderId(order.getId());
        createBillReq.setBizId(endDate.toString());
        createBillReq.setParentBillId(totalBill.getId());
        createBillReq.setBillType(totalBill.getBillType());
        createBillReq.setBillSubType(totalBill.getBillSubType());
        createBillReq.setPriceReceivable(newPrice);
        createBillReq.setPriceRefundable(OrderConst.ZERO);
        createBillReq.setDiscount(order.getDiscount());
        createBillReq.setOperatorUser(OrderConst.JOB_OPERATOR);
        createBillReq.setRemark(startDate + "-"  + endDate);
        createBillReq.setStartTime(billStartTime);
        createBillReq.setEndTime(billEndTime);
        createBillReq.setPatientName(order.getPatientName());
        createBillReq.setNursingJson(OrderNursingSimpleResponse.toJsonStr(orderNursingSimpleResponseList));
        OrderBillIdResponse createBillResp = orderBillService.create(createBillReq);
        return createBillResp.getBillId();
    }

    /**
     * 创建结算单账单
     * @return
     */
    public Boolean createSettle(Integer dateHour) {
        log.info("OrderBillExec createSettle start dateHour:{}",dateHour);
        List<OrderDO> orderList = orderRepositoryService.findAllNeedSettle(dateHour);
        if (CollectionUtils.isEmpty(orderList)){
            return Boolean.TRUE;
        }
        for (OrderDO order : orderList) {
            ExceptionUtil.execute(log,() -> {
                OrderSettleApplyRequest orderRequest = OrderSettleApplyRequest.buildById(order.getId());
                orderService.commitSettle(orderRequest);
                return null;
            });
        }
        log.info("OrderBillExec createSettle end");
        return Boolean.TRUE;
    }

}
