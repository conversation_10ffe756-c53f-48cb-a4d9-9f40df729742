package com.avatar.hospital.chaperone.service.nursing.dto;

import com.avatar.hospital.chaperone.database.item.enums.ItemServerType;
import com.avatar.hospital.chaperone.database.nursing.enums.Gender;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus;
import com.avatar.hospital.chaperone.database.order.enums.OrderStatus;
import com.avatar.hospital.chaperone.request.nursing.NursingAddRequest;
import com.avatar.hospital.chaperone.service.order.dto.OrderChangeLogDTO;
import com.avatar.hospital.chaperone.utils.CompareAlias;
import com.avatar.hospital.chaperone.utils.CompareDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingDayLogDTO {


    @Data
    public static abstract class AbsCompareDTO implements CompareDTO, Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 陪护单C端用户ID,后端取登录用户ID
         */
        @CompareAlias(ignore = true)
        private Long orderId;

        /**
         * 空对象标志
         */
        @CompareAlias(ignore = true)
        private Boolean empty = Boolean.FALSE;

        public void setEmptyTrue() {
            empty = Boolean.TRUE;
        }

        @Override
        public Boolean getEmpty() {
            return empty;
        }
    }

    /**
     * 创建
     */
    @Data
    public static class Create extends AbsCompareDTO {
        /**
         * 日期，格式yyyyMMdd
         */
        private List<String> date;

        /**
         * 护工ID
         */
        private Long nursingId;

        /**
         * 护工姓名
         */
        private String nursingName;

        /**
         * 护工状态
         */
        private Integer status;
    }



    public final static NursingDayLogDTO.Create buildByCreate() {
        NursingDayLogDTO.Create logDTO = new NursingDayLogDTO.Create();
        logDTO.setEmptyTrue();
        return logDTO;
    }


    public static NursingDayLogDTO.Create buildByCreate(Long nursingId, NursingAddRequest request, List<String> dateList) {
        NursingDayLogDTO.Create logDTO = new NursingDayLogDTO.Create();
        logDTO.setDate(dateList);
        logDTO.setNursingId(nursingId);
        logDTO.setNursingName(request.getName());
        logDTO.setStatus(NursingDayStatus.FREE.getStatus());
        return logDTO;
    }
}
