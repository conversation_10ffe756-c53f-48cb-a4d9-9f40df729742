package com.avatar.hospital.chaperone.consumer.controller.caccount;

import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/16
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/template/message")
public class WechatTemplateMessageController {

    private final WxMpService wxMpService;

    @PostMapping(value = "/send")
    public void send(@RequestParam(value = "openId") String openId, @RequestParam(value = "templateId") String templateId) {
        try {
            WxMpTemplateMessage wxMpTemplateMessage = WxMpTemplateMessage.builder().toUser(openId).templateId(templateId).build();
            WxMpTemplateData wxMpTemplateData1 = new WxMpTemplateData("first.DATA", "尊贵的用户");
            WxMpTemplateData wxMpTemplateData2 = new WxMpTemplateData("delivername.DATA", "顺丰快递");
            WxMpTemplateData wxMpTemplateData3 = new WxMpTemplateData("ordername.DATA", "123456123");
            WxMpTemplateData wxMpTemplateData4 = new WxMpTemplateData("remark.DATA", "测试");

            wxMpTemplateMessage.addData(wxMpTemplateData1).addData(wxMpTemplateData2).addData(wxMpTemplateData3).addData(wxMpTemplateData4);

            String result = wxMpService.getTemplateMsgService().sendTemplateMsg(wxMpTemplateMessage);
            log.info("WechatTemplateMessageController send result:{}", result);
        } catch (WxErrorException e) {
            log.error("WechatTemplateMessageController send error:{}", Throwables.getStackTraceAsString(e));
        }
    }

}
