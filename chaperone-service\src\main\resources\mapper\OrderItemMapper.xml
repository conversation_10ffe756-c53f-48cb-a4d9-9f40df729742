<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.order.mapper.OrderItemMapper">

    <select id="listByConsumerLog" resultType="com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO">
        SELECT i.*
            FROM `t_order_item` i
            LEFT JOIN `t_order` o on o.id= i.`order_id`
            WHERE i.`item_id` IN
                <foreach collection="itemIdList" separator="," item="item" open="(" close=")">#{item}</foreach>
            and o.`order_status` &gt;= 30
            and i.`deleted` = 0
            and o.`deleted` = 0
            and o.real_start_time &lt;= #{dateHour}
            and o.real_end_time &gt; #{dateHour}
            ORDER BY i.`id` ASC
    </select>
</mapper>