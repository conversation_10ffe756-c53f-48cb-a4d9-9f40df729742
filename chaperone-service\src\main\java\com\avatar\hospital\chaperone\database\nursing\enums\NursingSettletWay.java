package com.avatar.hospital.chaperone.database.nursing.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum NursingSettletWay {
    NONE(-1, "未知"),
    ONE(2, "一次性结算"),
    MONTH(1, "月结"),
    ;

    private final Integer status;

    private final String describe;


    NursingSettletWay(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static NursingSettletWay of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (NursingSettletWay itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
