package com.avatar.hospital.chaperone.database.repair.enums;

import lombok.Getter;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 13:23
 **/
@Getter
public enum RepairFormUrgencyDegreeType {


    /**
     * 非紧急
     */
    NON_URGENT(0, "非紧急"),


    /**
     * 紧急
     */
    URGENCY(1, "紧急"),

    ;

    private final Integer type;

    private final String describe;


    RepairFormUrgencyDegreeType(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }

    public static RepairFormUrgencyDegreeType of(Integer status) {
        if (status == null) {
            return null;
        }
        for (RepairFormUrgencyDegreeType repairFormUrgencyDegreeType : values()) {
            if (status.equals(repairFormUrgencyDegreeType.getType())) {
                return repairFormUrgencyDegreeType;
            }
        }
        return null;
    }
}
