package com.avatar.hospital.chaperone.database.code.repository;

import com.avatar.hospital.chaperone.database.code.dataobject.ProjectCodeDO;
import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 业务编号生成表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface ProjectCodeRepositoryService extends IService<ProjectCodeDO> {

    String generateCode(CodeBizType codeBizType);
}
