package com.avatar.hospital.chaperone.annotation;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-20 17:19
 **/
@Slf4j
@Aspect
@Component
@ConditionalOnClass(RedissonClient.class)
public class IdempotentAspect {
    private static final String KEY_TEMPLATE = "idempotent";
    @Resource
    private RedissonClient redissonClient;
    private final DefaultParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();
    private final SpelExpressionParser spelExpressionParser = new SpelExpressionParser();

    @Pointcut("@annotation(com.avatar.hospital.chaperone.annotation.Idempotent)")
    public void executeIdempotent() {
    }

    @Around("executeIdempotent()")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        RLock lock = null;
        String lockKey = null;
        try {
            MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
            Idempotent lockInfo = signature.getMethod().getAnnotation(Idempotent.class);
            String value = lockInfo.value();
            // SpEL表达式解析key
            // 获得方法参数名数组
            String[] parameterNames = parameterNameDiscoverer.getParameterNames(signature.getMethod());
            if (parameterNames != null && parameterNames.length > 0) {
                EvaluationContext context = new StandardEvaluationContext();
                //获取方法参数值
                Object[] args = proceedingJoinPoint.getArgs();
                for (int i = 0; i < args.length; i++) {
                    // 替换SpEL里的变量值为实际值， 比如 #user -->  user对象
                    context.setVariable(parameterNames[i], args[i]);
                }
                // 解析出实际的信息
                lockKey = idempotentKey(spelExpressionParser.parseExpression(value).getValue(context, String.class));
            }

            lock = redissonClient.getFairLock(lockKey);
            if (lock.tryLock(lockInfo.waitTime(), lockInfo.expire(), TimeUnit.SECONDS)) {
                return proceedingJoinPoint.proceed();
            } else {
                log.warn("IdempotentAspectFail try lock fail");
                ErrorCode errorCode = lockInfo.errorCode();
                return SingleResponse.buildFailure(errorCode.getErrorCode(),errorCode.getErrorMessage());
            }
        } catch (Exception e) {
            log.warn("IdempotentAspect to proceed, e:{}", e.getMessage(), e);
            throw e;
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 幂等
     */
    public static String idempotentKey(String... keys) {
        StringBuilder sb = new StringBuilder(KEY_TEMPLATE);
        for (String key : keys) {
            sb.append(":").append(key);
        }
        return sb.toString();
    }
}
