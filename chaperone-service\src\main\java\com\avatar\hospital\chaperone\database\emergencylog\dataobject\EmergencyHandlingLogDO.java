package com.avatar.hospital.chaperone.database.emergencylog.dataobject;

import com.avatar.hospital.chaperone.database.emergencylog.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 应急处置日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter
@TableName("t_project_emergency_handling_log")
public class EmergencyHandlingLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 事件时间
     */
    private LocalDateTime eventTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属院区ID
     */
    private Long orgId;


}
