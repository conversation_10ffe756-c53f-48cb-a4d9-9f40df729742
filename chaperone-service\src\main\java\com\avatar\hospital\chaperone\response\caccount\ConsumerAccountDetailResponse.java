package com.avatar.hospital.chaperone.response.caccount;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Data
public class ConsumerAccountDetailResponse implements Serializable {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户名称
     */
    private String nickName;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 性别
     *
     * @see com.avatar.hospital.chaperone.database.caccount.enums.AccountSex
     */
    private Integer sex;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 用户头像地址
     */
    private String avatarUrl;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.caccount.enums.AccountStatus
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

}
