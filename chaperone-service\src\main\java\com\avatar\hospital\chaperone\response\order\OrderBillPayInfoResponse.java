package com.avatar.hospital.chaperone.response.order;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 17:02
 **/
@Data
public class OrderBillPayInfoResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    private String appId;
    private String timeStamp;
    private String nonceStr;
    private String packageValue;
    private String signType;
    private String paySign;

    /**
     * 微信支付商户号
     */
    private String partnerid;

    /**
     * 统一下单订单号
     */
    private String prepayid;

}
