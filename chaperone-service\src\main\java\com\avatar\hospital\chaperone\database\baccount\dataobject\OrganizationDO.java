package com.avatar.hospital.chaperone.database.baccount.dataobject;

import com.avatar.hospital.chaperone.database.baccount.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * B端组织机构表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_organization")
public class OrganizationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 上级组织机构ID 顶级节点默认为null
     */
    private Long parentId;

    /**
     * 联络人名称
     */
    private String liaisonName;

    /**
     * 联络人电话
     */
    private String liaisonPhoneNumber;

    /**
     * 银行账号
     */
    private String bankAccountNumber;

    /**
     * 层级
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationLevel
     */
    private Integer level;

    /**
     * 组织机构类型
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationType
     */
    private Integer type;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationStatus
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;
}
