package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.CashLogDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderBillDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderPayDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单-账单; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderBillRepositoryService extends IService<OrderBillDO> {

    /**
     * 获取总账单信息
     * @param orderId
     * @return
     */
    OrderBillDO getTotalByOrderId(Long orderId);

    /**
     * 查预付单
     * @param orderId
     * @return
     */
    OrderBillDO getRepayByOrderId(Long orderId);

    /**
     * 更新账单
     * @param totalBillUpdate
     * @param repayBill
     * @param oldRepayBill
     * @return
     */
    Boolean modifyBillType(OrderBillDO totalBillUpdate, OrderBillDO repayBill, OrderBillDO oldRepayBill);

    /**
     *
     * @param cashPay
     * @param payDO
     */
    Boolean cashPay(OrderBillDO cashPay, OrderPayDO payDO, CashLogDO cashLog);

    /**
     * 查询所有在有效期内的子账单
     * @param type
     * @param subType
     * @return
     */
    List<Long> findAllTotal(Integer type, Integer subType);

    /**
     * 查询所有账单
     *
     * @param orderId
     * @return
     */
    List<OrderBillDO> findAllSubBillByOrderId(Long orderId);

    /**
     * 更新
     * @param updateEntity
     * @param updateBillEntity
     */
    void updateByWxNotify(OrderPayDO updateEntity, OrderBillDO updateBillEntity);

    /**
     * 校验
     * @param orderId
     */
    void checkExist(Long orderId);

    /**
     * 添加
     * @param orderBillDO
     */
    void add(OrderBillDO orderBillDO);

    /**
     * 获取最新账单
     * @param orderId
     * @return
     */
    OrderBillDO getNewBill(Long orderId);

    /**
     * 获取应付未付金额
     * @param start
     * @param end
     * @return
     */
    Long unpay(LocalDateTime start, LocalDateTime end);

    /**
     * 更新预付单开始时间
     * @param id
     * @param realStartTime
     */
    void updateRepayStartTime(Long id, Integer realStartTime);
}
