package com.avatar.hospital.chaperone.database.plan.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanRefDeviceDO;
import com.avatar.hospital.chaperone.database.plan.mapper.MaintenancePlanRefDeviceMapper;
import com.avatar.hospital.chaperone.database.plan.repository.MaintenancePlanRefDeviceRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 维保计划关联设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class MaintenancePlanRefDeviceRepositoryServiceImpl extends ServiceImpl<MaintenancePlanRefDeviceMapper, MaintenancePlanRefDeviceDO> implements MaintenancePlanRefDeviceRepositoryService {

    @Override
    public boolean update2delete(PlanRequest request) {

        LambdaUpdateWrapper<MaintenancePlanRefDeviceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MaintenancePlanRefDeviceDO::getPlanId, request.getId());
        updateWrapper.set(MaintenancePlanRefDeviceDO::getUpdateBy, request.getOperator());
        updateWrapper.set(MaintenancePlanRefDeviceDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.setSql(" deleted = id");
        update(updateWrapper);
        return true;
    }

    @Override
    public List<Long> getRefIds(Long planId) {

        LambdaQueryWrapper<MaintenancePlanRefDeviceDO> queryWrapper = queryWrapper();
        queryWrapper.eq(MaintenancePlanRefDeviceDO::getPlanId, planId);
        queryWrapper.eq(MaintenancePlanRefDeviceDO::getDeleted, DeletedEnum.NO.getStatus());
        List<MaintenancePlanRefDeviceDO> stockApplyRefPartBatchDOS = this.list(queryWrapper);
        return stockApplyRefPartBatchDOS.stream().map(o -> o.getDeviceId()).collect(Collectors.toList());
    }

    @Override
    public Set<Long> getRefIds(Set<Long> planIds) {
        LambdaQueryWrapper<MaintenancePlanRefDeviceDO> queryWrapper = queryWrapper();
        queryWrapper.in(MaintenancePlanRefDeviceDO::getPlanId, planIds);
        queryWrapper.eq(MaintenancePlanRefDeviceDO::getDeleted, DeletedEnum.NO.getStatus());
        List<MaintenancePlanRefDeviceDO> refOrgDOS = this.list(queryWrapper);
        return refOrgDOS.stream().map(o -> o.getDeviceId()).collect(Collectors.toSet());

    }

    private LambdaQueryWrapper<MaintenancePlanRefDeviceDO> queryWrapper() {
        LambdaQueryWrapper<MaintenancePlanRefDeviceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaintenancePlanRefDeviceDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}
