# Redis 配置文件

# 网络配置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# 通用配置
daemonize no
pidfile /var/run/redis.pid
loglevel notice
logfile ""

# 数据库配置
databases 16
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端配置
maxclients 10000
