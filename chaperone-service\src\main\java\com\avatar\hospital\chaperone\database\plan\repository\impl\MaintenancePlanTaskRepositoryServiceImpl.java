package com.avatar.hospital.chaperone.database.plan.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanTaskDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanTaskDO;
import com.avatar.hospital.chaperone.database.plan.enums.TaskStatusType;
import com.avatar.hospital.chaperone.database.plan.mapper.MaintenancePlanTaskMapper;
import com.avatar.hospital.chaperone.database.plan.repository.MaintenancePlanTaskRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.TaskExecuteRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 维保计划任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class MaintenancePlanTaskRepositoryServiceImpl extends ServiceImpl<MaintenancePlanTaskMapper, MaintenancePlanTaskDO> implements MaintenancePlanTaskRepositoryService {
    private final String STATISTICS_SELECT_SQL = "status,count(id) as id";
    @Override
    public LambdaQueryWrapper<MaintenancePlanTaskDO> queryWrapper() {
        LambdaQueryWrapper<MaintenancePlanTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaintenancePlanTaskDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    @Override
    public Boolean execute(TaskExecuteRequest request) {

        PlanTaskDO taskDO = this.getById(request.getId());
        AssertUtils.isNotNull(taskDO, ErrorCode.PROJECT_PLAN_TASK_NOT_EXIST);
        LocalDateTime now = LocalDateTime.now();
        //1.任务完成
        LambdaUpdateWrapper<MaintenancePlanTaskDO> update = this.updateWrapper();
        update.eq(PlanTaskDO::getId, request.getId());
        update.set(Objects.nonNull(request.getRemark()), PlanTaskDO::getRemark, request.getRemark());
        update.set(Objects.nonNull(request.getIsRepair()), PlanTaskDO::getIsRepair, request.getIsRepair());
        update.set(PlanTaskDO::getStatus, TaskStatusType.COMPLETED.getCode());
        update.set(PlanTaskDO::getUpdateBy, request.getOperator());
        update.set(PlanTaskDO::getUpdatedAt, now);
        update.set(PlanTaskDO::getCompletedTime, now);

        return this.update(update);
    }

    /**
     * 任务过期
     *
     * @param taskIds
     * @return
     */
    @Override
    public Boolean expired(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();

        LambdaUpdateWrapper<MaintenancePlanTaskDO> update = this.updateWrapper();
        update.in(PlanTaskDO::getId, taskIds);

        update.set(PlanTaskDO::getStatus, TaskStatusType.EXPIRED.getCode());
        update.set(PlanTaskDO::getUpdatedAt, now);
        this.update(update);
        return true;
    }

    @Override
    public boolean update2delete(PlanRequest request) {

        LambdaUpdateWrapper<MaintenancePlanTaskDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PlanTaskDO::getPlanId, request.getId());
        updateWrapper.set(PlanTaskDO::getUpdateBy, request.getOperator());
        updateWrapper.set(PlanTaskDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.setSql(" deleted = id");
        this.update(updateWrapper);
        return true;
    }

    @Override
    public Map<Integer, Integer> getStatisticsForYear(Integer year) {
        LocalDateTime[] startEndOfYear = DateUtils.getStartEndOfYear(year);
        QueryWrapper<MaintenancePlanTaskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(STATISTICS_SELECT_SQL)
                .lambda()
                .eq(MaintenancePlanTaskDO::getDeleted, DeletedEnum.NO.getStatus())
                .ge(MaintenancePlanTaskDO::getCreatedAt, startEndOfYear[0])
                .le(MaintenancePlanTaskDO::getCreatedAt,startEndOfYear[1])
                .groupBy(MaintenancePlanTaskDO::getStatus);
        List<MaintenancePlanTaskDO> list = list(queryWrapper);
        Map<Integer, Long> map = list.stream()
                .collect(Collectors.toMap(MaintenancePlanTaskDO::getStatus, MaintenancePlanTaskDO::getId));

        Map<Integer, Integer> result = Maps.newHashMap();
        for (int i = 0; i <= 3; i++) {
            result.put(i,map.getOrDefault(i,0L).intValue());
        }
        return result;
    }

    @Override
    public Map<Integer, Integer> getStatisticsForMonth(Integer month) {
        LocalDateTime[] startEndOfMoth = DateUtils.getStartEndOfMonth(month);
        QueryWrapper<MaintenancePlanTaskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(STATISTICS_SELECT_SQL)
                .lambda()
                .eq(MaintenancePlanTaskDO::getDeleted, DeletedEnum.NO.getStatus())
                .ge(MaintenancePlanTaskDO::getCreatedAt, startEndOfMoth[0])
                .le(MaintenancePlanTaskDO::getCreatedAt,startEndOfMoth[1])
                .groupBy(MaintenancePlanTaskDO::getStatus);
        List<MaintenancePlanTaskDO> list = list(queryWrapper);
        Map<Integer, Long> map = list.stream()
                .collect(Collectors.toMap(MaintenancePlanTaskDO::getStatus, MaintenancePlanTaskDO::getId));

        Map<Integer, Integer> result = Maps.newHashMap();
        for (int i = 0; i <= 3; i++) {
            result.put(i,map.getOrDefault(i,0L).intValue());
        }
        return result;
    }

    private LambdaUpdateWrapper<MaintenancePlanTaskDO> updateWrapper() {
        LambdaUpdateWrapper<MaintenancePlanTaskDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MaintenancePlanTaskDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
