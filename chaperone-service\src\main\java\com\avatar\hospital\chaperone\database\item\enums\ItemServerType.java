package com.avatar.hospital.chaperone.database.item.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Description:
 *      套餐类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum ItemServerType implements IEnumConvert<Integer> {
    NONE(-1, "未知",-1L),
    OTHER(0, "其他",0L),
    // 没有默认值
    HOSPITAL(1, "住院陪护",null),
    OLD_PEOPLE_HOME(2, "养老院陪护",null),
    HOME(3, "居家陪护",3L),
    HOUSEKEEPING(4, "家政陪护",4L),
    ;

    private final Integer status;

    private final String describe;

    /**
     * 默认机构ID
     */
    private final Long orgId;


    ItemServerType(Integer status, String describe,Long orgId) {
        this.status = status;
        this.describe = describe;
        this.orgId = orgId;
    }

    public static ItemServerType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (ItemServerType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }


    public static  final Map<Integer, String> toMap() {
        ItemServerType[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (ItemServerType value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

    @Override
    public String convertDesc(Integer i) {
        return of(i).getDescribe();
    }
}
