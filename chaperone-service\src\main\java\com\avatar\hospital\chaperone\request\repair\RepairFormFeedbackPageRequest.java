package com.avatar.hospital.chaperone.request.repair;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:51
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormFeedbackPageRequest extends PageRequest  implements OperatorReq {
    /**
     * 报修单ID
     */
    @NotNull
    public Long repairFormId;

    /**
     * 操作用户
     * @ignore
     */
    private Operator operatorUser;
}
