package com.avatar.hospital.chaperone.generator.convert;

import com.baomidou.mybatisplus.generator.config.INameConvert;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;


/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/4/2
 */
public class EntityNameConvert extends INameConvert.DefaultNameConvert {

    public EntityNameConvert(StrategyConfig strategyConfig) {
        super(strategyConfig);
    }

    @Override
    public String entityNameConvert(TableInfo tableInfo) {
        String tableName = super.entityNameConvert(tableInfo);
        return tableName + "DO";
    }
}
