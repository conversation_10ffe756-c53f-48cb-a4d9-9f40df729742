package com.avatar.hospital.chaperone.service.repair.impl;

import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.builder.part.PartApplyBuilder;
import com.avatar.hospital.chaperone.builder.repair.RepairFeedbackBuilder;
import com.avatar.hospital.chaperone.builder.repair.RepairFormBuilder;
import com.avatar.hospital.chaperone.database.baccount.repository.WebAccountRepositoryService;
import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartApplyModel;
import com.avatar.hospital.chaperone.database.part.repository.PartApplyRepositoryService;
import com.avatar.hospital.chaperone.database.part.repository.PartBatchRepositoryService;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormFeedbackDO;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormFeedbackType;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormStatus;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormFeedbackRepositoryService;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.repair.RepairFormFeedbackCreateRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormFeedbackPageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormFeedbackIdResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormFeedbackResponse;
import com.avatar.hospital.chaperone.service.code.ProjectCodeService;
import com.avatar.hospital.chaperone.service.repair.RepairFormFeedbackService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 16:29
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RepairFormFeedbackServiceImpl implements RepairFormFeedbackService {
    private final RepairFormFeedbackRepositoryService repairFormFeedbackRepositoryService;
    private final RepairFormRepositoryService repairFormRepositoryService;
    private final WebAccountRepositoryService webAccountRepositoryService;
    private final PartApplyRepositoryService partApplyRepositoryService;
    private final PartBatchRepositoryService partBatchRepositoryService;
    private final ProjectCodeService projectCodeService;

    @Override
    public PageResponse<RepairFormFeedbackResponse> paging(RepairFormFeedbackPageRequest request) {
        LambdaQueryWrapper<RepairFormFeedbackDO> queryWrapper = queryWrapper();
        queryWrapper.eq(Objects.nonNull(request.getRepairFormId()),RepairFormFeedbackDO::getRepairFormId,request.getRepairFormId());
        Page<RepairFormFeedbackDO> page = request.ofPage();
        page = repairFormFeedbackRepositoryService.page(page,queryWrapper);
        List<RepairFormFeedbackDO> records = page.getRecords();
        // 获取关联数据
        List<Long> repairFormId = CollUtils.toListLongDistinct(records, RepairFormFeedbackDO::getRepairFormId);
        List<Long> feedbackIdList = CollUtils.toListLongDistinct(records, RepairFormFeedbackDO::getId);
        List<RepairFormDO> repairFormList = repairFormRepositoryService.findAllByIdList(repairFormId);
        Map<Long, List<PartApplyModel>> feedbackPartRef = partApplyRepositoryService.findMapByFeedbackIdList(feedbackIdList);

        List<RepairFormFeedbackResponse> list = RepairFormBuilder.createRepairFormFeedbackResponseList(records,repairFormList,feedbackPartRef);
        return PageResponse.build(page,list);
    }

    @Override
    public PageResponse<RepairFormFeedbackResponse> pagingForC(RepairFormFeedbackPageRequest request) {
        LambdaQueryWrapper<RepairFormFeedbackDO> queryWrapper = queryWrapper();
        queryWrapper.eq(Objects.nonNull(request.getRepairFormId()),RepairFormFeedbackDO::getRepairFormId,request.getRepairFormId());
        queryWrapper.orderByDesc(RepairFormFeedbackDO::getCreatedAt);
        Page<RepairFormFeedbackDO> page = request.ofPage();
        page = repairFormFeedbackRepositoryService.page(page,queryWrapper);
        List<RepairFormFeedbackDO> records = page.getRecords();
        // 获取关联数据
        List<Long> repairFormId = CollUtils.toListLongDistinct(records, RepairFormFeedbackDO::getRepairFormId);
        List<Long> feedbackIdList = CollUtils.toListLongDistinct(records, RepairFormFeedbackDO::getId);
        List<RepairFormDO> repairFormList = repairFormRepositoryService.findAllByIdList(repairFormId);
        Map<Long, List<PartApplyModel>> feedbackPartRef = partApplyRepositoryService.findMapByFeedbackIdList(feedbackIdList);

        List<RepairFormFeedbackResponse> list = RepairFormBuilder.createRepairFormFeedbackResponseList(records,repairFormList,feedbackPartRef);
        return PageResponse.build(page,list);
    }

    @Override
    public RepairFormFeedbackIdResponse create(RepairFormFeedbackCreateRequest request) {
        RepairFormDO repair = repairFormRepositoryService.getById(request.getRepairFormId());
        AssertUtils.isTrue(Objects.equals(repair.getStatus(), RepairFormStatus.HAS_EXECUTOR.getStatus())
                ||Objects.equals(repair.getStatus(), RepairFormStatus.NOT_FINISH.getStatus()) , ErrorCode.REPAIR_FORM_STATUS_NOT_SUPPORT_FEED);

        RepairFormFeedbackDO feedback = RepairFeedbackBuilder.createRepairFormFeedbackDO(request);
        PartApplyDO partApply = null;
        PartApplyRefPartBatchDO partApplyRefPartBatchDO = null;
        if (Objects.equals(RepairFormFeedbackType.UER_PART.getStatus(),request.getType())) {  // 使用备件处理
            log.info("RepairFormFeedbackServiceImpl[]create use part>> request:{}", JSON.toJSONString(request));
            AssertUtils.isNotNull(request.getPart(),ErrorCode.PROJECT_PARAM_PART_BATCH_NOT_EXIST);
            PartBatchDO partBatchDO = partBatchRepositoryService.getById(request.getPart().getPartBatchId());
            AssertUtils.isNotNull(partBatchDO, ErrorCode.PROJECT_PART_BATCH_NOT_EXIST);
            String partBatchName = partBatchDO.getName();

            partApply = PartApplyBuilder.createPartApplyDO(request,feedback);
            partApply.setRepairFormId(repair.getId());
            partApply.setCode(projectCodeService.generateCode(CodeBizType.BJSY));

            partApplyRefPartBatchDO = PartApplyBuilder.createPartApplyRefPartBatchDO(request,partApply,partBatchName);
        }
        RepairFormDO updateEntity = null;
        if (Objects.equals(RepairFormFeedbackType.FINISH.getStatus(),request.getType())) {  // 使用备件处理
            log.info("RepairFormFeedbackServiceImpl[]create finish>> request:{}", JSON.toJSONString(request));
            updateEntity = new RepairFormDO();
            updateEntity.setId(repair.getId());
            updateEntity.setStatus(RepairFormStatus.AUTH.getStatus());
            updateEntity.setUpdatedAt(LocalDateTime.now());
        }
        repairFormFeedbackRepositoryService.add(updateEntity,feedback,partApply,partApplyRefPartBatchDO);
        return RepairFormFeedbackIdResponse.builder()
                .id(feedback.getId())
                .build();
    }

    @Override
    public Map<Long, RepairFormFeedbackResponse> getLastRepairFormFeedback(List<Long> formIdList) {
        Map<Long, RepairFormFeedbackDO> records = repairFormFeedbackRepositoryService.getLastRepairFormFeedback(formIdList);
        Map<Long, RepairFormFeedbackResponse> map = Maps.newHashMapWithExpectedSize(records.size());
        records.forEach((k,v) -> map.put(v.getRepairFormId(),RepairFormBuilder.buildRepairFormFeedbackResponseForRemark(v)));
        return map;
    }

    private LambdaQueryWrapper<RepairFormFeedbackDO> queryWrapper() {
        LambdaQueryWrapper<RepairFormFeedbackDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairFormFeedbackDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
