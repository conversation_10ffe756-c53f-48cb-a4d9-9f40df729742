package com.avatar.hospital.chaperone.web.controller.baccount;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: B端用户登出接口
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/logout")
public class WebLogoutController {

    /**
     * 退出登录
     *
     * @return -
     */
    @PostMapping(value = "")
    public SingleResponse<Boolean> logout() {
        return TemplateProcess.doProcess(log, () -> {
            Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
            log.info("WebLogoutController logout accountId:{}", accountId);
            StpUtil.logout();
            return Boolean.TRUE;
        });
    }

}
