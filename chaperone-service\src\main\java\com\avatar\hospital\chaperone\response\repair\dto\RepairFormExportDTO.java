package com.avatar.hospital.chaperone.response.repair.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @program: hospital-chaperone
 * @description: 账单导出
 * @author: sp0372
 * @create: 2023-10-24 10:29
 **/
@Data
@ExcelIgnoreUnannotated
public class RepairFormExportDTO {

    @ExcelProperty(value = "报修单编号")
    private String code;

    @ExcelProperty(value = "报修单类型")
    private String systemType;

    @ExcelProperty(value = "报修单状态")
    private String status;

    @ExcelProperty(value = "报修单人员")
    private String createByName;

    @ExcelProperty(value = "报修单时间")
    private String createAt;

    @ExcelProperty(value = "完成时间")
    private String completedTime;

    @ExcelProperty(value = "派工人员")
    private String assignerAccountName;

    @ExcelProperty(value = "执行人员")
    private String executorAccountName;

    @ExcelProperty(value = "紧急程度")
    private String urgencyDegreeType;

    @ExcelProperty(value = "报修位置")
    private String location;
    @ExcelProperty(value = "问题说明")
    private String remark;
    @ExcelProperty(value = "情况说明")
    private String desc;

}
