<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.order.mapper.OrderMapper">

    <select id="findAllByNursingId" resultType="com.avatar.hospital.chaperone.database.order.dataobject.OrderDO">
        SELECT o.*
        from `t_order_nursing` n
        LEFT JOIN `t_order` o on n.`order_id`= o.`id`
        WHERE n.`deleted`= 0
        and o.`deleted`= 0
        and n.`nursing_id`= #{nursingId}
        and o.`order_status` <![CDATA[ >= ]]>  30
        and o.`real_end_time` <![CDATA[ >= ]]> #{startDate}
        and o.`real_start_time` <![CDATA[ <= ]]> #{endDate}
        <if test="orderId != null">and n.`order_id` = #{orderId}</if>
    </select>
</mapper>