package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * Description:
 *  支付状态
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderPayStatus implements IEnumConvert<Integer> {

    TIME_OUT(-4, "已过期"),
    FAIL(-3, "支付失败"),
    CANCEL(-2, "取消"),

    NONE(-1, "未知"),
    UNPAY(0, "未支付","NOTPAY"),
    PAYING(1, "待支付"),
    SUCCESS(2, "支付成功","SUCCESS"),
    CLOSE(3, "订单已关闭","CLOSED"),
    REFUND_ALL(10, "全部退款","REFUND"),
    REFUND_SUB(11, "部分退款","REFUND"),
    ;

    private final Integer status;

    private final String describe;
    private final String wxStatus;

    OrderPayStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
        this.wxStatus = null;
    }

    OrderPayStatus(Integer status, String describe,String wxStatus) {
        this.status = status;
        this.describe = describe;
        this.wxStatus = wxStatus;
    }

    public static OrderPayStatus of(Integer status) {
        if (status == null) {
            return null;
        }
        for (OrderPayStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return null;
    }

    public static  final Map<Integer, String> toMap() {
        OrderPayStatus[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (OrderPayStatus value : values) {
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

    @Override
    public String convertDesc(Integer val) {
        OrderPayStatus e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    public boolean matchWith(Integer payStatus) {
        OrderPayStatus payEnum = of(payStatus);
        return Objects.equals(this,payEnum);
    }

    /**
     * 通过微信状态转换
     * @param status
     * @return
     */
    public OrderPayStatus ofWxNotify(String  status) {
        if (status == null) {
            return NONE;
        }
        for (OrderPayStatus itemStatus : values()) {
            if (status.equals(itemStatus.getWxStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }
}
