package com.avatar.hospital.chaperone.consumer;

import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.annotation.MapperScans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/7
 */
@ComponentScan("com.avatar.hospital.chaperone")
@MapperScans(value = {
        @MapperScan(value = "com.avatar.hospital.chaperone.database.baccount.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.caccount.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.item.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.message.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.nursing.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.order.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.statistics.mapper"),
        @MapperScan(value = "com.avatar.hospital.chaperone.database.*.mapper"),

})
@SpringBootApplication
public class HospitalChaperoneConsumerApplication {

    public static void main(String[] args) {
        SpringApplication.run(HospitalChaperoneConsumerApplication.class, args);
    }

}
