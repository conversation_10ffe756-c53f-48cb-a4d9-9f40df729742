package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.nursing.enums.Gender;
import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <p>
 * 订单主表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_order")
public class OrderDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 订单ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 订单C端用户ID
     */
      private Long accountId;

      /**
     * 服务类型，参考:server_type
       * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
     */
      private Integer serverType;

      /**
     * 病人姓名
     */
      private String patientName;

      /**
     * 病人性别,-1未知 0 女 1 男
       * @see Gender
     */
      private Integer patientGender;

      /**
     * 病人出生日期 yyyyMMdd
     */
      private String patientBirthday;

      /**
     * 家属联系电话
     */
      private String phone;

      /**
     * 病人病情描述
     */
      private String patientStateDesc;

      /**
     * 特殊要求
     */
      private String specificDesc;

      /**
     * 机构ID(医院)
     */
      private Long orgId;

      /**
     * 科室
     */
      private String departments;

      /**
     * 床号
     */
      private String bedNo;

      /**
     * 详细地址
     */
      private String address;

      /**
     * 陪护订单状态,参考：order_status
       * @see com.avatar.hospital.chaperone.database.order.enums.OrderStatus
     */
      private Integer orderStatus;

      /**
     * 陪护开始日期，格式：yyyy-MM-dd HH:mm:ss
     */
      private Integer startTime;

      /**
     * 陪护终止日期，格式：yyyy-MM-dd HH:mm:ss
     */
      private Integer endTime;

      /**
     * 陪护实际开始日期格式：yyyyMMddHH
     */
      private Integer realStartTime;

      /**
     * 陪护实际终止日期格式：yyyyMMddHH
     */
      private Integer realEndTime;

      /**
     * 总账单ID
     */
      private Long parentBillId;

      /**
     * 折扣(99折)
     */
      private Integer discount;

      /**
     * 折扣类型 0 不影响已产生费用 1 影响已产生费用
       * @see com.avatar.hospital.chaperone.database.order.enums.OrderDiscountType
     */
      private Integer discountType;

      /**
     * 物业比例
     */
      private Integer rateCertifiedProperty;

      /**
     * 医院比例
     */
      private Integer rateHospital;

      /**
     * 护工比例
     */
      private Integer rateNursing;

      /**
     * 备注
     */
      private String remark;
  /**
   * 订单所属C端用户(下单人手机号)
   */
  private String accountPhone;

  /**
   * 没有设置费率
   * @return
   */
  public Boolean noSettingRate() {
    return Objects.equals(-1,rateCertifiedProperty)
            && Objects.equals(-1,rateHospital)
            && Objects.equals(-1,rateNursing);
  }

}
