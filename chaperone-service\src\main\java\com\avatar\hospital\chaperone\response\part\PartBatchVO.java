package com.avatar.hospital.chaperone.response.part;

import com.avatar.hospital.chaperone.annotation.serializer.LocalDateTimeSerializer;
import com.avatar.hospital.chaperone.response.BaseVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.NumberSerializers;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 13:15
 */
@Data
public class PartBatchVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = -5481496257106547961L;
    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 批次编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 可用数量
     */
    private Integer balance;

    /**
     * 单价
     */
    private Integer price;

    /**
     * 数量单位
     */
    private String unit;

    /**
     * 状态（0-待入库，1-已入库）
     * @see com.avatar.hospital.chaperone.database.part.enums.PartBatchStatusType
     */
    private Integer status;

    /**
     * 入库时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime entryTime;

    /**
     * 版本号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long version;

}
