package com.avatar.hospital.chaperone.web.handler;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Slf4j
@SuppressWarnings("ALL")
@RestControllerAdvice
public class WebGlobalExceptionHandler {

    /**
     * 没有登录异常
     *
     * @param request           -
     * @param response          -
     * @param notLoginException -
     * @return -
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(HttpStatus.OK)
    public SingleResponse notLoginException(HttpServletRequest request, HttpServletResponse response, NotLoginException notLoginException) {
        log.error("WebGlobalExceptionHandler 统一异常处理 notPermissionException  {}", Throwables.getStackTraceAsString(notLoginException));
        return SingleResponse.buildFailure(ErrorCode.WEB_ACCOUNT_NOT_LOGIN_ERROR.getErrorCode(), ErrorCode.WEB_ACCOUNT_NOT_LOGIN_ERROR.getErrorMessage());
    }

    /**
     * 访问接口没有权限异常
     *
     * @param request                -
     * @param response               -
     * @param notPermissionException -
     * @return -
     */
    @ExceptionHandler(NotPermissionException.class)
    @ResponseStatus(HttpStatus.OK)
    public SingleResponse notPermissionException(HttpServletRequest request, HttpServletResponse response, NotPermissionException notPermissionException) {
        log.error("WebGlobalExceptionHandler 统一异常处理 notPermissionException  {}", Throwables.getStackTraceAsString(notPermissionException));
        return SingleResponse.buildFailure(ErrorCode.WEB_ACCOUNT_NOT_PERMISSION_ERROR.getErrorCode(), ErrorCode.WEB_ACCOUNT_NOT_PERMISSION_ERROR.getErrorMessage());
    }

    /**
     * 普通未拦截异常
     *
     * @param request   -
     * @param response  -
     * @param exception -
     * @return
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public SingleResponse exception(HttpServletRequest request, HttpServletResponse response, Exception exception) {
        log.error("WebGlobalExceptionHandler 统一异常处理 exception {}", Throwables.getStackTraceAsString(exception));
        return SingleResponse.buildFailure(ErrorCode.SYSTEM_ERROR.getErrorCode(), ErrorCode.SYSTEM_ERROR.getErrorMessage());
    }

    /**
     * 普通未拦截异常
     *
     * @param request   -
     * @param response  -
     * @param exception -
     * @return
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public SingleResponse businessException(HttpServletRequest request, HttpServletResponse response, BusinessException exception) {
        log.error("WebGlobalExceptionHandler 统一异常处理 businessException {}", Throwables.getStackTraceAsString(exception));
        return SingleResponse.buildFailure(exception.getErrorCode(), exception.getErrorMessage());
    }

    /**
     * 最高级别异常
     *
     * @param request   -
     * @param response  -
     * @param exception -
     * @return
     */
    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.OK)
    public SingleResponse throwableException(HttpServletRequest request, HttpServletResponse response, Throwable throwable) {
        log.error("WebGlobalExceptionHandler 统一异常处理 throwableException {}", Throwables.getStackTraceAsString(throwable));
        return SingleResponse.buildFailure(ErrorCode.SYSTEM_ERROR.getErrorCode(), ErrorCode.SYSTEM_ERROR.getErrorMessage());
    }

}
