package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 16:47
 **/
@Data
public class OrderRequest implements OperatorReq,Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    /**
     * 强制改为取消状态
     */
    private Boolean forceCancel;

    public static final OrderRequest buildById(Long orderId) {
        OrderRequest obj = new OrderRequest();
        obj.setOrderId(orderId);
        obj.setOperatorUser(OrderConst.JOB_OPERATOR);
        return obj;
    }

    public static final OrderRequest buildForCancelById(Long orderId) {
        OrderRequest obj = new OrderRequest();
        obj.setOrderId(orderId);
        obj.setOperatorUser(OrderConst.JOB_OPERATOR);
        obj.setForceCancel(Boolean.TRUE);
        return obj;
    }

}
