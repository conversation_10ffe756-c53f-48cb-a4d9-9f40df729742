package com.avatar.hospital.chaperone.properties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Description: 用作固定微信模板消息
 *
 * <AUTHOR>
 * @since 2023/10/17
 */
@Data
@Slf4j
@ConfigurationProperties(prefix = WxTemplateMessageProperties.PREFIX)
@Component
public class WxTemplateMessageProperties implements Serializable {

    public static final String PREFIX = "wx-template-message";

    /**
     * 模板消息配置
     * key:模板ID value:模板数据
     */
    private Map<String, List<TemplateData>> map;


    @Data
    public static class TemplateData implements Serializable {

        private String name;

        private String value;

        public static final TemplateData build(String name, String value) {
            TemplateData obj = new TemplateData();
            obj.setName(name);
            obj.setValue(value);
            return obj;
        }

    }

    /**
     * 根据模板消息ID获取模板数据
     *
     * @param templateId -
     * @return -
     */
    public List<TemplateData> getTemplateData(String templateId) {
        if (map == null || templateId == null) {
            return Collections.emptyList();
        }
        return map.getOrDefault(templateId, Collections.emptyList());
    }

}
