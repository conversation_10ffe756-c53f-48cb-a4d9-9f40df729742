package com.avatar.hospital.chaperone.response.item;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 15:47
 **/
@Data
public class ItemIdResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    // build

    public static final ItemIdResponse buildById(Long itemId) {
        ItemIdResponse obj = new ItemIdResponse();
        obj.setId(itemId);
        return obj;
    }
}
