package com.avatar.hospital.chaperone.response.nursing;

import com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * @author:sp0420
 * @Description:
 */
@Data
public class NursingDayPagingResponse implements Serializable {


    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 排班
     */
    private Long date;

    /**
     * 护工工号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long nursingId;

    /**
     * 护工名称
     */
    private String nursingName;

    /**
     * 班次状态
     * @see com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus
     */
    private Integer status;

    /**
     * 关联陪护单
     */
    private List<NursingDayOrderResponse> orderList;

    public Integer getStatus() {
        if ( CollectionUtils.isEmpty(orderList)) {
            if (NursingDayStatus.LEAVE.getStatus().equals(status)){
                return NursingDayStatus.LEAVE.getStatus();
            }
            if (NursingDayStatus.FREE.getStatus().equals(status)){
                return NursingDayStatus.FREE.getStatus();
            }
        }
        return NursingDayStatus.BUSY.getStatus();
    }

    public void setStatus() {
        //订单为空，判断是否为请假，不为请假则改为空闲；订单不为空，则为忙碌
        if ( CollectionUtils.isEmpty(orderList)) {
            if (NursingDayStatus.LEAVE.getStatus().equals(status)){
                this.status =  NursingDayStatus.LEAVE.getStatus();
            }else {
                this.status =  NursingDayStatus.FREE.getStatus();
            }
        }else {
            this.status =  NursingDayStatus.BUSY.getStatus();
        }
    }
}
