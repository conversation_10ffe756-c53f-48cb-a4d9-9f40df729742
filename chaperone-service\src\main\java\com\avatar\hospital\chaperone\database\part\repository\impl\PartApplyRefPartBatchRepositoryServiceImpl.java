package com.avatar.hospital.chaperone.database.part.repository.impl;

import com.avatar.hospital.chaperone.database.part.dataobject.PartApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.model.PartStatisticsModel;
import com.avatar.hospital.chaperone.database.part.mapper.PartApplyRefPartBatchMapper;
import com.avatar.hospital.chaperone.database.part.repository.PartApplyRefPartBatchRepositoryService;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 备件使用申请单关联备件批次 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class PartApplyRefPartBatchRepositoryServiceImpl extends ServiceImpl<PartApplyRefPartBatchMapper, PartApplyRefPartBatchDO> implements PartApplyRefPartBatchRepositoryService {


    @Override
    public List<PartApplyRefPartBatchDO> getByApplyIdList(List<Long> applyIdList) {
        if (CollUtils.isEmpty(applyIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<PartApplyRefPartBatchDO> queryWrapper = queryWrapper();
        queryWrapper.in(PartApplyRefPartBatchDO::getSparePartApplyId,applyIdList);
        List<PartApplyRefPartBatchDO> list = list(queryWrapper);
        return list;
    }

    @Override
    public PartApplyRefPartBatchDO getByApplyId(Long applyId) {
        LambdaQueryWrapper<PartApplyRefPartBatchDO> queryWrapper = queryWrapper();
        queryWrapper.eq(PartApplyRefPartBatchDO::getSparePartApplyId,applyId);
        PartApplyRefPartBatchDO obj = getOne(queryWrapper);
        return obj;
    }

    @Override
    public List<PartStatisticsModel> getStatistics(LocalDateTime start, LocalDateTime end) {
        return baseMapper.getStatistics(start,end);
    }

    private LambdaQueryWrapper<PartApplyRefPartBatchDO> queryWrapper() {
        LambdaQueryWrapper<PartApplyRefPartBatchDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PartApplyRefPartBatchDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }
}
