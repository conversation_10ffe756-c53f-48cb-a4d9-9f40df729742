package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * Description:
 *  交易类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderTradeType implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    INIT(0, "待定"),
    CASH(1, "现金"),
    WECHAT(2, "微信"),
    ;

    private final Integer status;

    private final String describe;


    OrderTradeType(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderTradeType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderTradeType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    public static  final Map<Integer, String> toMap() {
        OrderTradeType[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (OrderTradeType value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

    @Override
    public String convertDesc(Integer val) {
        OrderTradeType e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }
}
