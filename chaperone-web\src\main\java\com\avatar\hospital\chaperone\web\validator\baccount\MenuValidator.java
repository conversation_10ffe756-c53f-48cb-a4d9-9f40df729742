package com.avatar.hospital.chaperone.web.validator.baccount;

import com.avatar.hospital.chaperone.database.baccount.enums.MenuStatus;
import com.avatar.hospital.chaperone.database.baccount.enums.MenuType;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.MenuAddRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuTreeRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuUpdateRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/11
 */
public class MenuValidator {

    private static final Integer DEFAULT_STATUS = MenuStatus.ENABLE.getStatus();

    private static final Integer DEFAULT_SORT = 100;


    public static void addValidate(MenuAddRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getName(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getFrontName(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getParentId(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getType(), ErrorCode.PARAMETER_ERROR);

        if (MenuType.AUTHORITY.getType().equals(request.getType())) {
            AssertUtils.hasText(request.getMenuKey(), ErrorCode.PARAMETER_ERROR);
        }

        if (request.getStatus() != null) {
            AssertUtils.isNotNull(MenuStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        }
        else {
            request.setStatus(DEFAULT_STATUS);
        }

        if (request.getSort() != null) {
            AssertUtils.isTrue(request.getSort() >= 0, ErrorCode.PARAMETER_ERROR);
        }
        else {
            request.setSort(DEFAULT_SORT);
        }

        // 设置创建人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setCreateBy(accountId);
        request.setUpdateBy(accountId);
    }

    public static void updateValidate(MenuUpdateRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getId(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getName(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getFrontName(), ErrorCode.PARAMETER_ERROR);
        if (request.getStatus() != null) {
            AssertUtils.isNotNull(MenuStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        }
        if (request.getSort() != null) {
            AssertUtils.isTrue(request.getSort() >= 0, ErrorCode.PARAMETER_ERROR);
        }
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void deleteValidate(MenuDeleteRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.notEmpty(request.getIds(), ErrorCode.PARAMETER_ERROR);
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void pagingValidate(MenuPagingRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageIndex(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageSize(), ErrorCode.PARAMETER_ERROR);
    }

    public static void treeValidate(MenuTreeRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getAccountId(), ErrorCode.PARAMETER_ERROR);
    }

    public static void accountMenuTreeValidate(MenuTreeRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getAccountId(), ErrorCode.PARAMETER_ERROR);
    }
}
