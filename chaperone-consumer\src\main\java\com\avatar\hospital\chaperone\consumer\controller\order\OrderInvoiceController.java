package com.avatar.hospital.chaperone.consumer.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.request.order.OrderInvoiceCreateRequest;
import com.avatar.hospital.chaperone.request.order.OrderInvoiceRequest;
import com.avatar.hospital.chaperone.response.order.OrderInvoiceIdResponse;
import com.avatar.hospital.chaperone.response.order.OrderInvoiceResponse;
import com.avatar.hospital.chaperone.service.order.OrderInvoiceService;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * C端陪护单-发票
 * @program: hospital-chaperone
 * @description: 订单|C端
 * @author: sp0372
 * @create: 2023-10-13 15:04
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/order/invoice")
public class OrderInvoiceController {
    private final OrderInvoiceService orderEstimateService;

    /**
     * 创建
     */
    @Idempotent(value = OrderKey.BILL_INVOICE_PREFIX + " + #request.billId")
    @PostMapping("create")
    public SingleResponse<OrderInvoiceIdResponse> create(@Validated @RequestBody OrderInvoiceCreateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderEstimateService.create(request);
        });
    }

    /**
     * 查询
     */
    @PostMapping("getByBillId")
    public SingleResponse<OrderInvoiceResponse> getByBillId(@Validated @RequestBody OrderInvoiceRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderEstimateService.getByBillId(request.getBillId());
        });
    }
}
