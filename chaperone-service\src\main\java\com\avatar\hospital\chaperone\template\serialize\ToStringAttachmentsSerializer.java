package com.avatar.hospital.chaperone.template.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.Objects;

/**
 * org 0 2 3 返回未null
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 16:36
 **/
public class ToStringAttachmentsSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String attachments, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {

        if (Objects.isNull(attachments)) {
            jsonGenerator.writeNull();
        }else {
            if (attachments.endsWith(",")) {
                attachments = attachments.substring(0,attachments.length()-1);
            }
            jsonGenerator.writeString(attachments);
        }
    }
}
