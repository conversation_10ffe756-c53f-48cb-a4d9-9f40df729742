package com.avatar.hospital.chaperone.database.order.repository.impl;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderPresetValueDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderPresetValueStatus;
import com.avatar.hospital.chaperone.database.order.enums.OrderPresetValueType;
import com.avatar.hospital.chaperone.database.order.mapper.OrderPresetValueMapper;
import com.avatar.hospital.chaperone.database.order.repository.OrderPresetValueRepositoryService;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单信息预设置; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Service
public class OrderPresetValueRepositoryServiceImpl extends ServiceImpl<OrderPresetValueMapper, OrderPresetValueDO> implements OrderPresetValueRepositoryService {

    @Override
    public void add(OrderPresetValueDO presetValue) {
        LambdaQueryWrapper<OrderPresetValueDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderPresetValueDO::getStatus, OrderPresetValueStatus.NO.getStatus());
        queryWrapper.eq(OrderPresetValueDO::getType, presetValue.getType());
        queryWrapper.eq(OrderPresetValueDO::getOrderId, presetValue.getOrderId());
        OrderPresetValueDO presetValueDO = getOne(queryWrapper);

        if (Objects.isNull(presetValueDO)) {
            save(presetValue);
        } else {
            presetValueDO.setVal(presetValue.getVal());
            presetValueDO.setTime(presetValue.getTime());
            presetValueDO.setUpdateBy(presetValue.getUpdateBy());
            presetValueDO.setUpdatedAt(LocalDateTime.now());
            updateById(presetValueDO);
        }
    }

    @Override
    public void clean(Long orderId, Integer type) {
        LambdaQueryWrapper<OrderPresetValueDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderPresetValueDO::getStatus, OrderPresetValueStatus.NO.getStatus());
        queryWrapper.eq(OrderPresetValueDO::getType, type);
        queryWrapper.eq(OrderPresetValueDO::getOrderId, orderId);
        List<OrderPresetValueDO> list = list(queryWrapper);
        list.forEach(item -> item.setStatus(OrderPresetValueStatus.CLEAN.getStatus()));
        updateBatchById(list);
    }

    @Override
    public OrderPresetValueDO getByOrderNursing(Long orderId) {
        LambdaQueryWrapper<OrderPresetValueDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderPresetValueDO::getStatus, OrderPresetValueStatus.NO.getStatus());
        queryWrapper.eq(OrderPresetValueDO::getType, OrderPresetValueType.NURSING.getStatus());
        queryWrapper.eq(OrderPresetValueDO::getOrderId, orderId);
        OrderPresetValueDO orderPresetValueDO = getOne(queryWrapper);
        return orderPresetValueDO;
    }

    @Override
    public void use(Long orderId, Integer type) {
        LambdaQueryWrapper<OrderPresetValueDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrderPresetValueDO::getStatus, OrderPresetValueStatus.NO.getStatus());
        queryWrapper.eq(OrderPresetValueDO::getType, type);
        queryWrapper.eq(OrderPresetValueDO::getOrderId, orderId);
        List<OrderPresetValueDO> list = list(queryWrapper);
        list.forEach(item -> item.setStatus(OrderPresetValueStatus.YES.getStatus()));
        updateBatchById(list);
    }

    private LambdaQueryWrapper<OrderPresetValueDO> queryWrapper() {
        LambdaQueryWrapper<OrderPresetValueDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPresetValueDO::getDeleted, OrderConst.NO_DELETED);
        return queryWrapper;
    }
}
