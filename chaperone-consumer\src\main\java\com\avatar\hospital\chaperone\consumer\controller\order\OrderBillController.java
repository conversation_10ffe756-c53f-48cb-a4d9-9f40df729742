package com.avatar.hospital.chaperone.consumer.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.*;
import com.avatar.hospital.chaperone.service.order.OrderBillService;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.utils.RequestUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * C端陪护单-账单
 * @program: hospital-chaperone
 * @description: 订单|C端
 * @author: sp0372
 * @create: 2023-10-13 15:04
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/bill")
public class OrderBillController {
    private final OrderBillService orderBillService;

    /**
     * 账单查询
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<OrderBillResponse>> paging(@RequestBody OrderBillCPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderBillService.pagingForC(request);
        });
    }

    /**
     * 账单详情
     */
    @PostMapping("detail")
    public SingleResponse<OrderBillDetailResponse> detail(@RequestBody OrderBillDetailRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderBillService.detail(request);
        });
    }

    /**
     * 生成支付单
     */
    @Idempotent(value = OrderKey.BILL_MODIFY_PREFIX + " + #request.billId")
    @PostMapping("pay")
    public SingleResponse<OrderBillPayResponse> pay(@Validated @RequestBody OrderBillPayRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            request.setIp(RequestUtils.getRequestHost());
            return orderBillService.pay(request);
        });
    }

    /**
     * 查询支付单信息
     */
    @PostMapping("payInfo")
    public SingleResponse<OrderBillPayResponse> payInfo(@RequestBody OrderBillPayRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderBillService.payInfo(request);
        });
    }


}
