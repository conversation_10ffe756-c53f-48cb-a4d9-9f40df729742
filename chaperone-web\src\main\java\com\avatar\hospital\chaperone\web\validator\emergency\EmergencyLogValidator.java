package com.avatar.hospital.chaperone.web.validator.emergency;

import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogAddRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogPagingRequest;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogUpdateRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;

/**
 * @author:sp0420
 * @Description:
 */
public class EmergencyLogValidator {

    public static void addValidate(EmergencyLogAddRequest request) {
        // 设置创建人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setCreateBy(accountId);
    }

    public static void updateValidate(EmergencyLogUpdateRequest request) {
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void pagingValidate(EmergencyLogPagingRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageIndex(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageSize(), ErrorCode.PARAMETER_ERROR);
    }
}
