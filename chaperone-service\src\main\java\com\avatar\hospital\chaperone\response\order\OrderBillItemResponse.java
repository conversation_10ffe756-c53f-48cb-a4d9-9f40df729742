package com.avatar.hospital.chaperone.response.order;

import com.avatar.hospital.chaperone.database.item.enums.ItemStatus;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description: 订单简易信息
 * @author: sp0372
 * @create: 2023-10-11 16:55
 **/
@Data
public class OrderBillItemResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemId;


    /**
     * 天数/数量
     */
    private Integer number;

    /**
     * 单价
     */
    private Integer price;

    /**
     * 总金额，单位分
     */
    private Integer totalPrice;

    // 套餐信息

    /**
     * 套餐类型：参考，server_type
     * @see com.avatar.hospital.chaperone.database.item.enums.ItemServerType
     */
    private Integer serverType;



    /**
     * 套餐名称
     */
    private String name;

    /**
     * 状态(影响C端展示)：1 上架，0 下架
     * @see ItemStatus
     */
    private Integer status;

    /**
     * 护工星级 1 ~ 5 星
     */
    private Integer nursingStar;

    /**
     * 详细描述
     */
    private String remark;

    /**
     * 封面图
     */
    private String coverPicture;

    /**
     * 开始时间 0 ~ 23
     */
    private Integer chargingTime;
}
