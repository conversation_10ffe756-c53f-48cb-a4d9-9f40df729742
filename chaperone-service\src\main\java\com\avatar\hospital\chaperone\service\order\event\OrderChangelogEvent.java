package com.avatar.hospital.chaperone.service.order.event;

import com.avatar.hospital.chaperone.database.order.enums.OrderLogEvent;
import com.avatar.hospital.chaperone.service.order.dto.OrderChangeLogDTO;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.utils.CompareDTO;
import com.avatar.hospital.chaperone.utils.DateUtils;
import lombok.Data;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import javax.annotation.Nullable;

/**
 * @program: hospital-chaperone
 * @description: 陪护单变更事件
 * @author: sp0372
 * @create: 2023-10-12 15:02
 **/
public class OrderChangelogEvent extends ApplicationEvent {
    @Getter
    private Holder holder;
    public OrderChangelogEvent(Object source,Holder holder) {
        super(source);
        this.holder = holder;
    }

    public static final OrderChangelogEvent build(Object source,Holder holder) {
        OrderChangelogEvent obj = new OrderChangelogEvent(source,holder);
        return obj;
    }

    public static final OrderChangelogEvent build(Object source,OrderLogEvent event,OrderChangeLogDTO.AbsCompareDTO before, OrderChangeLogDTO.AbsCompareDTO after,Operator operatorUser) {
        OrderChangelogEvent obj = new OrderChangelogEvent(source,Holder.build(event,before,after,event.getClazz(),operatorUser));
        return obj;
    }

    public static final OrderChangelogEvent build(Object source,String time,OrderLogEvent event,OrderChangeLogDTO.AbsCompareDTO before, OrderChangeLogDTO.AbsCompareDTO after,Operator operatorUser) {
        OrderChangelogEvent obj = new OrderChangelogEvent(source,Holder.build(time,event,before,after,event.getClazz(),operatorUser));
        return obj;
    }


    @Data
    public static class Holder {

        /**
         * 操作时间
         */
        private String time;

        /**
         * 操作事件
         */
        private OrderLogEvent event;
        /**
         * 更新之前
         */
        @Nullable
        private OrderChangeLogDTO.AbsCompareDTO before;
        /**
         * 更新之后
         */
        private OrderChangeLogDTO.AbsCompareDTO after;
        /**
         * 比较对象类对象
         */
        private Class<CompareDTO> clazz;

        /**
         * 操作用户
         */
        private Operator operatorUser;

        public static final Holder build(String time, OrderLogEvent event, OrderChangeLogDTO.AbsCompareDTO before, OrderChangeLogDTO.AbsCompareDTO after,Class clazz,Operator operatorUser) {
            Holder obj = new Holder();
            obj.setEvent(event);
            obj.setBefore(before);
            obj.setAfter(after);
            obj.setTime(time);
            obj.setClazz(clazz);
            obj.setOperatorUser(operatorUser);
            return obj;
        }

        public static final Holder build(OrderLogEvent event,OrderChangeLogDTO.AbsCompareDTO before, OrderChangeLogDTO.AbsCompareDTO after,Class clazz,Operator operatorUser) {
            return build(DateUtils.dateTimeStr(),event,before,after,clazz,operatorUser);
        }
    }



}
