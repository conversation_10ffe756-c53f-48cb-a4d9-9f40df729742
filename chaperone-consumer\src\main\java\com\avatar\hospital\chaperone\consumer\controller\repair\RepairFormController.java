package com.avatar.hospital.chaperone.consumer.controller.repair;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.request.repair.RepairFormCreateRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormDetailRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormPageCRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormCResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormDetailResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormIdResponse;
import com.avatar.hospital.chaperone.service.repair.RepairFormService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * C端工程-报修单
 *
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-30 14:29
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/repair/base")
@Deprecated
public class RepairFormController {
    private final RepairFormService repairFormService;

    /**
     * 查询-分页
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<RepairFormCResponse>> paging(@RequestBody RepairFormPageCRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormService.pagingForC(request);
        });
    }

    /**
     * 查询-详情
     */
    @PostMapping("detail")
    public SingleResponse<RepairFormDetailResponse> detail(@RequestBody RepairFormDetailRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormService.detailForC(request);
        });
    }

    /**
     * 创建
     */
    @PostMapping("create")
    public SingleResponse<RepairFormIdResponse> create(@RequestBody RepairFormCreateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormService.createForC(request);
        });
    }

}
