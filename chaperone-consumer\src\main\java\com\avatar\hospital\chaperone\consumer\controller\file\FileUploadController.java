package com.avatar.hospital.chaperone.consumer.controller.file;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.component.oss.OssComponent;
import com.avatar.hospital.chaperone.consumer.utils.ConsumerAccountUtils;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.response.file.FileUploadResponse;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.FileUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * C端文件上传
 *
 * <AUTHOR>
 * @since 2023/10/18
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/consumer/file")
public class FileUploadController {

    private final OssComponent ossComponent;

    /**
     * 允许上传的图片文件类型
     */
    private static final List<String> IMAGE_ALLOWED_TYPES = Arrays.asList("jpeg", "png", "jpg", "bmp", "gif", "tif", "webp");

    /**
     * 上传文件
     */
    @PostMapping(value = "/upload/image")
    public SingleResponse<FileUploadResponse> uploadImage(@RequestParam(value = "file", required = false) MultipartFile file) {
        return TemplateProcess.doProcess(log, () -> {
            AssertUtils.isNotNull(file, ErrorCode.FILE_EMPTY_ERROR);
            AssertUtils.isFalse(file.isEmpty(), ErrorCode.FILE_EMPTY_ERROR);
            Long accountId = ConsumerAccountUtils.getCurrentAccountIdAndThrow();

            log.info("FileUploadController uploadImage accountId:{},fileName:{}", accountId, file.getOriginalFilename());

            String fileExtName = FileUtils.getFileExtName(file.getOriginalFilename());
            AssertUtils.hasText(fileExtName, ErrorCode.FILE_FORMAT_ERROR);
            AssertUtils.isTrue(IMAGE_ALLOWED_TYPES.contains(fileExtName), ErrorCode.FILE_TYPE_NOT_ALLOW_UPLOAD_ERROR);

            FileUploadResponse fileUploadResponse = ossComponent.uploadFile(accountId, file);
            AssertUtils.isNotNull(fileUploadResponse, ErrorCode.FILE_UPLOAD_ERROR);

            return fileUploadResponse;
        });
    }

}
