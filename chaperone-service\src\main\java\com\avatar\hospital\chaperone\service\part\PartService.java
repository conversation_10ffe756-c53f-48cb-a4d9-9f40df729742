package com.avatar.hospital.chaperone.service.part;

import com.avatar.hospital.chaperone.database.part.dataobject.PartBatchDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.request.part.PartBatchRequest;
import com.avatar.hospital.chaperone.request.part.QueryExportRequest;
import com.avatar.hospital.chaperone.request.part.QueryRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.service.part.dto.PartExportDTO;
import com.avatar.hospital.chaperone.response.part.PartVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 15:08
 */
public interface PartService {

    boolean update2delete(PartBatchRequest partBatchRequest);

    PageResponse<PartVO> paging(QueryRequest request);

    List<PartExportDTO> export(QueryExportRequest request);

    Boolean entryStock(LocalDateTime entryTime, Long stockApplyId, PartBatchDO partBatchDO,PartStockApplyRefPartBatchDO partBatch, Long operator);
}
