package com.avatar.hospital.chaperone.request.part;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:53
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PartApplyPassRequest implements OperatorReq,Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审批单ID
     */
    private Long id;


    /**
     * 审核说明
     */
    private String auditRemark;

    /**
     * 操作人
     */
    public Operator operatorUser;
}
