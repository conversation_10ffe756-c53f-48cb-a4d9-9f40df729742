package com.avatar.hospital.chaperone.service.caccount.impl;

import com.avatar.hospital.chaperone.builder.caccount.ConsumerAccountBuilder;
import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.caccount.repository.ConsumerAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountBasicInfoWriteConditionRequest;
import com.avatar.hospital.chaperone.request.caccount.ConsumerAccountUpdateRequest;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountBasicInfoWriteConditionResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountDetailResponse;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountUpdateResponse;
import com.avatar.hospital.chaperone.service.caccount.ConsumerAccountService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ConsumerAccountServiceImpl implements ConsumerAccountService {

    private final ConsumerAccountRepositoryService consumerAccountRepositoryService;

    @Override
    public ConsumerAccountBasicInfoWriteConditionResponse basicInfoWriteCondition(ConsumerAccountBasicInfoWriteConditionRequest request) {
        AccountDO accountDO = consumerAccountRepositoryService.findById(request.getAccountId());
        AssertUtils.isNotNull(accountDO, ErrorCode.CONSUMER_ACCOUNT_NOT_EXIST);
        boolean phoneNumberWrite = StringUtils.isNotBlank(accountDO.getPhoneNumber());
        return ConsumerAccountBasicInfoWriteConditionResponse.builder().phoneNumberWrite(phoneNumberWrite).build();
    }

    @Override
    public ConsumerAccountUpdateResponse update(ConsumerAccountUpdateRequest request) {
        AccountDO accountDO = consumerAccountRepositoryService.findById(request.getAccountId());
        AssertUtils.isNotNull(accountDO, ErrorCode.CONSUMER_ACCOUNT_NOT_EXIST);

        Boolean result = consumerAccountRepositoryService.incrementUpdate(ConsumerAccountBuilder.buildAccountDO(request));
        AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);

        return ConsumerAccountUpdateResponse.builder().success(true).build();
    }

    @Override
    public ConsumerAccountDetailResponse detail(Long accountId) {
        AccountDO accountDO = consumerAccountRepositoryService.findById(accountId);
        AssertUtils.isNotNull(accountDO, ErrorCode.CONSUMER_ACCOUNT_NOT_EXIST);
        return ConsumerAccountBuilder.buildConsumerAccountDetailResponse(accountDO);
    }

    @Override
    public ConsumerAccountDetailResponse getByMobile(String mobile) {
        AccountDO accountDO = consumerAccountRepositoryService.findByPhoneNumber(mobile);
        AssertUtils.isNotNull(accountDO, ErrorCode.CONSUMER_ACCOUNT_NOT_EXIST);
        return ConsumerAccountBuilder.buildConsumerAccountDetailResponse(accountDO);
    }


}
