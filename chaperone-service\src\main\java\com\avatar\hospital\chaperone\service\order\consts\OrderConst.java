package com.avatar.hospital.chaperone.service.order.consts;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.utils.OrderUtils;

import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 订单常量
 * @author: sp0372
 * @create: 2023-10-13 16:37
 **/
public class OrderConst {
    /**
     * 未删除
     */
    public static final Long NO_DELETED= 0L;
    /**
     * 父账单
     */
    public static final Long TOP_BILL= 0L;
    /**
     * 0
     */
    public static final Integer ZERO = 0;
    /**
     * 默认比例
     */
    public static final Integer DEFAULT_RATE = -1;
    /**
     * 没有退款金额
     */
    public static final Integer NO_REFUND_PRICE = 0;
    /**
     * 默认备注
     */
    public static final String DEFAULT_REMARK = "";
    /**
     * 空字符串
     */
    public static final String EMPTY = "";
    /**
     * 没有则扣
     */
    public static final Integer NO_DISCOUNT = OrderUtils.DISCOUNT_RATE;
    /**
     * 优惠金额未0
     */
    public static final Integer NO_DISCOUNT_PRICE = 0;
    /**
     * 消费记录 有效版本
     */
    public static final Long CONSUMER_LOG_VERSION = 1L;


    /**
     * 定时任务操作人ID
     */
    public static final Operator JOB_OPERATOR = Operator.of(1L,"1","定时任务",3);

    /**
     * 支付商品描述
     */
    public static final String PAY_DESCRIPTION = "服务套餐";

    public static final String WX_NOTIFY_HEAD_SERIAL = "Wechatpay-Serial";
    public static final String WX_NOTIFY_HEAD_NONCE = "Wechatpay-Nonce";
    public static final String WX_NOTIFY_HEAD_SIGNATURE = "Wechatpay-Signature";
    public static final String WX_NOTIFY_HEAD_TIMESTAMP = "Wechatpay-Timestamp";

    public static final String SUCCESS = "SUCCESS";

}
