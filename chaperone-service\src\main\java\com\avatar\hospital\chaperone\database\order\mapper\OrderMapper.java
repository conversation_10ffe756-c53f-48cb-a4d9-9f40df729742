package com.avatar.hospital.chaperone.database.order.mapper;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单主表; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderMapper extends BaseMapper<OrderDO> {

    List<OrderDO> findAllByNursingId(@Param("nursingId") Long nursingId,
                                     @Param("orderId")  Long orderId,
                                     @Param("startDate")  Integer startDate,
                                     @Param("endDate") Integer endDate);
}
