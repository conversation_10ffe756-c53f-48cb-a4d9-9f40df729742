package com.avatar.hospital.chaperone.web.controller.order;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.annotation.Idempotent;
import com.avatar.hospital.chaperone.builder.order.OrderBuilder;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.order.OrderConsumerExportPageRequest;
import com.avatar.hospital.chaperone.request.order.OrderConsumerPageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.order.OrderConsumerLogResponse;
import com.avatar.hospital.chaperone.service.order.*;
import com.avatar.hospital.chaperone.service.order.consts.OrderKey;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.utils.ExportUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * B端陪护单-消费明细
 * @program: hospital-chaperone
 * @description: 套餐
 * @author: sp0372
 * @create: 2023-10-13 10:07
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/order/consumerLog")
public class OrderConsumerLogController {
    private final OrderConsumerlogService orderConsumerlogService;

    /**
     * 查询-订单消费明细
     * @param request
     * @return
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<OrderConsumerLogResponse>> paging(@RequestBody OrderConsumerPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return orderConsumerlogService.paging(request);
        });
    }

    /**
     * 导出-订单消费明细
     * @param param
     * @return
     */
    @Idempotent(value = OrderKey.CONSUMERLOG_EXPORT_PREFIX)
    @PostMapping("export")
    public void export(@RequestBody OrderConsumerExportPageRequest param, HttpServletResponse response) throws IOException {
        // paging 超过数量限制，直接返回
        OrderConsumerPageRequest req = OrderBuilder.toOrderConsumerPageRequest(param);
        req.setPageSize(1);
        PageResponse<OrderConsumerLogResponse> paging = orderConsumerlogService.paging(req);
        if (Objects.nonNull(paging) && paging.getTotal() > ExportUtil.MAX_EXPORT_SIZE) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            SingleResponse singleResponse
                    = SingleResponse.buildFailure(ErrorCode.EXPORT_LIMIT_ERROR.getErrorCode(), ErrorCode.EXPORT_LIMIT_ERROR.getErrorMessage());
            response.getWriter().println(JSON.toJSONString(singleResponse));
            return;
        }
        ExportUtil.export(response,param,orderConsumerlogService::exportPaging, ExcelTypeEnum.XLS);
    }

}
