package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderInvoiceNeedCertificate;
import com.avatar.hospital.chaperone.database.order.enums.OrderInvoiceType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单-发票;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_order_invoice")
public class OrderInvoiceDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 订单ID
     */
      private Long orderId;

  /**
   * 账单ID
   */
  private Long billId;

      /**
     * 是否需要付费凭证,0 不需要，1 需要
       * @see OrderInvoiceNeedCertificate
     */
      private Integer needCertificate;

      /**
     * 类型: 1 电子收据 2 电子发票 3 实体收据
       * @see OrderInvoiceType
     */
      private Integer invoiceType;

      /**
     * 电子邮箱
     */
      private String email;

      /**
     * 纳税人名称
     */
      private String taxpayerName;

      /**
     * 纳税人识别号
     */
      private String taxpayerNo;


}
