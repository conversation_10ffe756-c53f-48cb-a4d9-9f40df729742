package com.avatar.hospital.chaperone.database.plan.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanRefExecutorDO;
import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanRefExecutorDO;
import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.database.plan.mapper.MaintenancePlanRefExecutorMapper;
import com.avatar.hospital.chaperone.database.plan.repository.MaintenancePlanRefExecutorRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 巡检计划关联人员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class MaintenancePlanRefExecutorRepositoryServiceImpl extends ServiceImpl<MaintenancePlanRefExecutorMapper, MaintenancePlanRefExecutorDO> implements MaintenancePlanRefExecutorRepositoryService {

    @Override
    public boolean update2delete(PlanRequest request) {

        LambdaUpdateWrapper<MaintenancePlanRefExecutorDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MaintenancePlanRefExecutorDO::getPlanId, request.getId());
        updateWrapper.set(MaintenancePlanRefExecutorDO::getUpdateBy, request.getOperator());
        updateWrapper.set(MaintenancePlanRefExecutorDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.setSql(" deleted = id");
        update(updateWrapper);
        return true;
    }

    @Override
    public List<Long> getRefIds(Long planId) {

        LambdaQueryWrapper<MaintenancePlanRefExecutorDO> queryWrapper = queryWrapper();
        queryWrapper.eq(MaintenancePlanRefExecutorDO::getPlanId, planId);
        queryWrapper.eq(MaintenancePlanRefExecutorDO::getDeleted, DeletedEnum.NO.getStatus());
        List<MaintenancePlanRefExecutorDO> stockApplyRefPartBatchDOS = this.list(queryWrapper);
        return stockApplyRefPartBatchDOS.stream().map(o -> o.getExecutorAccountId()).collect(Collectors.toList());
    }

    @Override
    public Set<Long> getRefIds(Set<Long> planIds) {
        LambdaQueryWrapper<MaintenancePlanRefExecutorDO> queryWrapper = queryWrapper();
        queryWrapper.in(MaintenancePlanRefExecutorDO::getPlanId, planIds);
        queryWrapper.eq(MaintenancePlanRefExecutorDO::getDeleted, DeletedEnum.NO.getStatus());
        List<MaintenancePlanRefExecutorDO> refOrgDOS = this.list(queryWrapper);
        return refOrgDOS.stream().map(o -> o.getExecutorAccountId()).collect(Collectors.toSet());

    }

    @Override
    public Set<Long> getPlanByAccount(Long accountId) {
        LambdaQueryWrapper<MaintenancePlanRefExecutorDO> queryWrapper = queryWrapper();
        queryWrapper.eq(PlanRefExecutorDO::getDeleted, DeletedEnum.NO.getStatus());
        queryWrapper.orderByDesc(PlanRefExecutorDO::getId);
        queryWrapper.eq(PlanRefExecutorDO::getExecutorAccountId, accountId);

        List<MaintenancePlanRefExecutorDO> refExecutorDOS = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(refExecutorDOS)) {
            return Sets.newHashSet();
        }
        return refExecutorDOS.stream().map(PlanRefExecutorDO::getPlanId).collect(Collectors.toSet());
    }

    private LambdaQueryWrapper<MaintenancePlanRefExecutorDO> queryWrapper() {
        LambdaQueryWrapper<MaintenancePlanRefExecutorDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaintenancePlanRefExecutorDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}

