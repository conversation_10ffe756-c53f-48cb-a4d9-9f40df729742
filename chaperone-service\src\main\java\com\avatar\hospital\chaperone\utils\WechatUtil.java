package com.avatar.hospital.chaperone.utils;

import com.avatar.hospital.chaperone.response.order.OrderBillPayInfoResponse;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-07 17:24
 **/
public class WechatUtil {
    private final static String raw = "appId=%s&nonceStr=%s&package=%s&signType=%s&timeStamp=%s";
    public static final String  paySign(OrderBillPayInfoResponse payInfo,String mchKey) {
        String row = String.format(raw, payInfo.getAppId(), payInfo.getNonceStr(), payInfo.getPackageValue(), payInfo.getSignType(),payInfo.getTimeStamp());
        row += "&key=" + mchKey;
        String sign = DigestUtils.md5DigestAsHex(row.getBytes(StandardCharsets.UTF_8)).toUpperCase();
        return sign;
    }
}
