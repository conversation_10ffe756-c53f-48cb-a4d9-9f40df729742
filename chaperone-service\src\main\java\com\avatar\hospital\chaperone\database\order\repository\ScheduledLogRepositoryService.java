package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.ScheduledLogDO;
import com.avatar.hospital.chaperone.job.ScheduledType;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 定时任务执行记录; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
public interface ScheduledLogRepositoryService extends IService<ScheduledLogDO> {

    ScheduledLogDO create(ScheduledType scheduledType);
}
