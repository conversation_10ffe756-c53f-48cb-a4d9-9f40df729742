package com.avatar.hospital.chaperone.builder.caccount;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.caccount.enums.AccountStatus;
import com.avatar.hospital.chaperone.database.caccount.enums.AccountTripartiteType;
import com.avatar.hospital.chaperone.request.caccount.dto.ConsumerLoginTripartiteDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
public class ConsumerLoginBuilder {

    public static AccountDO buildAccountDO(WxMaUserInfo userInfo) {
        if (userInfo == null) {
            return null;
        }
        AccountDO accountDO = new AccountDO();
        accountDO.setNickName(userInfo.getNickName());
        accountDO.setAvatarUrl(userInfo.getAvatarUrl());
        accountDO.setStatus(AccountStatus.ENABLE.getStatus());
        return accountDO;
    }

    public static ConsumerLoginTripartiteDTO buildConsumerLoginTripartite(WxMaJscode2SessionResult session, String appId) {
        if (session == null || StringUtils.isBlank(appId)) {
            return null;
        }
        ConsumerLoginTripartiteDTO consumerLoginTripartite = new ConsumerLoginTripartiteDTO();
        consumerLoginTripartite.setAppId(appId);
        consumerLoginTripartite.setOpenId(session.getOpenid());
        consumerLoginTripartite.setUnionId(session.getUnionid());
        consumerLoginTripartite.setType(AccountTripartiteType.WECHAT.getType());
        return consumerLoginTripartite;
    }
}
