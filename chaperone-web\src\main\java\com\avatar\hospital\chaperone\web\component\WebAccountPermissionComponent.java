package com.avatar.hospital.chaperone.web.component;

import com.avatar.hospital.chaperone.component.baccount.RoleComponent;
import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.MenuDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleMenuDO;
import com.avatar.hospital.chaperone.database.baccount.enums.MenuType;
import com.avatar.hospital.chaperone.database.baccount.enums.MenuStatus;
import com.avatar.hospital.chaperone.database.baccount.repository.AccountRoleRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.MenuRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleMenuRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.WebAccountRepositoryService;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/12
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WebAccountPermissionComponent {

    private final WebAccountRepositoryService webAccountRepositoryService;
    private final AccountRoleRepositoryService accountRoleRepositoryService;
    private final RoleRepositoryService roleRepositoryService;
    private final MenuRepositoryService menuRepositoryService;
    private final RoleMenuRepositoryService roleMenuRepositoryService;
    private final RoleComponent roleComponent;


    /**
     * 返回用户角色权限字符串集合
     *
     * @param accountId -
     * @return -
     */
    public List<String> getAccountRoleList(Long accountId) {
        if (accountId == null) {
            return Collections.emptyList();
        }
        AccountDO account = webAccountRepositoryService.findById(accountId);
        if (account == null || !account.enable()) {
            return Collections.emptyList();
        }
        List<RoleDO> roleList = roleComponent.findAccountRoleList(accountId);
        if (CollectionUtils.isEmpty(roleList)) {
            return Collections.emptyList();
        }
        return roleList.stream().map(RoleDO::getRoleKey).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 返回用户权限集合
     *
     * @param roleKey -
     * @return -
     */
    public List<String> getAccountPermissionList(String roleKey) {
        if (StringUtils.isBlank(roleKey)) {
            return Collections.emptyList();
        }
        RoleDO role = roleRepositoryService.findByRoleKey(roleKey);
        if (role == null) {
            return Collections.emptyList();
        }
        List<RoleMenuDO> roleMenuList = roleMenuRepositoryService.findByRoleIds(Sets.newHashSet(role.getId()));
        if (CollectionUtils.isEmpty(roleMenuList)) {
            return Collections.emptyList();
        }
        List<MenuDO> menuList = menuRepositoryService.findByIdsAndTypesAndStatus(roleMenuList.stream().map(RoleMenuDO::getMenuId).collect(Collectors.toSet())
                , Sets.newHashSet(MenuType.AUTHORITY.getType()), MenuStatus.ENABLE.getStatus());
        if (CollectionUtils.isEmpty(menuList)) {
            return Collections.emptyList();
        }
        return menuList.stream().map(MenuDO::getMenuKey).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }
}
