package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderPayDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单-支付记录; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderPayRepositoryService extends IService<OrderPayDO> {

    /**
     * 根据订单查询所有支付单
     * @param orderIds
     * @return
     */
    List<OrderPayDO> findByOrderIdList(List<Long> orderIds);

    /**
     * 获取在有效时间范围内的支付单
     *
     * @param billId
     * @param price
     * @return
     */
    OrderPayDO getPay(Long billId, Integer price);

    /**
     * 支付单
     * @param billId
     * @return
     */
    OrderPayDO getPaySuccess(Long billId);
}
