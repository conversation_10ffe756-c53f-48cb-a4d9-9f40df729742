package com.avatar.hospital.chaperone.database.baccount.repository;

import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationControlDO;
import com.baomidou.mybatisplus.extension.service.IService;


/**
 * <p>
 * B端组织机构表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface OrganizationControlRepositoryService extends IService<OrganizationControlDO> {

    /**
     * 根据组织机构ID查询
     *
     * @param orgId -
     * @return -
     */
    OrganizationControlDO findByOrgId(Long orgId);
}
