package com.avatar.hospital.chaperone.service.baccount.impl;

import com.avatar.hospital.chaperone.builder.baccount.RoleBuilder;
import com.avatar.hospital.chaperone.component.baccount.StpComponent;
import com.avatar.hospital.chaperone.database.baccount.dataobject.MenuDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleMenuDO;
import com.avatar.hospital.chaperone.database.baccount.repository.MenuRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleMenuRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.RoleAddRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleAllocationMenuRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleMenuRequest;
import com.avatar.hospital.chaperone.request.baccount.RolePagingRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleAddResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleAllocationMenuResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleMenuResponse;
import com.avatar.hospital.chaperone.response.baccount.RolePagingResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleUpdateResponse;
import com.avatar.hospital.chaperone.service.baccount.RoleService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RoleServiceImpl implements RoleService {

    private final RoleRepositoryService roleRepositoryService;
    private final MenuRepositoryService menuRepositoryService;
    private final RoleMenuRepositoryService roleMenuRepositoryService;
    private final StpComponent stpComponent;

    @Override
    public RoleAddResponse add(RoleAddRequest request) {
        AssertUtils.isNull(roleRepositoryService.findByName(request.getName()), ErrorCode.ROLE_NAME_EXIST);
        AssertUtils.isNull(roleRepositoryService.findByRoleKey(request.getRoleKey()), ErrorCode.ROLE_KEY_EXIST);

        Long roleId = roleRepositoryService.add(RoleBuilder.buildRoleDO(request));
        return RoleAddResponse.builder().roleId(roleId).build();
    }

    @Override
    public RoleUpdateResponse update(RoleUpdateRequest request) {
        RoleDO roleDO = roleRepositoryService.findById(request.getId());
        AssertUtils.isNotNull(roleDO, ErrorCode.ROLE_NOT_EXIST);

        if (!Objects.equals(request.getName(), roleDO.getName())) {
            RoleDO role = roleRepositoryService.findByName(request.getName());
            AssertUtils.isNull(role, ErrorCode.ROLE_NAME_EXIST);
        }
        if (!Objects.equals(request.getRoleKey(), roleDO.getRoleKey())) {
            RoleDO role = roleRepositoryService.findByRoleKey(request.getRoleKey());
            AssertUtils.isNull(role, ErrorCode.ROLE_KEY_EXIST);
        }

        Boolean result = roleRepositoryService.incrementUpdate(RoleBuilder.buildRoleDO(request));
        AssertUtils.isTrue(result, ErrorCode.UPDATE_ERROR);
        return RoleUpdateResponse.builder().success(true).build();
    }

    @Override
    public RoleDeleteResponse delete(RoleDeleteRequest request) {
        List<RoleDO> roleList = roleRepositoryService.findByIds(request.getIds());
        if (CollectionUtils.isEmpty(roleList)) {
            return RoleDeleteResponse.builder().success(true).build();
        }
        Set<Long> roleIds = roleList.stream().map(RoleDO::getId).collect(Collectors.toSet());
        Boolean result = roleRepositoryService.deleteByIds(roleIds, request.getUpdateBy());
        // 删除角色对应的权限缓存
        if (Boolean.TRUE.equals(result)) {
            stpComponent.deleteRolePermissionCache(roleList.stream().map(RoleDO::getRoleKey).collect(Collectors.toSet()));
        }
        return RoleDeleteResponse.builder().success(result).build();
    }

    @Override
    public RoleAllocationMenuResponse allocationMenu(RoleAllocationMenuRequest request) {
        RoleDO roleDO = roleRepositoryService.findById(request.getRoleId());
        AssertUtils.isNotNull(roleDO, ErrorCode.ROLE_NOT_EXIST);

        List<MenuDO> menuDOList = menuRepositoryService.findByIds(request.getMenuIds());
        AssertUtils.notEmpty(menuDOList, ErrorCode.MENU_NOT_EXIST);

        Set<Long> menuIds = menuDOList.stream().map(MenuDO::getId).collect(Collectors.toSet());
        Boolean result = roleRepositoryService.allocationMenu(request.getRoleId(), menuIds);
        // 删除角色对应的权限缓存
        if (Boolean.TRUE.equals(result)) {
            stpComponent.deleteRolePermissionCache(roleDO.getRoleKey());
        }
        return RoleAllocationMenuResponse.builder().success(result).build();
    }

    @Override
    public PageResponse<RolePagingResponse> paging(RolePagingRequest request) {
        PageResponse<RoleDO> accountPageResponse = roleRepositoryService.paging(request.getPageIndex(), request.getPageSize(), RoleBuilder.buildRoleDO(request));
        return RoleBuilder.buildRolePagingResponse(accountPageResponse);
    }

    @Override
    public RoleDetailResponse detail(Long roleId) {
        RoleDO roleDO = roleRepositoryService.findById(roleId);
        AssertUtils.isNotNull(roleDO, ErrorCode.WEB_ACCOUNT_NOT_EXIST);
        return RoleBuilder.buildRoleDetailResponse(roleDO);
    }

    @Override
    public RoleMenuResponse menu(RoleMenuRequest request) {
        RoleDO roleDO = roleRepositoryService.findById(request.getRoleId());
        AssertUtils.isNotNull(roleDO, ErrorCode.WEB_ACCOUNT_NOT_EXIST);

        List<RoleMenuDO> roleMenuDOList = roleMenuRepositoryService.findByRoleIds(Sets.newHashSet(request.getRoleId()));
        if (CollectionUtils.isEmpty(roleMenuDOList)) {
            return RoleMenuResponse.builder().menuList(Collections.emptyList()).build();
        }
        Set<Long> menuIds = roleMenuDOList.stream().map(RoleMenuDO::getMenuId).collect(Collectors.toSet());
        List<MenuDO> menuDOList = menuRepositoryService.findByIds(menuIds);
        if (CollectionUtils.isEmpty(menuDOList)) {
            return RoleMenuResponse.builder().menuList(Collections.emptyList()).build();
        }
        return RoleMenuResponse.builder().menuList(RoleBuilder.buildRoleMenuResponse(menuDOList)).build();
    }

}
