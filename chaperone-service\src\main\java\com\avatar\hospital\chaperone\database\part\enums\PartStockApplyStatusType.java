package com.avatar.hospital.chaperone.database.part.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description (1 - 通过 ， 2 - 驳回 ， 0 - 待审批 ）
 * @date 2023/10/26 19:55
 */
@Getter
public enum PartStockApplyStatusType {
    NONE(-1, "未知"),
    WAITING(0, "待审批"),
    PASSED(1, "通过"),
    REJECT(2, "驳回"),
    ;

    private final Integer code;

    private final String describe;


    PartStockApplyStatusType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static PartStockApplyStatusType of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (PartStockApplyStatusType type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
