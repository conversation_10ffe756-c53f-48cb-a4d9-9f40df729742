<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.nursing.mapper.NursingDayMapper">
    <select id="countByB" resultType="java.lang.Long">
        SELECT count(*)
        FROM (
        SELECT a1.id
        FROM `t_nursing_day` a1
                 LEFT JOIN `t_nursing_order` a2 on a1.`nursing_id` = a2.`nursing_id` and a1.date = a2.date
        <!--                 LEFT JOIN `t_order` a3 on a2.`order_id` = a3.`id`-->
        <where>
            <if test="orderId != null">
                and a2.order_id = #{orderId}
            </if>
            <if test="nursingId != null">
                and a1.nursing_id = #{nursingId}
            </if>
            <if test="status != null">
                <if test="status == 0">
                    and a1.status = 0
                </if>
                <if test="status == 1">
                    and a2.id is null
                    and a1.status != 0

                </if>
                <if test="status == 2">
                    and a2.id is not null
                </if>
            </if>
            <if test="startTime != null">

                and a1.date >= #{startTime}
            </if>
            <if test="endTime != null">
                and #{endTime} >= a1.date
            </if>
            and a1.deleted = 0
            and (a2.deleted = 0
             or a2.deleted is null)
            <!--            and (a3.deleted = 0 or a3.deleted is null)-->
        </where>
        GROUP BY a1.id
        )a
    </select>


    <resultMap id="nursingDay" type="com.avatar.hospital.chaperone.response.nursing.NursingDayPagingResponse">
        <id column="id" property="id"/>
        <result column="date" property="date"/>
        <result column="nursing_id" property="nursingId"/>
        <result column="nursing_name" property="nursingName"/>
        <result column="status" property="status"/>
        <!--        <collection property="orderList" ofType="com.avatar.hospital.chaperone.response.nursing.NursingDayOrderResponse"-->
        <!--                    resultMap="nursingDayOrder"/>-->
    </resultMap>

    <resultMap id="nursingDayOrder" type="com.avatar.hospital.chaperone.response.nursing.NursingDayOrderResponse">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_name" property="orderName"/>
        <result column="order_date" property="orderDate"/>
        <result column="nursing_id" property="nursingId"/>
    </resultMap>

    <select id="listByB" resultMap="nursingDay">
        SELECT a1.id as id,
               a1.date,
               a1.nursing_id,
               a1.nursing_name,
               a1.status
        FROM `t_nursing_day` a1
                 LEFT JOIN `t_nursing_order` a2 on a1.`nursing_id` = a2.`nursing_id` and a1.date = a2.date
        <!--                 LEFT JOIN `t_order` a3 on a2.`order_id` = a3.`id`-->
        <where>
            <if test="orderId != null">
                and a2.order_id = #{orderId}
            </if>
            <if test="nursingId != null">
                and a1.nursing_id = #{nursingId}
            </if>
            <if test="status != null">
                <if test="status == 0">
                    and a1.status = 0
                </if>
                <if test="status == 1">
                    and a2.id is null
                    and a1.status != 0

                </if>
                <if test="status == 2">
                    and a2.id is not null
                </if>
            </if>
            <if test="startTime != null">

                and a1.date >= #{startTime}
            </if>
            <if test="endTime != null">
                and #{endTime} >= a1.date
            </if>
            and a1.deleted = 0
            and (a2.deleted = 0
             or a2.deleted is null)
            <!--            and (a3.deleted = 0 or a3.deleted is null)-->
        </where>
        GROUP BY a1.id
        <if test="sortType != null">
            <if test="sortType == 1">
                ORDER BY a1.date asc
            </if>
            <if test="sortType == 2">
                ORDER BY a1.date desc,a1.id desc
            </if>
        </if>
        limit ${(offset-1)*limit}, ${limit}
    </select>

    <select id="listByNursingOrderByB" resultMap="nursingDayOrder">
        select a2.id, a2.order_id, a3.patient_name as order_name, a2.date as order_date, a1.nursing_id
        FROM `t_nursing_order` a2
                 LEFT JOIN `t_nursing_day` a1 on a1.`nursing_id` = a2.`nursing_id` and a1.date = a2.date
                 LEFT JOIN `t_order` a3 on a2.`order_id` = a3.`id`
        <where>
            a1.nursing_id in
            <foreach collection="nursingIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and a1.deleted = 0
        </where>
        group by a2.id
    </select>
</mapper>