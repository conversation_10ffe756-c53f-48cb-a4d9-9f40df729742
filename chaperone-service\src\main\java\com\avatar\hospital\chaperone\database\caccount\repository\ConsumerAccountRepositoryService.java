package com.avatar.hospital.chaperone.database.caccount.repository;

import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.request.caccount.dto.ConsumerLoginTripartiteDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * C端用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface ConsumerAccountRepositoryService extends IService<AccountDO> {

    /**
     * 添加C端用户
     *
     * @param accountDO               -
     * @param consumerLoginTripartite -
     * @return -
     */
    Long add(AccountDO accountDO, ConsumerLoginTripartiteDTO consumerLoginTripartite);

    /**
     * 增量更新
     *
     * @param accountDO -
     * @return -
     */
    Boolean incrementUpdate(AccountDO accountDO);

    /**
     * 根据id查询
     *
     * @param accountId -
     * @return -
     */
    AccountDO findById(Long accountId);

    /**
     * 根据手机号码查询
     *
     * @param phoneNumber -
     * @return -
     */
    AccountDO findByPhoneNumber(String phoneNumber);
}
