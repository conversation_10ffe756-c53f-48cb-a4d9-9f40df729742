package com.avatar.hospital.chaperone.database.baccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum MenuType {

    /**
     * 菜单
     */
    CATALOGUE(1, "菜单"),

    /**
     * 按钮
     */
    BUTTON(2, "按钮"),

    /**
     * 接口
     */
    AUTHORITY(3, "接口"),


    ;

    private final Integer type;

    private final String describe;


    MenuType(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }

    public static MenuType of(Integer type) {
        if (type == null) {
            return null;
        }
        for (MenuType menuType : values()) {
            if (type.equals(menuType.getType())) {
                return menuType;
            }
        }
        return null;
    }
}
