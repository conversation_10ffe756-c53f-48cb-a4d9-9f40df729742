package com.avatar.hospital.chaperone.component.baccount;

import com.avatar.hospital.chaperone.database.baccount.dataobject.AccountRoleDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleDO;
import com.avatar.hospital.chaperone.database.baccount.dataobject.RoleMenuDO;
import com.avatar.hospital.chaperone.database.baccount.repository.AccountRoleRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleMenuRepositoryService;
import com.avatar.hospital.chaperone.database.baccount.repository.RoleRepositoryService;
import com.avatar.hospital.chaperone.properties.RoleProperties;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/27
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class RoleComponent {

    private final AccountRoleRepositoryService accountRoleRepositoryService;
    private final RoleMenuRepositoryService roleMenuRepositoryService;
    private final RoleRepositoryService roleRepositoryService;
    private final RoleProperties roleProperties;

    /**
     * 根据用户ID查询所有的角色ID
     *
     * @param accountId -
     * @return -
     */
    public Set<Long> findAccountRoleIds(Long accountId) {
        if (accountId == null) {
            return Collections.emptySet();
        }
        List<AccountRoleDO> accountRoleDOList = accountRoleRepositoryService.findByAccountIds(Sets.newHashSet(accountId));
        if (CollectionUtils.isEmpty(accountRoleDOList)) {
            log.warn("RoleComponent findAccountRoleIds accountRoleDOList empty accountId:{}", accountId);
            return Collections.emptySet();
        }
        return accountRoleDOList.stream().map(AccountRoleDO::getRoleId).collect(Collectors.toSet());
    }

    /**
     * 根据用户ID查询所有的角色
     *
     * @param accountId -
     * @return -
     */
    public List<RoleDO> findAccountRoleList(Long accountId) {
        if (accountId == null) {
            return Collections.emptyList();
        }
        List<AccountRoleDO> accountRoleDOList = accountRoleRepositoryService.findByAccountIds(Sets.newHashSet(accountId));
        if (CollectionUtils.isEmpty(accountRoleDOList)) {
            log.warn("RoleComponent findAccountRoleList accountRoleDOList empty accountId:{}", accountId);
            return Collections.emptyList();
        }
        return roleRepositoryService.findByIds(accountRoleDOList.stream().map(AccountRoleDO::getRoleId).collect(Collectors.toSet()));
    }

    /**
     * 根据用户ID查询所有的权限ID
     *
     * @param accountId -
     * @return -
     */
    public Set<Long> findAccountPermissionList(Long accountId) {
        if (accountId == null) {
            return Collections.emptySet();
        }
        List<RoleDO> roleList = findAccountRoleList(accountId);
        if (CollectionUtils.isEmpty(roleList)) {
            return Collections.emptySet();
        }
        List<RoleMenuDO> roleMenuList = roleMenuRepositoryService.findByRoleIds(roleList.stream().map(RoleDO::getId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(roleMenuList)) {
            return Collections.emptySet();
        }
        return roleMenuList.stream().map(RoleMenuDO::getMenuId).collect(Collectors.toSet());
    }

    /**
     * 判断用户有没有查询所有医院层级列表的角色
     *
     * @param accountId -
     * @return -
     */
    public boolean isSelectHospitalListRole(Long accountId) {
        if (accountId == null) {
            return false;
        }
        List<RoleDO> roleDOList = findAccountRoleList(accountId);
        if (CollectionUtils.isEmpty(roleDOList)) {
            return false;
        }
        List<RoleDO> hospitalListRoleList = roleDOList.stream()
                .filter(RoleDO::enable)
                .filter(roleDO -> Objects.equals(roleProperties.getHospitalListRoleKey(), roleDO.getRoleKey()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hospitalListRoleList)) {
            return false;
        }
        return hospitalListRoleList.size() >= 1;
    }

    /**
     * 判断用户有没有查询所有部门层级列表的角色
     *
     * @param accountId -
     * @return -
     */
    public boolean isSelectDepartmentListRole(Long accountId) {
        if (accountId == null) {
            return false;
        }
        List<RoleDO> roleDOList = findAccountRoleList(accountId);
        if (CollectionUtils.isEmpty(roleDOList)) {
            return false;
        }
        List<RoleDO> departmentListRoleList = roleDOList.stream()
                .filter(RoleDO::enable)
                .filter(roleDO -> Objects.equals(roleProperties.getDepartmentListRoleKey(), roleDO.getRoleKey()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departmentListRoleList)) {
            return false;
        }
        return departmentListRoleList.size() >= 1;
    }

    /**
     * 判断用户有没有查询组织机构树的角色
     *
     * @param accountId -
     * @return -
     */
    public boolean isSelectOrganizationTreeRole(Long accountId) {
        if (accountId == null) {
            return false;
        }
        List<RoleDO> roleDOList = findAccountRoleList(accountId);
        if (CollectionUtils.isEmpty(roleDOList)) {
            return false;
        }
        List<RoleDO> departmentListRoleList = roleDOList.stream()
                .filter(RoleDO::enable)
                .filter(roleDO -> Objects.equals(roleProperties.getOrganizationTreeRoleKey(), roleDO.getRoleKey()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departmentListRoleList)) {
            return false;
        }
        return departmentListRoleList.size() >= 1;
    }


}
