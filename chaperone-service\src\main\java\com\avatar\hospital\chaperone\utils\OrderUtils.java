package com.avatar.hospital.chaperone.utils;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderNursingDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderStatus;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-17 11:26
 **/
@Slf4j
public class OrderUtils {

    public final static Integer DISCOUNT_RATE = 100000;

    /**
     * 计算折扣
     * @param price 500 => 5 元
     * @param discount 778 => 77.8 折扣
     * @return
     */
    public static final Integer calculationDiscount(Integer price,Integer discount) {
        if (Objects.isNull(discount) || discount < 0 || Objects.equals(discount,DISCOUNT_RATE)) {
            return price;
        }
        Integer newPrice = (price * discount) / DISCOUNT_RATE;
        return newPrice;
    }

    /**
     * 计算分层
     *     护工金额 = 总金额 * 护工分层比例(80) / 100
     *     医院金额 = 总金额 * 医院分层比例(15) / 100
     *     物业金额 = 总金额 - 护工金额 - 医院金额;
     * @param param
     * @return
     */
    public static final CalculationRateResult calculationRate(CalculationRateParam param) {
        Integer totalPrice = param.getTotalPrice();
        Long priceNursing = (totalPrice * 1L * param.getRateNursing()) / DISCOUNT_RATE ;
        Long priceHospital = (totalPrice * 1L * param.getRateHospital() )/ DISCOUNT_RATE ;
        Long priceCertifiedProperty = totalPrice - priceHospital - priceNursing;

        CalculationRateResult calculationRateResult = new CalculationRateResult();
        calculationRateResult.setTotalPrice(param.getTotalPrice());
        calculationRateResult.setRateCertifiedProperty(param.getRateCertifiedProperty());
        calculationRateResult.setPriceCertifiedProperty(priceCertifiedProperty.intValue());
        calculationRateResult.setRateHospital(param.getRateHospital());
        calculationRateResult.setPriceHospital(priceHospital.intValue());
        calculationRateResult.setRateNursing(param.getRateNursing());
        calculationRateResult.setPriceNursing(priceNursing.intValue());
        return calculationRateResult;
    }

    /**
     * 平均分金额 和 费率
     * @param nursingDOList
     * @return
     */
    public static final List<CalculationNursingRateResult> calculationNursingRate(Integer priceNursing,Integer rateNursing,List<OrderNursingDO> nursingDOList) {
        List<CalculationNursingRateResult> list = new ArrayList<>(nursingDOList.size());
        int size = nursingDOList.size();
        int totalPrice = 0;
        int totalRate = 0;
        for (int i = 0; i < size; i ++) {
            OrderNursingDO nursing = nursingDOList.get(i);
            // 最后一次循环
            Boolean last = i == size - 1;

            int price = priceNursing / size;
            int rate = rateNursing / size;
            if (last) {
                price = priceNursing - totalPrice;
                rate = rateNursing - totalRate;
            }
            totalPrice += price;
            totalRate  += rate;
            // 封装数据
            CalculationNursingRateResult dat = new CalculationNursingRateResult();
            dat.setNursingId(nursing.getNursingId());
            dat.setNursingName(nursing.getNursingName());
            dat.setRateNursing(rate);
            dat.setPriceNursing(price);
            list.add(dat);
        }
        return list;
    }

    public static Long getNewVsersion() {
        return DateUtils.dateLong();
    }

    /**
     * 生成消耗的日期
     *         画一个区间,2端为订单的开始时间和结束时间,中间有各个商品对应的日期+小时节点
     *             当前时间作为光标,
     *             输出当前消耗的时间日期
     * @param orderStartDate 订单开始时间 2023102013
     * @param orderEndDate 订单结算日期 2023102013
     * @param curDateTime 当前时间 2023102013
     * @param itemHour 商品开始计费时间
     * @return 20231020
     *      -1 不在时间范围内
     */
    public static Integer getConsumerDate(Integer orderStartDate,
                                          Integer orderEndDate,
                                          Integer curDateTime, Integer itemHour) {
        Integer result = -1;
        if (curDateTime <= orderStartDate || orderEndDate <= curDateTime) {
            return result;
        }
        // 获取小时 获取日期
        int curHour = curDateTime % 100;
        LocalDate localDate = DateUtils.parseForInt(curDateTime / 100);
        if (curHour < itemHour) {
            // 当天
        }else {
            // 明天
            localDate  = localDate.plusDays(DateUtils.ADD_ONE_DAY);
        }
        return DateUtils.dateInt(localDate);
    }

    /**
     * 获取订单超时时间
     * @return
     */
    public static String getPayOutTime() {
        String timeExpire = LocalDateTime.now()
                .plusMinutes(15)
                .format( DateTimeFormatter.ofPattern(DateUtils.DAY_LONG_20_PATTERN));
        String result = timeExpire;
        return result;
    }

    public static String getZoneOff() {
        ZoneOffset offset = ZoneOffset.of("+08:00");
        return offset.getId();
    }

    /**
     * 已经过期 60 秒的一个误差
     * @param payOutTime
     * @return
     */
    public static boolean outTime(String payOutTime) {
        LocalDateTime outTime = LocalDateTime.parse(payOutTime, DateTimeFormatter.ofPattern(DateUtils.DAY_LONG_20_PATTERN));
        outTime = outTime.plusMinutes(-1);
        boolean flag = outTime.isBefore(LocalDateTime.now());
        return flag;
    }

    public static Boolean isSuccess(WxPayOrderQueryResult queryInfo) {
        return Objects.equals(OrderConst.SUCCESS,queryInfo.getReturnCode())
                && Objects.equals(OrderConst.SUCCESS,queryInfo.getResultCode())
                && Objects.equals(OrderConst.SUCCESS,queryInfo.getTradeState());
    }

    public static boolean isWork(Integer orderStatus) {
        return Objects.equals(orderStatus, OrderStatus.WORK.getStatus())
                || Objects.equals(orderStatus, OrderStatus.APPLY_SETTLE.getStatus());
    }

    /**
     *
     * @param orderStatus
     * @return
     */
    public static boolean isCompleteInfo(Integer orderStatus) {
        return Objects.equals(orderStatus, OrderStatus.COMMIT_COMPLETE_INFO.getStatus())
                || Objects.equals(orderStatus, OrderStatus.WAIT_CONFIRM.getStatus()) ;
    }

    @Data
    public static class CalculationRateParam {
        /**
         * 总金额：分
         */
        private Integer totalPrice;
        /**
         * 物业分成比例
         */
        private Integer rateCertifiedProperty;

        /**
         * 医院分成比例
         */
        private Integer rateHospital;

        /**
         * 护工分成比例
         */
        private Integer rateNursing;

        public static final CalculationRateParam build(Integer totalPrice,Integer rateCertifiedProperty,Integer rateHospital,Integer rateNursing) {
            CalculationRateParam obj = new CalculationRateParam();
            obj.setTotalPrice(totalPrice);
            obj.setRateCertifiedProperty(rateCertifiedProperty);
            obj.setRateHospital(rateHospital);
            obj.setRateNursing(rateNursing);
            return obj;
        }

    }

    @Data
    public static class CalculationRateResult {
        /**
         * 总金额：分
         */
        private Integer totalPrice;

        /**
         * 物业分成比例
         */
        private Integer rateCertifiedProperty;

        /**
         * 物业金额
         */
        private Integer priceCertifiedProperty;

        /**
         * 医院分成比例
         */
        private Integer rateHospital;

        /**
         * 医院金额
         */
        private Integer priceHospital;

        /**
         * 护工分成比例
         */
        private Integer rateNursing;

        /**
         * 护工金额
         */
        private Integer priceNursing;
    }

    @Data
    public static class CalculationNursingRateResult {
        /**
         * 护工ID
         */
        private Long nursingId;

        /**
         * 护工名称
         */
        private String nursingName;

        /**
         * 护工分成比例
         */
        private Integer rateNursing;

        /**
         * 护工金额
         */
        private Integer priceNursing;
    }

    public static void main(String[] args) {
        List<OrderNursingDO> list = new ArrayList<>();
        OrderNursingDO nursingDO = new OrderNursingDO();
        nursingDO.setNursingId(1L);
        nursingDO.setNursingName("a");
        list.add(nursingDO);

        nursingDO = new OrderNursingDO();
        nursingDO.setNursingId(2L);
        nursingDO.setNursingName("b");
        list.add(nursingDO);

        nursingDO = new OrderNursingDO();
        nursingDO.setNursingId(3L);
        nursingDO.setNursingName("c");
        list.add(nursingDO);

        List<CalculationNursingRateResult> list1 = calculationNursingRate(776, 22, list);
        System.out.printf("");
    }
}
