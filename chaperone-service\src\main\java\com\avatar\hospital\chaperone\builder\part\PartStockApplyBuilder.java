package com.avatar.hospital.chaperone.builder.part;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.avatar.hospital.chaperone.common.Attachment;
import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyDO;
import com.avatar.hospital.chaperone.database.part.dataobject.PartStockApplyRefPartBatchDO;
import com.avatar.hospital.chaperone.database.part.enums.PartStockApplyStatusType;
import com.avatar.hospital.chaperone.database.part.repository.PartStockApplyRefPartBatchRepositoryService;
import com.avatar.hospital.chaperone.request.part.PartStockApplyRequest;
import com.avatar.hospital.chaperone.response.part.PartStockApplyVO;
import com.avatar.hospital.chaperone.utils.CodeUtil;
import com.avatar.hospital.chaperone.utils.SpringUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/27 18:09
 */
public class PartStockApplyBuilder {


    public static PartStockApplyDO build(PartStockApplyRequest request) {

        PartStockApplyDO partStockApplyDO = new PartStockApplyDO();
        BeanUtils.copyProperties(request, partStockApplyDO);
        partStockApplyDO.setCode(CodeUtil.generateCode(CodeBizType.BJRK));
        partStockApplyDO.setStatus(PartStockApplyStatusType.WAITING.getCode());
        partStockApplyDO.setAttachments(JSONObject.toJSONString(request.getAttachments()));
        partStockApplyDO.setCreateBy(request.getOperator());
        partStockApplyDO.setUpdateBy(request.getOperator());
        return partStockApplyDO;

    }

    public static List<PartStockApplyRefPartBatchDO> buildStockApply(PartStockApplyRequest request) {

        List<PartStockApplyRefPartBatchDO> refPartBatchDOS = request.getPartBatchs().stream().map(o -> {
            PartStockApplyRefPartBatchDO refPartBatchDO = new PartStockApplyRefPartBatchDO();
            refPartBatchDO.setSparePartBatchId(o.getPartBatchId());
            refPartBatchDO.setQuantity(o.getQuantity());
            refPartBatchDO.setStockApplyId(request.getId());
            refPartBatchDO.setCreateBy(request.getOperator());
            refPartBatchDO.setUpdateBy(request.getOperator());
            return refPartBatchDO;
        }).collect(Collectors.toList());

        return refPartBatchDOS;

    }

    public static PartStockApplyVO build(PartStockApplyDO partStockApplyDO) {
        PartStockApplyVO partStockApplyVO = new PartStockApplyVO();
        BeanUtils.copyProperties(partStockApplyDO, partStockApplyVO);
        partStockApplyVO.setAttachments(JSONArray.parseArray(partStockApplyDO.getAttachments(), Attachment.class));
        setPartBatch(partStockApplyVO);
        return partStockApplyVO;
    }

    private static void setPartBatch(PartStockApplyVO partStockApplyVO) {

        List<PartStockApplyRefPartBatchDO> partBatchIds = SpringUtils.getBean(PartStockApplyRefPartBatchRepositoryService.class).getPartBatchBy(partStockApplyVO.getId());

        List<PartStockApplyRequest.PartBatch> partBatchs = Lists.newArrayList();
        for (int i = 0; i < partBatchIds.size(); i++) {
            PartStockApplyRefPartBatchDO refPartBatchDO = partBatchIds.get(i);
            PartStockApplyRequest.PartBatch partBatch = new PartStockApplyRequest.PartBatch();
            partBatch.setPartBatchId(refPartBatchDO.getSparePartBatchId());
            partBatch.setQuantity(refPartBatchDO.getQuantity());
            partBatchs.add(partBatch);
        }
        partStockApplyVO.setPartBatchs(partBatchs);
    }
}
