package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderConsumerlogDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单-消耗明细表(天，套餐); 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderConsumerlogRepositoryService extends IService<OrderConsumerlogDO> {

    /**
     * 重新计算后保存
     * @param oldList
     * @param newlist
     * @return
     */
    boolean savesByResetCalculation(List<OrderConsumerlogDO> oldList, List<OrderConsumerlogDO> newlist);

    /**
     *  查询订单下所有有效记录
     * @param orderId 订单ID
     * @return
     */
    List<OrderConsumerlogDO> findAll(Long orderId);

    /**
     *  查询订单下某个时间范围所有有效记录
     * @param orderId 订单ID
     * @param startDate 开始日期(包括),格式yyyyMMdd
     * @param endDate 结束日期(包括),格式yyyyMMdd
     * @return
     */
    List<OrderConsumerlogDO> findAll(Long orderId,Integer startDate,Integer endDate);

    /**
     * 统计指定时间消费总额
     * @param orderId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer sumPrice(Long orderId,Integer startDate,Integer endDate);

    /**
     * 统计金额
     * @param star
     * @param end
     * @return
     */
    Long statistics(LocalDateTime star, LocalDateTime end);
}
