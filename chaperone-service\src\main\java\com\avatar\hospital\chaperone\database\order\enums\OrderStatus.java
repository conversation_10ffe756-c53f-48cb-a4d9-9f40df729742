package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.avatar.hospital.chaperone.response.baccount.OrganizationControlResponse;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * Description:
 *  交易类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderStatus implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    CANCEL(1, "取消"),
    CLOSE(2, "已关闭(审核完毕后取消)"),
    FINISH(3, "已完结(手动触发,到期触发)"),
    // B端审核
    COMMIT_HOSPITAL(10, "待院方审核"),
    COMMIT_CERTIFIED_PROPERTY(11, "待物业确认"),
    COMMIT_COMPLETE_INFO(12, "待完善信息(B端确认)"),
    // 用户
    WAIT_CONFIRM(21, "待用户确认下单"),
    WORK(30, "陪护中"),
    // 结算申请(C端提交，B确认后才停止收费)
    APPLY_SETTLE(31, "提交结算申请"),
    ;

    private final Integer status;

    private final String describe;


    OrderStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderStatus of(Integer status) {
        if (status == null) {
            return null;
        }
        for (OrderStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return null;
    }

    public static  final Map<Integer, String> toMap() {
        OrderStatus[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (OrderStatus value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

    /**
     * 是否一致
     * @param orderStatus
     * @return
     */
    public Boolean withEq(Integer orderStatus) {
        OrderStatus status = of(orderStatus);
        return Objects.equals(status,this);
    }

    @Override
    public String convertDesc(Integer val) {
        OrderStatus e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    /**
     * 获取初始审批状态
     * @param controlDO
     * @return
     */
    public final static OrderStatus initStatusByOrgControl(OrganizationControlResponse controlDO) {
        OrderStatus orderStatus = OrderStatus.COMMIT_HOSPITAL;
        if(Objects.nonNull(controlDO) && Objects.equals(1,controlDO.getOrderApproveModel())) {
            // 模式1 跳过医院审批
            orderStatus = OrderStatus.COMMIT_CERTIFIED_PROPERTY;
        }
        return orderStatus;
    }
}
