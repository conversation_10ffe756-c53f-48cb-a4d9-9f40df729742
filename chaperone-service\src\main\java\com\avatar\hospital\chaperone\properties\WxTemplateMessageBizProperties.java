package com.avatar.hospital.chaperone.properties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2024-01-02 16:45
 **/
@Data
@Slf4j
@ConfigurationProperties(prefix = WxTemplateMessageBizProperties.PREFIX)
@Component
public class WxTemplateMessageBizProperties implements Serializable {
    public static final String PREFIX = "wx-template-message-biz";

    /**
     * 订单收款提醒
     */
    private String orderPayTemplateId;
}
