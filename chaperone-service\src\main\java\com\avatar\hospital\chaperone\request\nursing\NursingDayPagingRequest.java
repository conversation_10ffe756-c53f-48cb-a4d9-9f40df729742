package com.avatar.hospital.chaperone.request.nursing;

import com.avatar.hospital.chaperone.request.PageRequest;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author:sp0420
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class NursingDayPagingRequest extends PageRequest {

    /**
     * 陪护单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 陪护单状态
     */
    private Integer status;

    /**
     * 起始时间
     */
    private Integer startTime;

    /**
     * 结束时间
     */
    private Integer endTime;

    /**
     * 排序规则，1-C端倒叙，2-B端正序
     */
    private Integer sortType;

    /**
     * 护工id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long nursingId;

    /**
     * 护工名称
     */
    private String nursingName;
}
