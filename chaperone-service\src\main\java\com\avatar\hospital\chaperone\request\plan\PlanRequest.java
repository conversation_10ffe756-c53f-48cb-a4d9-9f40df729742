package com.avatar.hospital.chaperone.request.plan;

import com.avatar.hospital.chaperone.database.plan.enums.PlanType;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import com.avatar.hospital.chaperone.template.serialize.ToStringForLongListSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 巡检计划
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Getter
@Setter

public class PlanRequest implements OperatorReq, Serializable {


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 巡检周期类型（1-天，2-月）
     *
     * @see com.avatar.hospital.chaperone.database.plan.enums.CircleType
     */
    private Integer circleType;

    /**
     * 巡检周期
     */
    private Integer circle;

    /**
     * 状态（1-生效，2-作废）
     *
     * @see com.avatar.hospital.chaperone.database.plan.enums.PlanStatusType
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属院区ID
     */
    private Long orgId;

    /**
     * 关联设备ids
     */
    private List<Long> deviceIds;
    /**
     * 关联部门id
     */
    private List<Long> orgIds;

    /**
     * 关联人员id
     */
    private List<Long> executorIds;

    /**
     * 操作人
     */
    public Operator operatorUser;

    /**
     * 计划类型
     */
    public PlanType planType;
}
