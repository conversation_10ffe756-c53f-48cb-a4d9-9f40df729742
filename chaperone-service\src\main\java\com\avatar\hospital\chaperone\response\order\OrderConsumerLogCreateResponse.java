package com.avatar.hospital.chaperone.response.order;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-12 09:28
 **/
@Data
public class OrderConsumerLogCreateResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 更新结果
     */
    private Boolean success;

    public static final OrderConsumerLogCreateResponse build(Boolean success) {
        OrderConsumerLogCreateResponse obj = new OrderConsumerLogCreateResponse();
        obj.setSuccess(success);
        return obj;
    }

}
