package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import lombok.Getter;

import java.util.Objects;

/**
 * Description:
 *  折扣影响类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderDiscountType implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    ASCEND(1, "追溯"),
    NO_ASCEND(0, "不追溯"),
    ;

    private final Integer status;

    private final String describe;


    OrderDiscountType(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderDiscountType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderDiscountType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        OrderDiscountType e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    /**
     * 是否追溯
     * @return
     */
    public Boolean isAscend() {
        return ASCEND.equals(this);
    }
}
