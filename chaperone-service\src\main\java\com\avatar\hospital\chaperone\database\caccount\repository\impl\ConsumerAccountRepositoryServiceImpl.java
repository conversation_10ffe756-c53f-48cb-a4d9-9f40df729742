package com.avatar.hospital.chaperone.database.caccount.repository.impl;

import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountDO;
import com.avatar.hospital.chaperone.database.caccount.dataobject.AccountOpenIdDO;
import com.avatar.hospital.chaperone.database.caccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.caccount.mapper.ConsumerAccountMapper;
import com.avatar.hospital.chaperone.database.caccount.repository.AccountOpenIdRepositoryService;
import com.avatar.hospital.chaperone.database.caccount.repository.ConsumerAccountRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.caccount.dto.ConsumerLoginTripartiteDTO;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * C端用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ConsumerAccountRepositoryServiceImpl extends ServiceImpl<ConsumerAccountMapper, AccountDO> implements ConsumerAccountRepositoryService {

    private final AccountOpenIdRepositoryService accountOpenIdRepositoryService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long add(AccountDO accountDO, ConsumerLoginTripartiteDTO consumerLoginTripartite) {
        if (accountDO == null || consumerLoginTripartite == null) {
            return null;
        }
        long accountId = IdUtils.getId();
        accountDO.setId(accountId);
        accountDO.setCreateBy(accountId);
        accountDO.setUpdateBy(accountId);
        if (!save(accountDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        if (StringUtils.isNotBlank(consumerLoginTripartite.getOpenId())) {
            AccountOpenIdDO accountOpenIdDO = new AccountOpenIdDO();
            accountOpenIdDO.setAccountId(accountDO.getId());
            accountOpenIdDO.setOpenId(consumerLoginTripartite.getOpenId());
            accountOpenIdDO.setUnionId(consumerLoginTripartite.getUnionId());
            accountOpenIdDO.setType(consumerLoginTripartite.getType());
            accountOpenIdDO.setAppId(consumerLoginTripartite.getAppId());
            if (!Boolean.TRUE.equals(accountOpenIdRepositoryService.add(accountOpenIdDO))) {
                throw BusinessException.of(ErrorCode.INSERT_ERROR);
            }
        }
        return accountDO.getId();
    }

    @Override
    public Boolean incrementUpdate(AccountDO accountDO) {
        if (accountDO == null) {
            return false;
        }
        LambdaUpdateWrapper<AccountDO> updateWrapper = updateWrapper();
        updateWrapper.set(accountDO.getNickName() != null, AccountDO::getNickName, accountDO.getNickName());
        updateWrapper.set(accountDO.getName() != null, AccountDO::getName, accountDO.getName());
        updateWrapper.set(accountDO.getPhoneNumber() != null, AccountDO::getPhoneNumber, accountDO.getPhoneNumber());
        updateWrapper.set(accountDO.getSex() != null, AccountDO::getSex, accountDO.getSex());
        updateWrapper.set(accountDO.getBirthday() != null, AccountDO::getBirthday, accountDO.getBirthday());
        updateWrapper.set(accountDO.getAdmissionTime() != null, AccountDO::getAdmissionTime, accountDO.getAdmissionTime());
        updateWrapper.set(accountDO.getAvatarUrl() != null, AccountDO::getAvatarUrl, accountDO.getAvatarUrl());
        updateWrapper.set(accountDO.getStatus() != null, AccountDO::getStatus, accountDO.getStatus());
        updateWrapper.set(accountDO.getUpdateBy() != null, AccountDO::getUpdateBy, accountDO.getUpdateBy());

        updateWrapper.eq(AccountDO::getId, accountDO.getId());
        if (!update(updateWrapper)) {
            throw BusinessException.of(ErrorCode.UPDATE_ERROR);
        }
        return true;
    }

    @Override
    public AccountDO findById(Long accountId) {
        if (accountId == null) {
            return null;
        }
        LambdaQueryWrapper<AccountDO> queryWrapper = queryWrapper();
        queryWrapper.eq(AccountDO::getId, accountId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public AccountDO findByPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return null;
        }
        LambdaQueryWrapper<AccountDO> queryWrapper = queryWrapper();
        queryWrapper.eq(AccountDO::getPhoneNumber, phoneNumber);
        return baseMapper.selectOne(queryWrapper);
    }

    private LambdaQueryWrapper<AccountDO> queryWrapper() {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<AccountDO> updateWrapper() {
        LambdaUpdateWrapper<AccountDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AccountDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }

}
