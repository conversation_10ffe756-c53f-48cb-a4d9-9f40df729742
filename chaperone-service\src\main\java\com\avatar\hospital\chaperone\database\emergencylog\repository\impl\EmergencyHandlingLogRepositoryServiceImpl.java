package com.avatar.hospital.chaperone.database.emergencylog.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.emergencylog.dataobject.EmergencyHandlingLogDO;
import com.avatar.hospital.chaperone.database.emergencylog.mapper.EmergencyHandlingLogMapper;
import com.avatar.hospital.chaperone.database.emergencylog.repository.EmergencyHandlingLogRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.emergency.EmergencyLogPagingRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.DefaultUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 应急处置日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class EmergencyHandlingLogRepositoryServiceImpl extends ServiceImpl<EmergencyHandlingLogMapper, EmergencyHandlingLogDO> implements EmergencyHandlingLogRepositoryService {

    @Override
    public Long add(EmergencyHandlingLogDO emergencyHandlingLogDO) {
        if (emergencyHandlingLogDO == null) {
            return null;
        }
        emergencyHandlingLogDO.setId(IdUtils.getId());
        if (!save(emergencyHandlingLogDO)) {
            throw BusinessException.of(ErrorCode.INSERT_ERROR);
        }
        return emergencyHandlingLogDO.getId();
    }

    @Override
    public Boolean incrementUpdate(EmergencyHandlingLogDO emergencyHandlingLogDO) {
        if (emergencyHandlingLogDO == null) {
            return false;
        }
        LambdaUpdateWrapper<EmergencyHandlingLogDO> updateWrapper = updateWrapper();
        updateWrapper.set(emergencyHandlingLogDO.getTitle() != null, EmergencyHandlingLogDO::getTitle, emergencyHandlingLogDO.getTitle());
        updateWrapper.set(emergencyHandlingLogDO.getEventTime() != null, EmergencyHandlingLogDO::getEventTime, emergencyHandlingLogDO.getEventTime());
        updateWrapper.set(emergencyHandlingLogDO.getOrgId() != null, EmergencyHandlingLogDO::getOrgId, emergencyHandlingLogDO.getOrgId());
        updateWrapper.set(emergencyHandlingLogDO.getRemark() != null, EmergencyHandlingLogDO::getRemark, emergencyHandlingLogDO.getRemark());
        updateWrapper.set(emergencyHandlingLogDO.getUpdateBy() != null, EmergencyHandlingLogDO::getUpdateBy, emergencyHandlingLogDO.getUpdateBy());
        updateWrapper.set(emergencyHandlingLogDO.getUpdatedAt() != null, EmergencyHandlingLogDO::getUpdatedAt, emergencyHandlingLogDO.getUpdatedAt());
        updateWrapper.eq(EmergencyHandlingLogDO::getId, emergencyHandlingLogDO.getId());
        return update(updateWrapper);
    }

    @Override
    public PageResponse<EmergencyHandlingLogDO> paging(EmergencyLogPagingRequest request) {
        int index = DefaultUtils.ifNullDefault(request.getPageIndex(), 1);
        int size = DefaultUtils.ifNullDefault(request.getPageSize(), 10);
        LambdaQueryWrapper<EmergencyHandlingLogDO> queryWrapper = queryWrapper();
        if (request.getTitle() != null) {
            queryWrapper.eq(request.getTitle() != null, EmergencyHandlingLogDO::getTitle, request.getTitle());
        }
        Page<EmergencyHandlingLogDO> page = baseMapper.selectPage(new Page<>(index, size), queryWrapper);
        return PageResponse.build(page.getTotal(), page.getCurrent(), page.getSize(), page.getRecords());
    }

    @Override
    public EmergencyHandlingLogDO findById(Long id) {
        if (id == null || id < 0) {
            return null;
        }
        LambdaQueryWrapper<EmergencyHandlingLogDO> queryWrapper = queryWrapper();
        queryWrapper.eq(EmergencyHandlingLogDO::getId, id);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public Integer countForYear(Integer year) {
        LocalDateTime[] startEndOfYear =DateUtils.getStartEndOfYear(year);
        LambdaQueryWrapper<EmergencyHandlingLogDO> queryWrapper = queryWrapper();
        queryWrapper.ge(EmergencyHandlingLogDO::getCreatedAt,startEndOfYear[0]);
        queryWrapper.le(EmergencyHandlingLogDO::getCreatedAt,startEndOfYear[1]);
        Long count = count(queryWrapper);
        return count.intValue();
    }

    @Override
    public Integer countForMonth(Integer month) {
        LocalDateTime[] startEndOfMonth =DateUtils.getStartEndOfMonth(month);
        LambdaQueryWrapper<EmergencyHandlingLogDO> queryWrapper = queryWrapper();
        queryWrapper.ge(EmergencyHandlingLogDO::getCreatedAt,startEndOfMonth[0]);
        queryWrapper.le(EmergencyHandlingLogDO::getCreatedAt,startEndOfMonth[1]);
        Long count = count(queryWrapper);
        return count.intValue();
    }

    private LambdaQueryWrapper<EmergencyHandlingLogDO> queryWrapper() {
        LambdaQueryWrapper<EmergencyHandlingLogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmergencyHandlingLogDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<EmergencyHandlingLogDO> updateWrapper() {
        LambdaUpdateWrapper<EmergencyHandlingLogDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(EmergencyHandlingLogDO::getDeleted, DeletedEnum.NO.getStatus());
        return updateWrapper;
    }
}
