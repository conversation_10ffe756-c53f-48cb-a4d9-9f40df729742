package com.avatar.hospital.chaperone.request.baccount;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class OrganizationUpdateRequest implements Serializable {

    /**
     * 组织机构ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 联络人名称
     */
    private String liaisonName;

    /**
     * 联络人电话
     */
    private String liaisonPhoneNumber;

    /**
     * 银行账号
     */
    private String bankAccountNumber;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationStatus
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 更新者ID
     */
    private Long updateBy;
}
