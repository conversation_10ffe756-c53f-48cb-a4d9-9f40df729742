package com.avatar.hospital.chaperone.job.order;

import com.avatar.hospital.chaperone.builder.order.OrderBuilder;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderBillDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.avatar.hospital.chaperone.database.order.repository.OrderBillRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderItemRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderRepositoryService;
import com.avatar.hospital.chaperone.request.order.OrderPriceCalculationRequest;
import com.avatar.hospital.chaperone.response.order.OrderPriceCalculationResponse;
import com.avatar.hospital.chaperone.service.order.OrderService;
import com.avatar.hospital.chaperone.service.order.dto.OrderPriceCalculationParam;
import com.avatar.hospital.chaperone.service.order.dto.OrderPriceCalculationResult;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 价格计算
 *
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-02 15:54
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPriceCalculationExec {
    private final OrderRepositoryService orderRepositoryService;
    private final OrderItemRepositoryService orderItemRepositoryService;
    private final OrderBillRepositoryService orderBillRepositoryService;
    @Autowired
    private OrderService orderService;

    /**
     * 价格部分
     *    折扣变动,价格会变化
     *    套餐变动,价格会变化
     *    时间变动,价格会变化
     * @param param
     * @return
     */
    public OrderPriceCalculationResult calculation(OrderPriceCalculationParam param) {
        Long delVersion = DelUtils.delVersion();
        OrderDO oldOrder = orderRepositoryService.findById(param.getOrderId());
        OrderBillDO oldOrderBillDO = orderBillRepositoryService.getTotalByOrderId(param.getOrderId());
        List<OrderItemDO> oldOrderItemList = orderItemRepositoryService.getByOrderId(param.getOrderId());
        oldOrderItemList.forEach(item -> item.setDeleted(delVersion));
        Map<Long, OrderItemDO> oldItemMap = oldOrderItemList.stream()
                .collect(Collectors.toMap(OrderItemDO::getItemId, Function.identity()));

        Integer startTime = Objects.isNull(param.getStatTime()) ? oldOrder.getRealStartTime() : param.getStatTime();
        Integer endTime = Objects.isNull(param.getEndTime()) ? oldOrder.getRealEndTime() : param.getEndTime();
        Integer discount = Objects.isNull(param.getDiscount()) ? oldOrder.getDiscount() : param.getDiscount();;
        List<Long> itemIdList = CollectionUtils.isEmpty(param.getItemIdList()) ?
                CollUtils.toListLongDistinct(oldOrderItemList,OrderItemDO::getItemId) : param.getItemIdList();


        OrderPriceCalculationRequest calculationRequest = new OrderPriceCalculationRequest();
        calculationRequest.setStartTime(startTime);
        calculationRequest.setEndTime(endTime);
        calculationRequest.setItemList(itemIdList);
        calculationRequest.setDiscount(discount);
        // 价格计算接口
        OrderPriceCalculationResponse calculationResult = orderService.priceCalculation(calculationRequest);

        // 生成新的记录

        OrderBillDO newTotalBillDO = new OrderBillDO();
        newTotalBillDO.setId(oldOrderBillDO.getId());
        newTotalBillDO.setPriceReceivable(calculationResult.getTotalDiscountPrice());
        newTotalBillDO.setUpdateBy(param.getOperator());

        OrderDO newOrderDO = new OrderDO();
        if (Objects.nonNull(oldOrder)) {
            newOrderDO.setId(oldOrder.getId());
        }
        newOrderDO.setDiscount(discount);
        newOrderDO.setStartTime(startTime);
        newOrderDO.setRealStartTime(startTime);
        newOrderDO.setEndTime(endTime);
        newOrderDO.setRealEndTime(endTime);
        newOrderDO.setUpdateBy(param.getOperator());

        Set<Long> existItemId = new HashSet<>();
        List<OrderItemDO> newItemList = calculationResult.getItemPriceList().stream()
                .map(item -> {
                    existItemId.add(item.getItemId());
                    OrderItemDO orderItemDO = oldItemMap.get(item.getItemId());
                    if (Objects.nonNull(orderItemDO)) {
                        orderItemDO.setOrderId(param.getOrderId());
                        orderItemDO.setItemName(item.getName());
                        orderItemDO.setNumber(item.getNumber());
                        orderItemDO.setPrice(item.getPrice());
                        orderItemDO.setTotalPrice(item.getTotalPrice());
                        orderItemDO.setUpdateBy(param.getOperator());
                        orderItemDO.setDeleted(DelUtils.NO_DELETED);
                        return orderItemDO;
                    } else {
                        orderItemDO = OrderBuilder.buildOrderItemDO(item, param.getOperator());
                        orderItemDO.setOrderId(param.getOrderId());
                        return orderItemDO;
                    }
                }).collect(Collectors.toList());
        oldOrderItemList.stream()
                .filter(item -> !existItemId.contains(item.getItemId()))
                .forEach(item -> newItemList.add(item));

        OrderPriceCalculationResult result = new OrderPriceCalculationResult();
        result.setOrderDO(newOrderDO);
        result.setTotalBill(newTotalBillDO);
        result.setOrderItemDOList(newItemList);
        return result;
    }


}
