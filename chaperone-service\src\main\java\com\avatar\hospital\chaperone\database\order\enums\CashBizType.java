package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * Description:
 *  账单类型
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum CashBizType implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    PERSON_EXTRACT(0, "人工提取"),
    BILL(1, "账单支付"),
    PERSON_DEPOSIT(2, "人工存入"),
    ALIPAY_DEPOSIT(3, "支付宝存入"),
    ALIPAY_EXTRACT(4, "支付宝提取"),
    ;

    private final Integer status;

    private final String describe;


    CashBizType(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static CashBizType of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (CashBizType itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        CashBizType e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }

    public static  final Map<Integer, String> toMap() {
        CashBizType[] values = values();
        Map<Integer,String> map = Maps.newHashMapWithExpectedSize(values.length);
        for (CashBizType value : values) {
            if (Objects.equals(NONE,value)) {
                continue;
            }
            map.put(value.getStatus(),value.getDescribe());
        }
        return map;
    }

}
