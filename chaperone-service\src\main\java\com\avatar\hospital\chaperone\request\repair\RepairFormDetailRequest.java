package com.avatar.hospital.chaperone.request.repair;

import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:50
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormDetailRequest implements OperatorReq, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 保修表单ID
     */
    @NotNull
    private Long id;


    /**
     * 操作用户
     */
    private Operator operatorUser;
}
