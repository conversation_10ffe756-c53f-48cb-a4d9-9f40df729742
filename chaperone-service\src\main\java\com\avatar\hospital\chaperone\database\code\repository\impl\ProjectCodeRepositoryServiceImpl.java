package com.avatar.hospital.chaperone.database.code.repository.impl;

import com.avatar.hospital.chaperone.database.code.dataobject.ProjectCodeDO;
import com.avatar.hospital.chaperone.database.code.enums.CodeBizType;
import com.avatar.hospital.chaperone.database.code.mapper.ProjectCodeMapper;
import com.avatar.hospital.chaperone.database.code.repository.ProjectCodeRepositoryService;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import static com.avatar.hospital.chaperone.enums.ErrorCode.PROJECT_CODE_NOT_CONFIG;
import static com.avatar.hospital.chaperone.utils.DateUtils.DAY_INT_PATTERN;

/**
 * <p>
 * 业务编号生成表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class ProjectCodeRepositoryServiceImpl extends ServiceImpl<ProjectCodeMapper, ProjectCodeDO> implements ProjectCodeRepositoryService {


    /**
     * 生成编号
     *
     * @param codeBizType
     * @return
     */
    @Override
    public String generateCode(CodeBizType codeBizType) {
        ProjectCodeDO projectCodeDO = query(codeBizType.getCode());
        AssertUtils.isNotNull(projectCodeDO, PROJECT_CODE_NOT_CONFIG);
        updateAutoId(projectCodeDO);

        return codeHandle(projectCodeDO);
    }

    private String codeHandle(ProjectCodeDO projectCodeDO) {
        String date = DateUtils.dateTimeStr(DAY_INT_PATTERN);
        StringBuffer stringBuilder = new StringBuffer(projectCodeDO.getPrefix());
        stringBuilder.append(date).append(projectCodeDO.getAutoId());
        return stringBuilder.toString();

    }

    private ProjectCodeDO query(Integer codeBizType) {
        LambdaQueryWrapper<ProjectCodeDO> query = new LambdaQueryWrapper();
        query.eq(ProjectCodeDO::getBizType, codeBizType);
        ProjectCodeDO projectCodeDO = this.getOne(query);
        return projectCodeDO;
    }

    private void updateAutoId(ProjectCodeDO projectCodeDO) {

        LambdaUpdateWrapper<ProjectCodeDO> updateWrapper = updateWrapper();
        updateWrapper.set(ProjectCodeDO::getAutoId, projectCodeDO.getAutoId() + 1);
        updateWrapper.eq(ProjectCodeDO::getBizType, projectCodeDO.getBizType()).eq(ProjectCodeDO::getAutoId, projectCodeDO.getAutoId());
        boolean rs = this.update(updateWrapper);
        if (!rs) {
            projectCodeDO = query(projectCodeDO.getBizType());
            updateAutoId(projectCodeDO);
        }
    }

    private LambdaUpdateWrapper<ProjectCodeDO> updateWrapper() {
        return new LambdaUpdateWrapper<>();
    }
}
