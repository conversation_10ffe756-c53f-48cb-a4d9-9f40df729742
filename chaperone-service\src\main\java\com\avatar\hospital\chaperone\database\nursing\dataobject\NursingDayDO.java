package com.avatar.hospital.chaperone.database.nursing.dataobject;

import com.avatar.hospital.chaperone.database.nursing.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <p>
 * 护工-考勤信息;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_nursing_day")
public class NursingDayDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 日期，格式yyyyMMdd
     */
      private Integer date;

      /**
     * 护工ID
     */
      private Long nursingId;

      /**
     * 护工姓名
     */
      private String nursingName;

      /**
     * 请假状态，0 未请假，1 空闲
       * @see com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus
     */
      private Integer status;

  /**
   * 不是请假
   * @return
   */
  public Boolean isNotStatusLeave() {
          return !Objects.equals(NursingDayStatus.LEAVE,NursingDayStatus.of(status));
      }

}
