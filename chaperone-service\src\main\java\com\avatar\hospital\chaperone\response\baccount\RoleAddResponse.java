package com.avatar.hospital.chaperone.response.baccount;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class RoleAddResponse implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long roleId;

}
