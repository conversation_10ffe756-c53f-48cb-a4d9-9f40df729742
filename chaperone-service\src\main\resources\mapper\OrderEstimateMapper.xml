<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.order.mapper.OrderEstimateMapper">

    <select id="statisticsNursing"
            resultType="com.avatar.hospital.chaperone.database.order.dataobject.model.StatisticsNursingModel">
        SELECT
            nur.`nursing_id` as id,
            nur.`nursing_name` as name,
            sum(es.`level`)  as star
        FROM `t_order_estimate` es
        LEFT JOIN `t_order_nursing` nur on es.`order_id`= nur.`order_id`
        WHERE es.`deleted`= 0
            and nur.`deleted`= 0
            and es.`created_at` &gt;= #{start}
            and es.`created_at` &lt; #{end}
            GROUP BY nur.`nursing_id`
            ORDER BY star desc
        LIMIT 10;
    </select>
</mapper>