package com.avatar.hospital.chaperone.web.controller.repair;

import com.alibaba.cola.dto.SingleResponse;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.repair.RepairFormFeedbackCreateRequest;
import com.avatar.hospital.chaperone.request.repair.RepairFormFeedbackPageRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormFeedbackIdResponse;
import com.avatar.hospital.chaperone.response.repair.RepairFormFeedbackResponse;
import com.avatar.hospital.chaperone.service.repair.RepairFormFeedbackService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  C端工程-报修单反馈(web)
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-30 14:29
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/c/repair/feedback")
public class RepairFormFeedbackCController {
    private final RepairFormFeedbackService repairFormFeedbackService;

    /**
     * 查询-分页
     */
    @PostMapping("paging")
    public SingleResponse<PageResponse<RepairFormFeedbackResponse>> paging(@Validated @RequestBody RepairFormFeedbackPageRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            AssertUtils.isNotNull(request.getRepairFormId(), ErrorCode.PARAMETER_ERROR);
            return repairFormFeedbackService.pagingForC(request);
        });
    }

    /**
     * 创建
     */
    @PostMapping("create")
    public SingleResponse<RepairFormFeedbackIdResponse> create(@RequestBody RepairFormFeedbackCreateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            return repairFormFeedbackService.create(request);
        });
    }

}
