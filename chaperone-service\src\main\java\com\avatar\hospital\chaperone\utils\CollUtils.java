package com.avatar.hospital.chaperone.utils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-18 11:49
 **/
public class CollUtils {

    /**
     * 集合带条件添加
     * @param flag
     * @param list
     * @param data
     * @param <T>
     */
    public static final <T> void add(boolean flag, Collection<T> list,T data) {
        if (flag) {
            list.add(data);
        }
    }

    /**
     * 获取所有ID
     * @param list
     * @param function
     * @return
     * @param <T>
     */
    public static <T,E> List<E> toListLongDistinct(List<T> list, Function<T,E> function) {
        if (Objects.isNull(list) || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<E> ids = list.stream()
                .map(function)
                .distinct()
                .collect(Collectors.toList());
        return ids;
    }

    public static boolean isEmpty(Collection list) {
       return Objects.isNull(list) || list.isEmpty();
    }
}
