package com.avatar.hospital.chaperone.template.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 账单支付时间序列化
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 16:36
 **/
public class BillPayTimeStringSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String str, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (Objects.isNull(str) || str.length() == 0) {
            jsonGenerator.writeString("");
        }else {
            String year = str.substring(0, 4);
            String month = str.substring(4, 6);
            String day = str.substring(6, 8);
            String hour = str.substring(8, 10);
            String minute = str.substring(10, 12);
            String sec = str.substring(12, 14);
            jsonGenerator.writeString(year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + sec);
        }
    }
}
