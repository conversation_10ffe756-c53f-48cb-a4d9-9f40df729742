package com.avatar.hospital.chaperone.response.emergency;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author:sp0420
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EmergencyLogAddResponse implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
}
