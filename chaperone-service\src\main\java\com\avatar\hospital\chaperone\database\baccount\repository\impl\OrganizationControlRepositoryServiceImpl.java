package com.avatar.hospital.chaperone.database.baccount.repository.impl;

import com.avatar.hospital.chaperone.database.baccount.dataobject.OrganizationControlDO;
import com.avatar.hospital.chaperone.database.baccount.enums.DeletedEnum;
import com.avatar.hospital.chaperone.database.baccount.mapper.OrganizationControlMapper;
import com.avatar.hospital.chaperone.database.baccount.repository.OrganizationControlRepositoryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <p>
 * B端组织机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrganizationControlRepositoryServiceImpl
        extends ServiceImpl<OrganizationControlMapper, OrganizationControlDO>
        implements OrganizationControlRepositoryService {


    @Override
    public OrganizationControlDO findByOrgId(Long orgId) {
        if (orgId == null || orgId < 0) {
            return null;
        }
        LambdaQueryWrapper<OrganizationControlDO> queryWrapper = queryWrapper();
        queryWrapper.eq(OrganizationControlDO::getOrgId, orgId);
        return baseMapper.selectOne(queryWrapper);
    }



    private LambdaQueryWrapper<OrganizationControlDO> queryWrapper() {
        LambdaQueryWrapper<OrganizationControlDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrganizationControlDO::getDeleted, DeletedEnum.NO.getStatus());
        return queryWrapper;
    }
}
