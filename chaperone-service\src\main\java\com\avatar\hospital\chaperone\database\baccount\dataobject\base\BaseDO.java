package com.avatar.hospital.chaperone.database.baccount.dataobject.base;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Data
public class BaseDO implements Serializable {

    private static final long serialVersionUID = 6791133411753063011L;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 删除时间 1:未删除;已删除:时间戳
     */
    @TableField(select = false)
    private Long deleted;
}
