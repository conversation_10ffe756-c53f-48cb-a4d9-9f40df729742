package com.avatar.hospital.chaperone.database.caccount.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * C端用户关联三方用户openId表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_account_open_id")
public class AccountOpenIdDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 三方用户标识openId
     */
    private String openId;

    /**
     * 三方用户标识unionId
     */
    private String unionId;

    /**
     * 三方账户类型
     *
     * @see com.avatar.hospital.chaperone.database.caccount.enums.AccountTripartiteType
     */
    private Integer type;

    /**
     * appId
     */
    private String appId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 删除时间 1:未删除;已删除:时间戳
     */
    @TableField(select = false)
    private Long deleted;
}
