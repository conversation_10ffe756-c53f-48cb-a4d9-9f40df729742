package com.avatar.hospital.chaperone.response.baccount;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class OrganizationHospitalResponse implements Serializable {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 上级组织机构ID 顶级节点默认为null
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 联络人名称
     */
    private String liaisonName;

    /**
     * 联络人电话
     */
    private String liaisonPhoneNumber;

    /**
     * 银行账号
     */
    private String bankAccountNumber;

    /**
     * 层级
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationLevel
     */
    private Integer level;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.OrganizationStatus
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;
}
