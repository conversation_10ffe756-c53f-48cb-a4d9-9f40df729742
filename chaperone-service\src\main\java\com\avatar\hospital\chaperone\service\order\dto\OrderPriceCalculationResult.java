package com.avatar.hospital.chaperone.service.order.dto;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderBillDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import lombok.Data;

import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-02 15:55
 **/
@Data
public class OrderPriceCalculationResult {

    /**
     * 订单
     */
    private OrderDO orderDO;

    /**
     * 总账单数据
     */
    private OrderBillDO totalBill;

    /**
     *  订单商品数据
     */
    private List<OrderItemDO> orderItemDOList;

}
