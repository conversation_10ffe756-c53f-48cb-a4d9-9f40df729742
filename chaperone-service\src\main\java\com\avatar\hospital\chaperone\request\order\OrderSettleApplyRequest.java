package com.avatar.hospital.chaperone.request.order;

import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-11 16:47
 **/
@Data
public class OrderSettleApplyRequest implements OperatorReq,Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 结算操作: 0 驳回 1 通过
     */
    private Integer settleApplyOperate;

    /**
     * 操作用户
     */
    private Operator operatorUser;

    public static final OrderSettleApplyRequest buildById(Long orderId) {
        OrderSettleApplyRequest obj = new OrderSettleApplyRequest();
        obj.setOrderId(orderId);
        obj.setOperatorUser(OrderConst.JOB_OPERATOR);
        obj.setSettleApplyOperate(1);
        return obj;
    }

    public Boolean isPass() {
        return Objects.equals(1,settleApplyOperate);
    }

    public Boolean isReject() {
        return Objects.equals(0,settleApplyOperate);
    }

}
