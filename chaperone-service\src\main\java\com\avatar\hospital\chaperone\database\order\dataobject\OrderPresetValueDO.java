package com.avatar.hospital.chaperone.database.order.dataobject;

import com.avatar.hospital.chaperone.database.order.dataobject.base.BaseDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderPresetValueStatus;
import com.avatar.hospital.chaperone.database.order.enums.OrderPresetValueType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订单信息预设置;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Getter
@Setter
  @TableName("t_order_preset_value")
public class OrderPresetValueDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 订单ID
     */
      private Long orderId;

      /**
     * 生效时间(时间戳)
     */
      private Long time;

      /**
     * 预设类型 1 护工
       * @see OrderPresetValueType
     */
      private Integer type;

      /**
     * 预设值
     */
      private String val;

      /**
     * 状态 0 未处理 1 已处理
       * @see OrderPresetValueStatus
     */
      private Integer status;


}
