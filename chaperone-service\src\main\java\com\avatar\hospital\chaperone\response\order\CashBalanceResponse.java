package com.avatar.hospital.chaperone.response.order;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description: 订单简易信息
 * @author: sp0372
 * @create: 2023-10-11 16:55
 **/
@Data
public class CashBalanceResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 余额 单位 分
     */
    private Integer price;


    /**
     * 现金余额 单位 分
     */
    private Integer cashPrice;


    /**
     * 支付宝余额 单位 分
     */
    private Integer alipayPrice;
    public static CashBalanceResponse build(int price,
                                            int cashPrice,
                                            int alipayPrice) {
        CashBalanceResponse obj = new CashBalanceResponse();
        obj.setPrice(price);
        obj.setCashPrice(cashPrice);
        obj.setAlipayPrice(alipayPrice);
        return obj;
    }
}
