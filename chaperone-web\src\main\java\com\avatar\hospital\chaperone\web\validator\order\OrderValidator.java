package com.avatar.hospital.chaperone.web.validator.order;

import com.avatar.hospital.chaperone.database.item.enums.ItemServerType;
import com.avatar.hospital.chaperone.database.order.enums.CashSource;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillSubType;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillType;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.OrderUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 套餐校验
 * @author: sp0372
 * @create: 2023-10-13 10:38
 **/
public class OrderValidator {
    public static void modifyBillValidate(OrderBillModifyTradeRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isTrue(!OrderBillType.NONE.equals(OrderBillType.of(request.getBillType())),ErrorCode.ORDER_BILL_TYPE_ERROR);
        AssertUtils.isTrue(!OrderBillSubType.NONE.equals(OrderBillType.of(request.getBillSubType())),ErrorCode.ORDER_BILL_TYPE_ERROR);

        // 如果不是预付单 价格设置为0
        if (!OrderBillType.of(request.getBillType()).isPrepay()) {
            request.setPrice(0);
        }
    }

    public static void cashPayValidate(OrderBillCashPayRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getBillId(), ErrorCode.PARAMETER_ERROR);
        // 默认现金
        if (Objects.isNull(request.getCashSource())) {
            request.setCashSource(CashSource.CASH.getStatus());
        }
    }

    public static void commitSettleValidate(OrderSettleApplyRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getOrderId(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getSettleApplyOperate(), ErrorCode.PARAMETER_ERROR);
    }

    public static void commitCompleteInfoValidate(OrderRequest request) {

    }

    public static void commitCertifiedPropertyValidate(OrderRequest request) {

    }

    public static void commitHospitalValidate(OrderRequest request) {

    }

    public static void modifyOrderItemValidate(OrderModifyItemRequest request) {
        List<Long> itemIdList = request.getItemIdList();
        if (Objects.isNull(itemIdList) || CollectionUtils.isEmpty(itemIdList)) {
            throw BusinessException.buildBusinessException(ErrorCode.ORDER_ITEM_MODIFY_ITEM_LIST_IS_NOT_NULL);
        }
    }

    public static void modifyNursingValidate(OrderModifyNursingRequest request) {

    }

    public static void modifyRateValidate(OrderModifyRateRequest request) {
        AssertUtils.isTrue((request.getRateCertifiedProperty() + request.getRateHospital() + request.getRateNursing()) == OrderUtils.DISCOUNT_RATE,
                ErrorCode.ORDER_RATE_ERROR);
    }

    public static void modifyDiscountValidate(OrderModifyDiscountRequest request) {

    }

    public static void modifyValidate(OrderModifyRequest request) {

    }

    /**
     * 现金提取
     * @param request
     */
    public static void extractByCash(CashExtractRequest request) {
        AssertUtils.isTrue(request.getPrice() > 0,ErrorCode.ORDER_NOT_EXIST);
        if (Objects.isNull(request.getCashSource())) {
            request.setCashSource(CashSource.CASH.getStatus());
        }
    }

    /**
     * 现金存入
     * @param request
     */
    public static void extractByCash(CashDepositRequest request) {
        AssertUtils.isTrue(request.getPrice() > 0,ErrorCode.ORDER_NOT_EXIST);
        if (Objects.isNull(request.getCashSource())) {
            request.setCashSource(CashSource.CASH.getStatus());
        }
    }

    public static void createValidate(OrderCreateRequest request) {
        AssertUtils.isTrue(Objects.nonNull(request.getAccountPhone()),ErrorCode.PARAMETER_ERROR);
        request.cleanNull();
        ItemServerType serverType = ItemServerType.of(request.getServerType());
        if (Objects.nonNull(serverType.getOrgId())) {
            request.setOrgId(serverType.getOrgId());
        }
    }
}
