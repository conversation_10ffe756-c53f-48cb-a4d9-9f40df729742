package com.avatar.hospital.chaperone.consumer.handler;


import com.avatar.hospital.chaperone.consumer.utils.ConsumerAccountUtils;
import com.avatar.hospital.chaperone.response.caccount.ConsumerAccountDetailResponse;
import com.avatar.hospital.chaperone.service.caccount.ConsumerAccountService;
import com.avatar.hospital.chaperone.template.model.Operator;
import com.avatar.hospital.chaperone.template.model.OperatorReq;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/13
 */
@Order(value = 100)
@Slf4j
@Aspect
@Component
public class WebOperatorAspect {

    @Autowired
    private ConsumerAccountService consumerAccountService;
    @Pointcut("execution(public * com.avatar.hospital.chaperone.consumer.controller..*.*(..))")
    public void log() {
    }

    /**
     * 环绕
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Before("log()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg instanceof OperatorReq) {
                // 赋值
                Long currentAccountId = ConsumerAccountUtils.getCurrentAccountId();
                if (Objects.nonNull(currentAccountId)) {
                    ConsumerAccountDetailResponse detail = consumerAccountService.detail(currentAccountId);
                    if (Objects.nonNull(detail)) {
                        Operator operator = new Operator();
                        operator.setId(detail.getId());
                        operator.setMobile(detail.getPhoneNumber());
                        operator.setName(detail.getNickName());
                        operator.setSource(1);
                        ((OperatorReq) arg).setOperatorUser(operator);
                    }
                }
            }
        }
    }

}
