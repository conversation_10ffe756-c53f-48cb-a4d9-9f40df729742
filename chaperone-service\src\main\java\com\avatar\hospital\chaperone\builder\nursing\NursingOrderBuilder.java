package com.avatar.hospital.chaperone.builder.nursing;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDayDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingOrderDO;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingSettletWay;
import com.avatar.hospital.chaperone.request.nursing.NursingSubstituteRequest;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.IdUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author:sp0420
 * @Description:
 */
public class NursingOrderBuilder {

    public static List<NursingOrderDO> buildNursingOrderDO(List<NursingOrderDO> request, NursingSubstituteRequest substituteRequest) {
        if (request == null) {
            return null;
        }
        request.stream().forEach(i->{
            i.setNursingId(substituteRequest.getNursingId());
            // 后端强制设置
            i.setWay(1);
            i.setUpdateBy(substituteRequest.getUpdateBy());
            i.setUpdatedAt(LocalDateTime.now());
        });

        return request;
    }
}
