package com.avatar.hospital.chaperone.response.order;


import com.avatar.hospital.chaperone.database.order.enums.CashBizType;
import com.avatar.hospital.chaperone.database.order.enums.CashSource;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * @program: hospital-chaperone
 * @description: 订单简易信息
 * @author: sp0372
 * @create: 2023-10-11 16:55
 **/
@Data
public class CashResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 类型，1 增加，2提取
     */
    private Integer type;

    /**
     * 金额 单位分
     */
    private Integer price;

    /**
     * 业务类型，1 账单现金提取 0 人工提取
     */
    private Integer bizType;

    /**
     * 业务ID(账单编号)
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bizId;

    /**
     * 提取人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long personId;

    /**
     * 提取人姓名
     */
    private String personName;

    /**
     * 操作时间
     */
    private String createAt;

    /**
     * 金额类型 1 现金 3 支付宝
     * @see com.avatar.hospital.chaperone.database.order.enums.CashSource
     */
    private Integer cashSource;

    public Long getBizId() {
        return Objects.equals(0L,bizId) ? null : bizId;
    }

    public Integer getBizType() {
        if (Objects.equals(CashBizType.BILL.getStatus(),bizType)
            && Objects.equals(CashSource.ALIPAY.getStatus(),cashSource)) {
            return CashBizType.ALIPAY_DEPOSIT.getStatus();
        }
        return bizType;
    }
}
