package com.avatar.hospital.chaperone.database.part.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description （0-待入库，1-可用，2-已使用）
 * @date 2023/10/26 19:55
 */
@Getter
public enum PartStatusType {
    NONE(-1, "未知"),
    NON_STOCK(0, "待入库"),
    STOCK(1, "已入库"),
    USED(2, "已使用"),
    ;

    private final Integer code;

    private final String describe;


    PartStatusType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static PartStatusType of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (PartStatusType type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
