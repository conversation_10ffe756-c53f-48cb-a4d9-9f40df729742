package com.avatar.hospital.chaperone.database.repair.repository.impl;

import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormTaskDO;
import com.avatar.hospital.chaperone.database.repair.mapper.RepairFormTaskMapper;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormTaskRepositoryService;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 报修单任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
public class RepairFormTaskRepositoryServiceImpl extends ServiceImpl<RepairFormTaskMapper, RepairFormTaskDO> implements RepairFormTaskRepositoryService {

    @Override
    public List<RepairFormTaskDO> getByFromIdList(List<Long> formIdList) {
        if(CollectionUtils.isEmpty(formIdList)) {
            return Collections.EMPTY_LIST;
        }
        LambdaQueryWrapper<RepairFormTaskDO> queryWrapper = queryWrapper();
        queryWrapper.in(RepairFormTaskDO::getRepairFormId,formIdList);
        return list(queryWrapper);
    }

    @Override
    public List<RepairFormTaskDO> getByFromId(Long id) {
        LambdaQueryWrapper<RepairFormTaskDO> queryWrapper = queryWrapper();
        queryWrapper.eq(RepairFormTaskDO::getRepairFormId,id);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<RepairFormTaskDO> queryWrapper() {
        LambdaQueryWrapper<RepairFormTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairFormTaskDO::getDeleted, OrderConst.NO_DELETED);
        return queryWrapper;
    }
}
