package com.avatar.hospital.chaperone.database.order.enums;

import com.avatar.hospital.chaperone.enums.IEnumConvert;
import lombok.Getter;

import java.util.Objects;

/**
 * Description:
 *  退款状态
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrderRefundStatus implements IEnumConvert<Integer> {
    NONE(-1, "未知"),
    REFUND_ING(0, "退款中"),
    SUCCESS(10, "退款成功"),
    FAIL(2, "退款失败"),
    ;

    private final Integer status;

    private final String describe;


    OrderRefundStatus(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static OrderRefundStatus of(Integer status) {
        if (status == null) {
            return NONE;
        }
        for (OrderRefundStatus itemStatus : values()) {
            if (status.equals(itemStatus.getStatus())) {
                return itemStatus;
            }
        }
        return NONE;
    }

    @Override
    public String convertDesc(Integer val) {
        OrderRefundStatus e = of(val);
        return Objects.isNull(e) ? UNKONW_TIP : e.getDescribe();
    }
}
