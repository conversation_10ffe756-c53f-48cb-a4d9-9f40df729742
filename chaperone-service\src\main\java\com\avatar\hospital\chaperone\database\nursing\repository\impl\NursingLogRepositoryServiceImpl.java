package com.avatar.hospital.chaperone.database.nursing.repository.impl;

import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingLogDO;
import com.avatar.hospital.chaperone.database.nursing.mapper.NursingLogMapper;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingLogRepositoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 护工-操作记录表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class NursingLogRepositoryServiceImpl extends ServiceImpl<NursingLogMapper, NursingLogDO> implements NursingLogRepositoryService {

}
