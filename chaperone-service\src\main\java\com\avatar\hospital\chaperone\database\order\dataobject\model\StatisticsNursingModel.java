package com.avatar.hospital.chaperone.database.order.dataobject.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * 评价统计数据
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-20 11:51
 **/
@Data
public class StatisticsNursingModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 护工ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 护工姓名
     */
    private String name;

    /**
     * 总评分
     */
    private Integer star;
}
