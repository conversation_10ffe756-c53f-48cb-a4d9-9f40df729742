package com.avatar.hospital.chaperone.database.device.repository;

import com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO;
import com.avatar.hospital.chaperone.request.device.DevicePagingRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设备 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface ProjectDeviceRepositoryService extends IService<ProjectDeviceDO> {

    /**
     * 通过设备id查询
     *
     * @param linkDeviceId
     * @return
     */
    Map<Long, String> findMapByIdList(List<Long> linkDeviceId);

    /**
     * @param deviceId
     * @return
     */
    String findNameById(Long deviceId);

    /**
     * @param projectDeviceDO
     * @return
     */
    Long add(ProjectDeviceDO projectDeviceDO);

    /**
     * @param id
     * @return
     */
    ProjectDeviceDO findById(Long id);

    /**
     * @param projectDeviceDO
     * @return
     */
    Boolean incrementUpdate(ProjectDeviceDO projectDeviceDO);

    /**
     *
     * @param request
     * @return
     */
    PageResponse<ProjectDeviceDO> paging(DevicePagingRequest request);


    /**
     *
     * @param start
     * @param end
     * @param model 0 正序 1 倒序
     * @return
     */
    List<ProjectDeviceDO> fault10(LocalDateTime start,LocalDateTime end,Integer model);
}
