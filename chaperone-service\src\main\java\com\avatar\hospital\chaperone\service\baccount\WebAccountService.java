package com.avatar.hospital.chaperone.service.baccount;

import com.avatar.hospital.chaperone.request.baccount.WebAccountAddRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountAllocationRoleRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountUpdatePasswordRequest;
import com.avatar.hospital.chaperone.request.baccount.WebAccountUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountAddResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountAllocationRoleResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountBasicResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountPagingResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountUpdatePasswordResponse;
import com.avatar.hospital.chaperone.response.baccount.WebAccountUpdateResponse;

import java.util.Collection;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
public interface WebAccountService {

    /**
     * 添加B端用户
     *
     * @param request -
     * @return -
     */
    WebAccountAddResponse add(WebAccountAddRequest request);

    /**
     * 更新B端用户
     *
     * @param request -
     * @return -
     */
    WebAccountUpdateResponse update(WebAccountUpdateRequest request);

    /**
     * 删除B端用户
     *
     * @param request -
     * @return -
     */
    WebAccountDeleteResponse delete(WebAccountDeleteRequest request);

    /**
     * 修改B端用户密码
     *
     * @param request -
     * @return -
     */
    WebAccountUpdatePasswordResponse changePassword(WebAccountUpdatePasswordRequest request);

    /**
     * 给用户分配角色
     *
     * @param request -
     * @return -
     */
    WebAccountAllocationRoleResponse allocationRole(WebAccountAllocationRoleRequest request);

    /**
     * B端用户列表
     *
     * @param request -
     * @return -
     */
    PageResponse<WebAccountPagingResponse> paging(WebAccountPagingRequest request);

    /**
     * B端用户详情
     *
     * @param accountId -
     * @return -
     */
    WebAccountDetailResponse detail(Long accountId);
    Map<Long, String> getAccountMap(Collection<Long> ids);
    WebAccountBasicResponse basicInfo(Long accountId);

}
