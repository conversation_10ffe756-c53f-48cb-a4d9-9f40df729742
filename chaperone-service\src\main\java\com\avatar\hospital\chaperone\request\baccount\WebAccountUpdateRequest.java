package com.avatar.hospital.chaperone.request.baccount;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class WebAccountUpdateRequest implements Serializable {

    /**
     * 组织机构ID
     */
    private Set<Long> organizationIds;

    /**
     * 部门ID
     */
    private Set<Long> departmentIds;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 用户头像地址
     */
    private String avatarUrl;

    /**
     * 启用状态
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.AccountStatus
     */
    private Integer status;

    /**
     * 用户类型
     *
     * @see com.avatar.hospital.chaperone.database.baccount.enums.AccountType
     */
    private Integer type;

    /**
     * 更新者ID
     */
    private Long updateBy;

}
