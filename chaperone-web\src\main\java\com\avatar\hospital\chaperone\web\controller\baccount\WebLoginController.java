package com.avatar.hospital.chaperone.web.controller.baccount;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.request.baccount.PhoneNumberPasswordWebLoginRequest;
import com.avatar.hospital.chaperone.response.baccount.PhoneNumberPasswordWebLoginResponse;
import com.avatar.hospital.chaperone.service.baccount.WebLoginService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.web.validator.login.WebLoginValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: B端用户登录接口
 *
 * <AUTHOR>
 * @since 2023/10/8
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/login")
public class WebLoginController {

    private final WebLoginService webLoginService;

    /**
     * 手机号码/密码登录
     *
     * @param request -
     * @return -
     */
    @PostMapping(value = "")
    public SingleResponse<PhoneNumberPasswordWebLoginResponse> login(@RequestBody PhoneNumberPasswordWebLoginRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("WebLoginController login request:{}", JSON.toJSONString(request));
            WebLoginValidator.loginValidate(request);
            return webLoginService.login(request);
        });
    }

}
