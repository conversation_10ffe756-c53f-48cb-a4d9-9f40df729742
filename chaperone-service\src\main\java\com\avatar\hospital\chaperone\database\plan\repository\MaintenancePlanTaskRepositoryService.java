package com.avatar.hospital.chaperone.database.plan.repository;

import com.avatar.hospital.chaperone.database.plan.dataobject.MaintenancePlanTaskDO;
import com.avatar.hospital.chaperone.database.plan.repository.base.PlanTaskRepositoryService;
import com.avatar.hospital.chaperone.request.plan.PlanRequest;
import com.avatar.hospital.chaperone.request.plan.TaskExecuteRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 维保计划任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface MaintenancePlanTaskRepositoryService extends IService<MaintenancePlanTaskDO>, PlanTaskRepositoryService {

    LambdaQueryWrapper<MaintenancePlanTaskDO> queryWrapper();

    Boolean execute(TaskExecuteRequest request);

    Boolean expired(List<Long> taskIds);

    boolean update2delete(PlanRequest request);

    /**
     * 统计维保计划任务统计
     * @param year
     * @return
     */
    Map<Integer, Integer> getStatisticsForYear(Integer year);

    /**
     * 统计维保计划任务统计
     * @param month
     * @return
     */
    Map<Integer, Integer> getStatisticsForMonth(Integer month);
}
