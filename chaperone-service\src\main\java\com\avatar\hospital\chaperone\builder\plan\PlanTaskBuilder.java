package com.avatar.hospital.chaperone.builder.plan;

import com.avatar.hospital.chaperone.database.plan.dataobject.base.PlanTaskDO;
import com.avatar.hospital.chaperone.response.plan.PlanTaskVO;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/30 10:52
 */
public class PlanTaskBuilder {

    public static PlanTaskVO build(PlanTaskDO taskDO) {
        PlanTaskVO planVO = new PlanTaskVO();
        BeanUtils.copyProperties(taskDO, planVO);
        planVO.setPlanId(taskDO.getPlanId());
        return planVO;

    }

}
