package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.CashLogDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 现金记录表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
public interface CashLogRepositoryService extends IService<CashLogDO> {

    /**
     * 统计某种类型
     * @param type
     * @return
     */
    Integer sumByType(Integer type);

    /**
     * 统计某种类型
     * @param type
     * @param source @see com.avatar.hospital.chaperone.database.order.enums.CashSource
     * @return
     */
    Integer sumByType(Integer type,Integer source);
}
