package com.avatar.hospital.chaperone.response.repair;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-27 10:49
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairFormIdResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
}
