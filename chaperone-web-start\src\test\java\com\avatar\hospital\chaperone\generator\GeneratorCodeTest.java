package com.avatar.hospital.chaperone.generator;

import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.TemplateConfig;
import com.baomidou.mybatisplus.generator.config.TemplateType;
import com.baomidou.mybatisplus.generator.config.po.LikeTable;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.avatar.hospital.chaperone.generator.convert.EntityNameConvert;
import com.avatar.hospital.chaperone.generator.convert.MapperConverterFileName;
import com.avatar.hospital.chaperone.generator.convert.ServiceConverterFileName;
import com.avatar.hospital.chaperone.generator.convert.ServiceImplConverterFileName;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/08
 */
public class GeneratorCodeTest {

    //public static final String filePath = "D:\\文档\\code";

    public static final String filePath = "C:\\Users\\<USER>\\Documents\\code";

    public static final String dataBaseUrl = "***********************************************************************************************************************************************************************************************************************************************************";
    public static final String username = "hospital_chaperone";
    public static final String password = "gG8lM0lE5z";
    public static final String modelName = "code";
    public static final String dataObjectSuperClass = "com.avatar.hospital.chaperone.database."+modelName+".dataobject.base.BaseDO";

    public static final String[] orderTables = {"t_order","t_order_refund","t_order_pay","t_order_item","t_order_bill","t_order_estimate","t_order_invoice","t_order_changelog","t_order_consumerlog","t_order_nursing"};
    public static final String[] itemTables = {"t_item","t_item_org"};
    public static final String[] nursingTables = {"t_nursing","t_nursing_day","t_nursing_hospital","t_nursing_order","t_nursing_log","t_nursing_top"};
    public static final String[] statisticStables = {"t_statistics"};
    public static final String[] message = {"t_message_c"};
    public static final String[] cash = {"t_cash_log"};
    public static final String[] plan = {"t_project_maintenance_plan","t_project_maintenance_plan_ref_device","t_project_maintenance_plan_ref_executor","t_project_maintenance_plan_ref_org","t_project_maintenance_plan_task","t_project_patrol_plan","t_project_patrol_plan_ref_device","t_project_patrol_plan_ref_executor","t_project_patrol_plan_ref_org","t_project_patrol_plan_task"};
    public static final String[] part = {"t_project_spare_part","t_project_spare_part_apply","t_project_spare_part_apply_ref_part_batch","t_project_spare_part_batch","t_project_spare_part_stock_apply_ref_part_batch","t_project_spare_part_stock_apply"};
    public static final String[] repair = {"t_project_repair_form","t_project_repair_form_feedback","t_project_repair_form_task"};
    public static final String[] device = {"t_project_device"};
    public static final String[] emergencylog = {"t_project_emergency_handling_log"};
    public static final String[] tables = {"t_project_code"};


    public static final String parentPackage = "com.avatar.hospital.chaperone";
    public static final String dataobjectPackage = "database." + modelName + ".dataobject";
    public static final String repositoryPackage = "database." + modelName + ".repository";
    public static final String repositoryImplPackage = "database." + modelName + ".repository.impl";
    public static final String mapperPackage = "database." + modelName + ".mapper";

    public static void main(String[] args) {


        DataSourceConfig dataSourceConfig = new DataSourceConfig.Builder(dataBaseUrl, username, password).build();

        AutoGenerator autoGenerator = new AutoGenerator(dataSourceConfig);

        GlobalConfig globalConfig = new GlobalConfig.Builder().outputDir(filePath).author("sp0347").dateType(DateType.TIME_PACK).commentDate("yyyy-MM-dd").build();

        autoGenerator.global(globalConfig);

        PackageConfig packageConfig = new PackageConfig.Builder()
                .parent(parentPackage)
                .entity(dataobjectPackage)
                .service(repositoryPackage)
                .serviceImpl(repositoryImplPackage)
                .mapper(mapperPackage)
                .build();

        autoGenerator.packageInfo(packageConfig);

        TemplateConfig templateConfig = new TemplateConfig.Builder()
                .disable(TemplateType.CONTROLLER, TemplateType.XML)
                .build();

        autoGenerator.template(templateConfig);


        StrategyConfig strategyConfig = new StrategyConfig.Builder()
                .enableCapitalMode()
                .enableSkipView()
                .disableSqlFilter()
                .addInclude(tables)
                .addTablePrefix("t_")
                .build();

        strategyConfig = strategyConfig.entityBuilder().enableLombok().superClass(dataObjectSuperClass).nameConvert(new EntityNameConvert(strategyConfig))
                .addSuperEntityColumns("deleted", "create_by", "update_by", "created_at", "updated_at")
                .build();

        strategyConfig = strategyConfig.serviceBuilder().convertServiceFileName(new ServiceConverterFileName()).convertServiceImplFileName(new ServiceImplConverterFileName()).build();

        strategyConfig = strategyConfig.mapperBuilder().convertMapperFileName(new MapperConverterFileName()).build();

        autoGenerator.strategy(strategyConfig);


        InjectionConfig injectionConfig = new InjectionConfig.Builder().build();

        autoGenerator.injection(injectionConfig);

        autoGenerator.execute();
    }
}
