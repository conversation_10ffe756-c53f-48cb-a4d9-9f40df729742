package com.avatar.hospital.chaperone.template.serialize;

import com.avatar.hospital.chaperone.utils.DateUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * LocalDateTime => yyyy-MM-dd HH:mm:ss
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-26 16:36
 **/
public class ToStringLocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {
    @Override
    public void serialize(LocalDateTime localDateTime, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {

        if (Objects.isNull(localDateTime)) {
            jsonGenerator.writeNull();
        }else {
            String time = localDateTime.format(DateTimeFormatter.ofPattern(DateUtils.DATETIME_PATTERN));
            jsonGenerator.writeString(time);
        }
    }
}
