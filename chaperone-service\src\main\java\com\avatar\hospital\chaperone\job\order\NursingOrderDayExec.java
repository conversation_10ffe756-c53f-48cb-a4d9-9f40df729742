package com.avatar.hospital.chaperone.job.order;

import com.avatar.hospital.chaperone.builder.nursing.NursingDayBuilder;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingDayDO;
import com.avatar.hospital.chaperone.database.nursing.dataobject.NursingOrderDO;
import com.avatar.hospital.chaperone.database.nursing.enums.NursingDayStatus;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingDayRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingOrderRepositoryService;
import com.avatar.hospital.chaperone.database.nursing.repository.NursingRepositoryService;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderNursingDO;
import com.avatar.hospital.chaperone.database.order.repository.OrderRepositoryService;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.ExceptionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: hospital-chaperone
 * @description: 护工-订单考勤信息表每日生成
 * @author: sp0372
 * @create: 2023-10-24 15:34
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class NursingOrderDayExec {
    private final OrderRepositoryService orderRepositoryService;
    private final NursingDayRepositoryService nursingDayRepositoryService;
    private final NursingOrderRepositoryService nursingOrderRepositoryService;
    private final NursingRepositoryService nursingRepositoryService;


    /**
     * 新增加排班30天
     * @param nursingId
     * @return
     */
    public Boolean add(Long nursingId) {
        Integer[] date = DateUtils.dateStartEnd();
        add(nursingId,date[0],date[1]);
        return Boolean.TRUE;
    }

    /**
     * 批量添加
     *
     * @param startDate yyyyMMdd
     * @param endDate yyyyMMdd
     */
    public Boolean add(Long nursingId,Integer startDate,Integer endDate) {
        log.info("NursingOrderDayExec add start >> nursingId:{},startDate:{}, endDate:{}",nursingId,startDate, endDate);
        NursingDO query = null;
        if (Objects.nonNull(nursingId)) {
            query = new NursingDO();
            query.setId(nursingId);
        }
        PageResponse<NursingDO> page = nursingRepositoryService.paging(1, 1, query);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return Boolean.TRUE;
        }
        int pageIndex = 1;
        int pageSize = 1000;
        List<NursingDO> records = null;
        do {
            log.info("NursingOrderDayExec add process pageIndex:{}",pageIndex);
            page = nursingRepositoryService.paging(pageIndex, pageSize, query);
            records = page.getRecords();
            for (NursingDO nursingDO : records) {
                ExceptionUtil.execute(log,() -> addNursingBindOrder(nursingDO.getId(),nursingDO.getName(),startDate,endDate));
            }
        }while (records.size() >= pageSize);
        log.info("NursingOrderDayExec add end");
        return Boolean.TRUE;
    }

    /**
     * 触发点
     *  * C端用户确认订单,会生成对应护工的排班信息
     *  * 修改护工信息,会生成对应护工的排班信息
     *
     * @param newNursingDOList
     * @param orderId
     * @param date
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addList(Boolean delFlag,List<OrderNursingDO> newNursingDOList,
                          @NotNull Long orderId,
                          Integer... date) {
        Integer startDate = date[0];
        Integer endDate = date[1];
        if (delFlag) {
            // 删除护工-订单绑定关系
            delNursingOrder(orderId,startDate);
        }
        for (OrderNursingDO nursingDO : newNursingDOList) {

            addOrderBind(nursingDO.getNursingId(),nursingDO.getNursingName(),orderId,startDate,endDate);
        }
        return Boolean.TRUE;
    }

    /**
     * 某笔订单某个护工的绑定关系
     * @param orderId
     * @param startDate 20231227 开始时间 包括
     */
    public void delNursingOrder(Long orderId, Integer startDate) {
        log.info("NursingOrderDayExec[]delNursingOrder start >> orderId:{}, startDate:{}",orderId, startDate);
        nursingOrderRepositoryService.delNursingOrder(orderId,startDate);
    }


    /**
     * 给某个护工添加某个订单的绑定关系
     *  * 订单ID 传递过来为null 只会添加排班信息,不会添加绑定信息
     * 护工-订单考勤数据
     *     添加
     *       定时任务添加(每个月生成下一个月的任务)
     *       订单绑定护工时添加
     *       B端设置替班
     *     删除
     *       护工删除时删除
     *       B端结算确认
     * @param nursingId 护工ID
     * @param name 护工名称
     * @param orderId 订单ID
     * @param startDate 开始时间 yyyyMMdd
     * @param endDate 结束时间 yyyyMMdd
     */
    public Boolean addOrderBind(@NotNull Long nursingId,
                                @NotNull String name,
                                Long orderId,
                                Integer startDate, Integer endDate) {
        log.info("NursingOrderDayExec[]addOne start>> nursingId:{}, name:{}, orderId:{}, startDate:{}, endDate:{}",nursingId, name, orderId, startDate, endDate);
        // 查找这个护工下所有订单数据 返回根据日期设计的所有订单(目前只有一个订单)
        List<Integer> dateAllList = DateUtils.getAllDate(startDate,endDate);
        Map<Integer,Long> dateRefOrderIdMap = getDateOrderMap(orderId,dateAllList);
        // 日期,护工-订单关联数据
        Map<Integer,Map<Long,NursingOrderDO>> oldDateRefNursingOrderMap = nursingOrderRepositoryService.findAllByOrderId(orderId,startDate,endDate);
        // 日期,护工数据
        Map<Integer,NursingDayDO> dateRefNursingDayMap = nursingDayRepositoryService.findAllByNursingId(nursingId,startDate,endDate);

        // 存储数据容器
        List<NursingDayDO> NursingDayStore = Lists.newLinkedList();
        List<NursingOrderDO> NursingOrderStore = Lists.newLinkedList();
        // 遍历时间日期
        for (Integer date : dateAllList) {
            log.info("NursingOrderDayExec for addOne data:{}",date);
            // 护工某天记录
            NursingDayDO nursingDay = dateRefNursingDayMap.getOrDefault(date, NursingDayBuilder.createNursingDayDO(date, nursingId, name));
            if (nursingDay.isNotStatusLeave()) { // 不是请假状态 设置为空闲
                nursingDay.setStatus(NursingDayStatus.FREE.getStatus());
            }
            // 获取某天 订单 和 护工 的绑定记录 所有记录
            Map<Long,NursingOrderDO> nursingOrderDOMap = oldDateRefNursingOrderMap.get(date);
            Boolean nursingOrderExist = Objects.nonNull(nursingOrderDOMap) && Objects.nonNull(nursingOrderDOMap.get(nursingId)); // 护工订单绑定关系是否存在

            Long processOrderId = dateRefOrderIdMap.get(date);
            if (Objects.nonNull(processOrderId)) {
                // 有订单数据
                if (!nursingOrderExist) { // 没有绑定记录 添加绑定记录 有绑定记录无需操作
                    NursingOrderStore.add(NursingDayBuilder.createNursingOrderDO(date,processOrderId,nursingId));
                }
            }

            NursingDayStore.add(nursingDay);
        }
        nursingDayRepositoryService.saveByAdd(NursingDayStore,NursingOrderStore);
        log.info("NursingOrderDayExec addOne end");
        return Boolean.TRUE;
    }

    /**
     * 每月添加护工ID
     * @param nursingId 护工ID
     * @param name 护工名称
     * @param startDate 开始时间 yyyyMMdd
     * @param endDate 结束时间 yyyyMMdd
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addNursingBindOrder(@NotNull Long nursingId,
                                       @NotNull String name,
                                       Integer startDate, Integer endDate) {
        log.info("NursingOrd erDayExec add0 start>> nursingId:{}, name:{}, startDate:{}, endDate:{}",nursingId, name, startDate, endDate);
        // 所有天数 指定护工所有订单数据 日期 订单列表数据
        List<OrderDO> orderList = orderRepositoryService.findAllByNursingId(nursingId,null,startDate,endDate);
        if (CollectionUtils.isEmpty(orderList)) {
            // 没有订单 只添加护工日期
            addOrderBind(nursingId,name,null,startDate,endDate);
            return Boolean.TRUE;
        }
        List<Long> orderIdList = CollUtils.toListLongDistinct(orderList,OrderDO::getId);
        for (Long orderId : orderIdList) {
            addOrderBind(nursingId,name,orderId,startDate,endDate);
        }
        log.info("NursingOrderDayExec addOne end");
        return Boolean.TRUE;
    }

    /**
     * 单个护工维度
     * 护工-订单考勤数据
     *     添加
     *       定时任务添加(每个月生成下一个月的任务)
     *       订单绑定护工时添加
     *       B端设置替班
     *     删除
     *       护工删除时删除
     *       B端结算确认
     * @param nursingId 护工ID
     * @param name 护工名称
     * @param orderId 订单ID
     * @param startDate 开始时间 yyyyMMdd
     * @param endDate 结束时间 yyyyMMdd
     */
    @Deprecated
    public Boolean addOneOld(@NotNull Long nursingId,
                             @NotNull String name,
                             Long orderId,
                             Integer startDate, Integer endDate) {
        log.info("NursingOrd erDayExec add0 start>> nursingId:{}, name:{}, orderId:{}, startDate:{}, endDate:{}",nursingId, name, orderId, startDate, endDate);
        // 准备数据  所有天数 指定护工所有订单数据 日期 订单列表数据
        Long delVersion = DelUtils.delVersion();
        List<Integer> dateAllList = DateUtils.getAllDate(startDate,endDate);
        List<OrderDO> orderList = orderRepositoryService.findAllByNursingId(nursingId,orderId,startDate,endDate);
        Map<Integer,List<Long>> dateOrderMap = getDateOrderMap(orderList,dateAllList);
        List<Long> orderIdList = CollUtils.toListLongDistinct(orderList,OrderDO::getId);
        Map<Integer, Map<Long,NursingOrderDO>> oldDateOrderIdNursingRef = nursingOrderRepositoryService.findAllByOrderId(orderIdList,startDate,endDate);
        Map<Integer,NursingDayDO> dateNursingDayRef = nursingDayRepositoryService.findAllByNursingId(nursingId,startDate,endDate);

        // 批量存储数据
        List<NursingDayDO> batchStoreNursingDaylist = new LinkedList<>();
        List<NursingOrderDO> batchStoreNursingOrderList = new LinkedList<>();
        for (Integer date : dateAllList) {
            log.info("NursingOrderDayExec add0 日期:{}",date);
            // 生成所有护工某一天的记录
            NursingDayDO nursingDay = dateNursingDayRef.getOrDefault(date, NursingDayBuilder.createNursingDayDO(date, nursingId, name));
            if (nursingDay.isNotStatusLeave()) {
                nursingDay.setStatus(NursingDayStatus.FREE.getStatus());
            }

            Map<Long, NursingOrderDO> oldNursingOrderRef = oldDateOrderIdNursingRef.getOrDefault(date,Maps.newHashMap());
            oldNursingOrderRef.forEach((k,v) -> v.setDeleted(delVersion));

            List<Long> processOrderIdList = dateOrderMap.getOrDefault(date,Collections.emptyList());
            for (Long processOrderId : processOrderIdList) {
                // 有订单数据
                NursingOrderDO nursingOrderDO = oldNursingOrderRef.get(processOrderId);
                if (Objects.isNull(nursingOrderDO)) {
                    batchStoreNursingOrderList.add(NursingDayBuilder.createNursingOrderDO(date,processOrderId,nursingId));
                } else {
                    if (nursingOrderDO.isNonForce()) {
                        nursingOrderDO.setNursingId(nursingId);
                    }
                    nursingOrderDO.setDeleted(DelUtils.NO_DELETED);
                    batchStoreNursingOrderList.add(nursingOrderDO);
                }

                if (!Objects.equals(NursingDayStatus.LEAVE,NursingDayStatus.of(nursingDay.getStatus()))) {
                    nursingDay.setStatus(NursingDayStatus.BUSY.getStatus());
                }
            }

            // oldNursingOrderRef 没有跑过的数据 删除
            oldNursingOrderRef.forEach((oid,nursingOrderDO) -> {
                if (!processOrderIdList.contains(oid)) {
                    batchStoreNursingOrderList.add(nursingOrderDO);
                }
            });

            batchStoreNursingDaylist.add(nursingDay);
        }
        nursingDayRepositoryService.saveByAdd(batchStoreNursingDaylist,batchStoreNursingOrderList);
        log.info("NursingOrderDayExec addOne end");
        return Boolean.TRUE;
    }

    /**
     * <date,List<orderId>>
     *
     * @param orderList
     * @param dateList
     * @return
     */
    private Map<Integer, List<Long>> getDateOrderMap(List<OrderDO> orderList, List<Integer> dateList) {
        Map<Integer, List<Long>> result = Maps.newHashMap();
        for (Integer date : dateList) {
            List<Long> orderIdList = orderList.stream()
                    .filter(order -> DateUtils.between(date, order.getRealStartTime(), order.getRealEndTime()))
                    .map(OrderDO::getId)
                    .collect(Collectors.toList());
            result.put(date,orderIdList);
        }
        return result;
    }

    /**
     * <date,orderId>>
     *
     * @param orderId 订单ID
     * @param dateList
     * @return
     */
    private Map<Integer, Long> getDateOrderMap(Long orderId, List<Integer> dateList) {
        OrderDO order = orderRepositoryService.getById(orderId);
        if (Objects.isNull(order)) {
            return Collections.emptyMap();
        }
        Map<Integer, Long> result = Maps.newHashMapWithExpectedSize(dateList.size());
        for (Integer date : dateList) {
            if (DateUtils.between(date, order.getRealStartTime(), order.getRealEndTime())) {
                result.put(date,orderId);
            }
        }
        return result;
    }

}
