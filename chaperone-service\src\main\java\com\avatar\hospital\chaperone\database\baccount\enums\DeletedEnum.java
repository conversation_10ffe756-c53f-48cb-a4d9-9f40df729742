package com.avatar.hospital.chaperone.database.baccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum DeletedEnum {

    /**
     * 未删除
     */
    NO(0, "未删除"),
    ;

    private final Integer status;
    private final String describe;

    DeletedEnum(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static DeletedEnum of(Integer status) {
        if (status == null) {
            return null;
        }
        for (DeletedEnum deletedEnum : values()) {
            if (status.equals(deletedEnum.getStatus())) {
                return deletedEnum;
            }
        }
        return null;
    }
}
