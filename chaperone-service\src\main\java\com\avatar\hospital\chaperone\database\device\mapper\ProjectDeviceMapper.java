package com.avatar.hospital.chaperone.database.device.mapper;

import com.avatar.hospital.chaperone.database.device.dataobject.ProjectDeviceDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 设备 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface ProjectDeviceMapper extends BaseMapper<ProjectDeviceDO> {

    List<ProjectDeviceDO> fault10(@Param("start") LocalDateTime start,
                                  @Param("end") LocalDateTime end,
                                  @Param("model") Integer model);
}
