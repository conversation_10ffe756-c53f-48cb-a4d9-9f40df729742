<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.avatar.hospital.chaperone.database.order.mapper.OrderConsumerlogMapper">

    <select id="sumPrice" resultType="java.lang.Integer">
        select sum(total_price) from t_order_consumerlog
        where deleted = 0
        and version  = 1
        and order_id = #{orderId}
        <if test="startDate != null">and `date` &gt;= #{startDate}</if>
        <if test="endDate != null">and `date` &lt;= #{endDate}</if>
    </select>
</mapper>