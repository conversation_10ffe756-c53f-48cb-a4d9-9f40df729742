package com.avatar.hospital.chaperone.service.statistics;

import com.avatar.hospital.chaperone.request.statistics.StatisticsRequest;
import com.avatar.hospital.chaperone.response.statistics.*;

/**
 *
 * 工程-统计信息
 *
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-10 13:39
 **/
public interface StatisticsService {

    /**
     * 备品备件使用情况统计 => 备件申请表，备件出资方，审核完成时间
     *      本月/本年度，各院区的备品消耗在维修上的总费用，其中的瑞新费用，院方的费用。
     * @return
     */
    StatisticsPartUseResponse getPartUse(StatisticsRequest request);

    /**
     * 应急处置事件数量 => 应急处理日志，事件日期
     *     本月，本年度
     * @return
     */
    StatisticsEmergencyHandlingResponse getEmergencyHandling(StatisticsRequest request);

    /**
     * 巡检任务完成情况 => 任务(状态),任务创建时间
     *      本月/本年度，进行中，已完成，已过期。
     * 维保任务完成情况 => 任务(状态),任务创建时间
     *      本月/本年度，进行中，已完成，已过期。
     * @return
     */
    StatisticsTaskResponse getTask(StatisticsRequest request);

    /**
     * 处置及时性排行榜（前10工单，后10工单）=> 报修单 (已完成的报修单,创建时间到完成时间 单位分)
     * @return
     */
    StatisticsRepairTimelinessTopResponse getRepairTimelinessTop(StatisticsRequest request);

    /**
     * 各类型报修单分别展示完成情况，本月/本年度 进行中，已完成
     * @return
     */
    StatisticsRepairStatusResponse getRepairStatus(StatisticsRequest request);

    /**
     * 故障排行榜 => 所有设备,报修单,报修单创建时间
     *      本月/本年度故障最少的设备-前10（比如从未被关联过报修单的即0故障），后10（关联报修单最多，故障最多的设备）
     * @return
     */
    StatisticsFaultTopResponse getFaultTop(StatisticsRequest request);

    /**
     * 各类型故障数量
     *      本月/本年度 => 报修单(系统类型),报修单创建时间
     * @return
     */
    StatisticsFaultTypeResponse getFaultType(StatisticsRequest request);
}
