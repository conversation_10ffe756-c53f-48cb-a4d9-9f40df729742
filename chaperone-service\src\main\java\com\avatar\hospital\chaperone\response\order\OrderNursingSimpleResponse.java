package com.avatar.hospital.chaperone.response.order;

import com.alibaba.fastjson.JSONArray;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 *
 * @author:sp0420
 * @Description:
 */
@Data
public class OrderNursingSimpleResponse implements Serializable {
    /**
     * 护工ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 姓名
     */
    private String name;

    public static final OrderNursingSimpleResponse build(Long id,String name) {
        OrderNursingSimpleResponse obj = new OrderNursingSimpleResponse();
        obj.setId(id);
        obj.setName(name);
        return obj;
    }


    public static List<OrderNursingSimpleResponse> of(String jsonStr) {
        if (!StrUtils.hasText(jsonStr)) {
            return Lists.newArrayList();
        }
        List<OrderNursingSimpleResponse> list = JSONArray.parseArray(jsonStr, OrderNursingSimpleResponse.class);
        return list;
    }

    public static String toJsonStr(List<OrderNursingSimpleResponse> list) {
        if (CollectionUtils.isEmpty(list)) {
            return StrUtils.EMPTY;
        }
        String jsonStr = JSONArray.toJSONString(list);
        return jsonStr;
    }
}
