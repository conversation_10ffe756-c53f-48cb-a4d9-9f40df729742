package com.avatar.hospital.chaperone.database.nursing.dataobject;

import com.avatar.hospital.chaperone.database.nursing.dataobject.base.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <p>
 * 护工-日期订单关联表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
  @TableName("t_nursing_order")
public class NursingOrderDO extends BaseDO {

    private static final long serialVersionUID = 1L;

      /**
     * 主键ID
     */
        @TableId(value = "id", type = IdType.AUTO)
      private Long id;

      /**
     * 日期,yyyyMMdd
     */
      private Integer date;

      /**
     * 护工ID
     */
      private Long nursingId;

      /**
     * 订单ID
     */
      private Long orderId;

  /**
   *  1 后端强制设置 0 不是强制设置
   */
  private Integer way;

  public Boolean isForce() {
    return Objects.equals(1, way);
  }

  public Boolean isNonForce() {
    return !isForce();
  }

}
