-- MySQL dump 10.13  Distrib 8.0.31, for Win64 (x86_64)
--
-- Host: localhost    Database: message_service
-- ------------------------------------------------------
-- Server version	5.7.36-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `t_b_account`
--

DROP TABLE IF EXISTS `t_b_account`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_b_account` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `nickname` varchar(256) NOT NULL COMMENT '用户昵称',
  `phone_number` varchar(20) NOT NULL COMMENT '电话号码',
  `avatar_url` varchar(256) COMMENT '用户头像地址',
  `password` varchar(256) NOT NULL COMMENT '登录密码',
  `status` tinyint(4) unsigned DEFAULT '1' COMMENT '启用状态 0:停用;1:启用',
  `type` tinyint(4) unsigned  COMMENT '用户类型 1:调度员;2:收银员',
  `last_login_ip` varchar(256) COMMENT '最后登录IP',
  `last_login_date` datetime(3) COMMENT '最后登录时间',
  `password_update_date` datetime(3) COMMENT '密码最后更新时间',
  `admin` tinyint(1) unsigned DEFAULT '0' COMMENT '是否管理员 0:否;1:是',
  `create_by` bigint(20) unsigned NOT NULL COMMENT '创建者',
  `update_by` bigint(20) unsigned NOT NULL COMMENT '更新者',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `deleted` bigint(20) unsigned DEFAULT '0' COMMENT '是否删除 0:未删除;其他值标识已删除',
  PRIMARY KEY (`id`),
  KEY `idx_phone_number` (`phone_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='B端用户表';
/*!40101 SET character_set_client = @saved_cs_client */;



--
-- Table structure for table `t_role`
--

DROP TABLE IF EXISTS `t_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_role` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `name` varchar(128) NOT NULL COMMENT '角色名称',
  `role_key` varchar(128) NOT NULL COMMENT '角色权限字符串',
  `status` tinyint(4) unsigned DEFAULT '1' COMMENT '启用状态 0:停用;1:启用',
  `sort` smallint(5) unsigned DEFAULT '1' COMMENT '排序',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) unsigned NOT NULL COMMENT '创建者',
  `update_by` bigint(20) unsigned NOT NULL COMMENT '更新者',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `deleted` bigint(20) unsigned DEFAULT '0' COMMENT '是否删除 0:未删除;其他值标识已删除',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
/*!40101 SET character_set_client = @saved_cs_client */;



--
-- Table structure for table `t_menu`
--

DROP TABLE IF EXISTS `t_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_menu` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `parent_id` bigint(20) unsigned NOT NULL COMMENT '上级菜单 顶级节点默认为null',
  `name` varchar(128) NOT NULL COMMENT '菜单名称',
  `front_name` varchar(128) NOT NULL COMMENT '前端菜单英文名称',
  `menu_key` varchar(128) COMMENT '菜单权限字符串',
  `component_url` varchar(128) COMMENT '组件路径',
  `type` tinyint(4) unsigned DEFAULT '1' COMMENT '层级 1:菜单;2:按钮;3:权限;',
  `icon` varchar(128) COMMENT '菜单图标',
  `status` tinyint(4) unsigned DEFAULT '1' COMMENT '启用状态 0:停用;1:启用',
  `sort` smallint(5) unsigned DEFAULT '1' COMMENT '排序',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) unsigned NOT NULL COMMENT '创建者',
  `update_by` bigint(20) unsigned NOT NULL COMMENT '更新者',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `deleted` bigint(20) unsigned DEFAULT '0' COMMENT '是否删除 0:未删除;其他值标识已删除',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';
/*!40101 SET character_set_client = @saved_cs_client */;



--
-- Table structure for table `t_account_role`
--

DROP TABLE IF EXISTS `t_account_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_account_role` (
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`account_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;



--
-- Table structure for table `t_role_menu`
--

DROP TABLE IF EXISTS `t_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_role_menu` (
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) unsigned NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `t_organization`
--

DROP TABLE IF EXISTS `t_organization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_organization` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `name` varchar(128) NOT NULL COMMENT '名称',
  `parent_id` bigint(20) unsigned COMMENT '上级组织机构ID 顶级节点默认为null',
  `liaison_name` varchar(128) COMMENT '联络人名称',
  `liaison_phone_number` varchar(128) COMMENT '联络人电话',
  `bank_account_number` varchar(128) COMMENT '银行账号',
  `level` tinyint(4) unsigned DEFAULT '1' COMMENT '层级 1:物业;2:医院;3:科室;4:床位',
  `type` tinyint(4) unsigned  COMMENT '组织结构类型 1:医院;2:部门',
  `status` tinyint(4) unsigned DEFAULT '1' COMMENT '启用状态 0:停用;1:启用',
  `sort` smallint(5) unsigned DEFAULT '1' COMMENT '排序',
  `create_by` bigint(20) unsigned NOT NULL COMMENT '创建者',
  `update_by` bigint(20) unsigned NOT NULL COMMENT '更新者',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `deleted` bigint(20) unsigned DEFAULT '0' COMMENT '是否删除 0:未删除;其他值标识已删除',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='B端组织机构表';
/*!40101 SET character_set_client = @saved_cs_client */;



--
-- Table structure for table `t_account_organization`
--

DROP TABLE IF EXISTS `t_account_organization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_account_organization` (
  `account_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `organization_id` bigint(20) unsigned NOT NULL COMMENT '组织机构ID',
  PRIMARY KEY (`account_id`,`organization_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='B端用户和组织机构关联表';
/*!40101 SET character_set_client = @saved_cs_client */;


DROP TABLE IF EXISTS worker_node;
CREATE TABLE worker_node
(
    ID          BIGINT      NOT NULL AUTO_INCREMENT COMMENT 'auto increment id',
    HOST_NAME   VARCHAR(64) NOT NULL COMMENT 'host name',
    PORT        VARCHAR(64) NOT NULL COMMENT 'port',
    TYPE        INT         NOT NULL COMMENT 'node type: ACTUAL or CONTAINER',
    LAUNCH_DATE DATE        NOT NULL COMMENT 'launch date',
    MODIFIED    TIMESTAMP   NOT NULL COMMENT 'modified time',
    CREATED     TIMESTAMP   NOT NULL COMMENT 'created time',
    PRIMARY KEY (ID)
) COMMENT='DB WorkerID Assigner for UID Generator',ENGINE = INNODB;



/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2023-04-27 16:19:19
