package com.avatar.hospital.chaperone.service.repair;

import com.avatar.hospital.chaperone.request.repair.*;
import com.avatar.hospital.chaperone.response.repair.*;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.repair.dto.RepairFormExportDTO;

import java.util.List;


/**
 * @program: hospital-chaperone
 * @description: 报修任务
 * @author: sp0372
 * @create: 2023-10-27 10:00
 **/
public interface RepairFormService {
    // B端

    /**
     * 分页查询
     */
    PageResponse<RepairFormResponse> paging(RepairFormPageRequest request);

    /**
     * 新建报修任务
     */
    RepairFormIdResponse create(RepairFormCreateRequest request);

    /**
     * 新建报修任务
     */
    RepairFormIdResponse modify(RepairFormModifyRequest request);

    /**
     * 分派执行人员
     */
    RepairFormIdResponse assignExecutor(RepairFormAssignExecutorRequest request);


    /**
     * 审核
     */
    RepairFormIdResponse auth(RepairFormAuthRequest request);

    // c端

    /**
     * 新建报修任务
     */
    RepairFormIdResponse createForC(RepairFormCreateRequest request);

    /**
     * 根据指派人员,根据指派人员,查看保修单
     */
    PageResponse<RepairFormCResponse> pagingForC(RepairFormPageCRequest request);

    /**
     * 问题说明
     */
    RepairFormDetailResponse detailForC(RepairFormDetailRequest request);

    /**
     * 导出
     *
     * @param request -
     * @return -
     */
    List<RepairFormExportDTO> exportPaging(RepairFormExportRequest request);

}
