package com.avatar.hospital.chaperone.web.validator.baccount;

import com.avatar.hospital.chaperone.database.baccount.enums.RoleStatus;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.baccount.RoleAddRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleAllocationMenuRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.RolePagingRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleUpdateRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/11
 */
public class RoleValidator {

    private static final Integer DEFAULT_STATUS = RoleStatus.ENABLE.getStatus();

    private static final Integer DEFAULT_SORT = 100;


    public static void addValidate(RoleAddRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getName(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getRoleKey(), ErrorCode.PARAMETER_ERROR);

        if (request.getStatus() != null) {
            AssertUtils.isNotNull(RoleStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        }
        else {
            request.setStatus(DEFAULT_STATUS);
        }

        if (request.getSort() != null) {
            AssertUtils.isTrue(request.getSort() >= 0, ErrorCode.PARAMETER_ERROR);
        }
        else {
            request.setSort(DEFAULT_SORT);
        }

        // 设置创建人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setCreateBy(accountId);
        request.setUpdateBy(accountId);
    }

    public static void updateValidate(RoleUpdateRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getId(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getName(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.hasText(request.getRoleKey(), ErrorCode.PARAMETER_ERROR);
        if (request.getStatus() != null) {
            AssertUtils.isNotNull(RoleStatus.of(request.getStatus()), ErrorCode.PARAMETER_ERROR);
        }
        if (request.getSort() != null) {
            AssertUtils.isTrue(request.getSort() >= 0, ErrorCode.PARAMETER_ERROR);
        }
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void deleteValidate(RoleDeleteRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.notEmpty(request.getIds(), ErrorCode.PARAMETER_ERROR);
        // 设置更新人
        Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
        request.setUpdateBy(accountId);
    }

    public static void pagingValidate(RolePagingRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageIndex(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getPageSize(), ErrorCode.PARAMETER_ERROR);
    }

    public static void allocationMenuValidate(RoleAllocationMenuRequest request) {
        AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
        AssertUtils.isNotNull(request.getRoleId(), ErrorCode.PARAMETER_ERROR);
        AssertUtils.notEmpty(request.getMenuIds(), ErrorCode.PARAMETER_ERROR);
    }
}
