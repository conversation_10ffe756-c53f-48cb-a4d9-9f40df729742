package com.avatar.hospital.chaperone.database.repair.repository.impl;

import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormDO;
import com.avatar.hospital.chaperone.database.repair.dataobject.RepairFormTaskDO;
import com.avatar.hospital.chaperone.database.repair.enums.RepairFormStatus;
import com.avatar.hospital.chaperone.database.repair.mapper.RepairFormMapper;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormRepositoryService;
import com.avatar.hospital.chaperone.database.repair.repository.RepairFormTaskRepositoryService;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.request.repair.RepairFormPageCRequest;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 报修单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
@RequiredArgsConstructor
public class RepairFormRepositoryServiceImpl extends ServiceImpl<RepairFormMapper, RepairFormDO> implements RepairFormRepositoryService {
    private final static String STATISTICS_REPAIR_STATUS_SELECT_SQL = "system_type,count(*) as status";
    private final RepairFormTaskRepositoryService repairFormTaskRepositoryService;
    @Override
    public Long add(RepairFormDO repairForm) {
        save(repairForm);
        return repairForm.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean assignExecutor(RepairFormDO newRepairFormDO, List<RepairFormTaskDO> repairFormTaskDOList) {
        AssertUtils.isTrue(updateById(newRepairFormDO), ErrorCode.UPDATE_ERROR);
        AssertUtils.isTrue(repairFormTaskRepositoryService.saveOrUpdateBatch(repairFormTaskDOList), ErrorCode.UPDATE_ERROR);
        return Boolean.TRUE;
    }

    @Override
    public Long countForC(Page<RepairFormDO> page, RepairFormPageCRequest request) {
        Integer status = request.getStatus();
        Long operator = request.getOperator();
        return baseMapper.countForC(status,operator);
    }

    @Override
    public List<RepairFormDO> listForC(Page<RepairFormDO> page, RepairFormPageCRequest request) {
        Integer status = request.getStatus();
        Long operator = request.getOperator();
        return baseMapper.listForC(status,operator,page.offset(),page.getSize());
    }

    @Override
    public List<RepairFormDO> findAllByIdList(List<Long> repairFormId) {
        if (CollUtils.isEmpty(repairFormId)) {
            return Collections.EMPTY_LIST;
        }
        LambdaQueryWrapper<RepairFormDO> queryWrapper = queryWrapper();
        queryWrapper.in(RepairFormDO::getId,repairFormId);
        List<RepairFormDO> list = list(queryWrapper);
        return list;
    }

    @Override
    public List<RepairFormDO> getRepairTimelinessTop10(LocalDateTime start, LocalDateTime end) {
        Page<RepairFormDO> paging = PageDTO.of(1, 10);
        LambdaQueryWrapper<RepairFormDO> queryWrapper = queryWrapper();
        queryWrapper.ne(RepairFormDO::getProcessTime,0L);
        queryWrapper.eq(RepairFormDO::getStatus, RepairFormStatus.FINISH.getStatus());
        queryWrapper.ge(RepairFormDO::getCreatedAt,start);
        queryWrapper.le(RepairFormDO::getCreatedAt,end);
        queryWrapper.orderByAsc(RepairFormDO::getProcessTime);
        paging = page(paging,queryWrapper);

        List<RepairFormDO> list = paging.getRecords();
        list = CollUtils.isEmpty(list) ? Collections.EMPTY_LIST : list;
        return list;
    }

    @Override
    public List<RepairFormDO> getRepairTimelinessBottom10(LocalDateTime start, LocalDateTime end) {
        Page<RepairFormDO> paging = PageDTO.of(1, 10);
        LambdaQueryWrapper<RepairFormDO> queryWrapper = queryWrapper();
        queryWrapper.ne(RepairFormDO::getProcessTime,0L);
        queryWrapper.eq(RepairFormDO::getStatus, RepairFormStatus.FINISH.getStatus());
        queryWrapper.ge(RepairFormDO::getCreatedAt,start);
        queryWrapper.le(RepairFormDO::getCreatedAt,end);
        queryWrapper.orderByDesc(RepairFormDO::getProcessTime);
        paging = page(paging,queryWrapper);

        List<RepairFormDO> list = paging.getRecords();
        list = CollUtils.isEmpty(list) ? Collections.EMPTY_LIST : list;
        return list;
    }

    @Override
    public Map<Integer, Integer> statisticsRepairStatusFinish(LocalDateTime start, LocalDateTime end) {
        QueryWrapper<RepairFormDO> queryWrapper = new QueryWrapper();
        queryWrapper.select(STATISTICS_REPAIR_STATUS_SELECT_SQL)
                .lambda()
                .ge(RepairFormDO::getCreatedAt,start)
                .le(RepairFormDO::getCreatedAt,end)
                .eq(RepairFormDO::getStatus, RepairFormStatus.FINISH.getStatus())
                .eq(RepairFormDO::getDeleted, DelUtils.NO_DELETED)
                .groupBy(RepairFormDO::getSystemType);
        List<RepairFormDO> list = list(queryWrapper);
        Map<Integer, Integer> result = list.stream()
                .collect(Collectors.toMap(RepairFormDO::getSystemType, RepairFormDO::getStatus));
        return result;
    }

    @Override
    public Map<Integer, Integer> statisticsRepairStatusForYear(Integer year) {
        LocalDateTime[] startEndOfYear = DateUtils.getStartEndOfYear(year);
        QueryWrapper<RepairFormDO> queryWrapper = new QueryWrapper();
        queryWrapper.select(STATISTICS_REPAIR_STATUS_SELECT_SQL)
                .lambda()
                .ge(RepairFormDO::getCreatedAt,startEndOfYear[0])
                .le(RepairFormDO::getCreatedAt,startEndOfYear[1])
                .eq(RepairFormDO::getDeleted, DelUtils.NO_DELETED)
                .groupBy(RepairFormDO::getSystemType);
        List<RepairFormDO> list = list(queryWrapper);
        Map<Integer, Integer> result = list.stream()
                .collect(Collectors.toMap(RepairFormDO::getSystemType, RepairFormDO::getStatus));
        return result;
    }

    @Override
    public Map<Integer, Integer> statisticsRepairStatusWork(LocalDateTime start, LocalDateTime end) {
        QueryWrapper<RepairFormDO> queryWrapper = new QueryWrapper();
        queryWrapper.select(STATISTICS_REPAIR_STATUS_SELECT_SQL)
                .lambda()
                .ge(RepairFormDO::getCreatedAt,start)
                .le(RepairFormDO::getCreatedAt,end)
                .ne(RepairFormDO::getStatus, RepairFormStatus.FINISH.getStatus())
                .eq(RepairFormDO::getDeleted, DelUtils.NO_DELETED)
                .groupBy(RepairFormDO::getSystemType);
        List<RepairFormDO> list = list(queryWrapper);
        Map<Integer, Integer> result = list.stream()
                .collect(Collectors.toMap(RepairFormDO::getSystemType, RepairFormDO::getStatus));
        return result;
    }

    @Override
    public Map<Integer, Integer> statisticsRepairStatusForMonth(Integer month) {
        LocalDateTime[] startEndOfMonth = DateUtils.getStartEndOfMonth(month);
        QueryWrapper<RepairFormDO> queryWrapper = new QueryWrapper();
        queryWrapper.select(STATISTICS_REPAIR_STATUS_SELECT_SQL)
                .lambda()
                .ge(RepairFormDO::getCreatedAt,startEndOfMonth[0])
                .le(RepairFormDO::getCreatedAt,startEndOfMonth[1])
                .eq(RepairFormDO::getDeleted, DelUtils.NO_DELETED)
                .groupBy(RepairFormDO::getSystemType);
        List<RepairFormDO> list = list(queryWrapper);
        Map<Integer, Integer> result = list.stream()
                .collect(Collectors.toMap(RepairFormDO::getSystemType, RepairFormDO::getStatus));
        return result;
    }

    @Override
    public Map<Long, RepairFormDO> getByIdList(List<Long> formIdList) {
        if(CollUtils.isEmpty(formIdList)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<RepairFormDO> queryWrapper = queryWrapper();
        queryWrapper.in(RepairFormDO::getId,formIdList);
        List<RepairFormDO> list = list(queryWrapper);
        if(CollUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<Long, RepairFormDO> idRef = list.stream()
                .collect(Collectors.toMap(RepairFormDO::getId, Function.identity(), (k1, k2) -> k1));
        return idRef;
    }

    private LambdaQueryWrapper<RepairFormDO> queryWrapper() {
        LambdaQueryWrapper<RepairFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairFormDO::getDeleted, DelUtils.NO_DELETED);
        return queryWrapper;
    }

    private LambdaUpdateWrapper<RepairFormDO> updateWrapper() {
        LambdaUpdateWrapper<RepairFormDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RepairFormDO::getDeleted, DelUtils.NO_DELETED);
        return updateWrapper;
    }
}
