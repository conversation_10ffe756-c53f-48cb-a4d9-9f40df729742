package com.avatar.hospital.chaperone.builder.baccount;

import com.avatar.hospital.chaperone.database.baccount.dataobject.MenuDO;
import com.avatar.hospital.chaperone.request.baccount.MenuAddRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuPagingRequest;
import com.avatar.hospital.chaperone.request.baccount.MenuUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuPagingResponse;
import com.avatar.hospital.chaperone.response.baccount.MenuTreeResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/11
 */
public class MenuBuilder {

    public static MenuDO buildMenuDO(MenuAddRequest request, Integer parentLevel) {
        if (request == null || parentLevel == null) {
            return null;
        }
        MenuDO menuDO = new MenuDO();
        menuDO.setName(request.getName());
        menuDO.setFrontName(request.getFrontName());
        menuDO.setParentId(request.getParentId());
        menuDO.setComponentUrl(request.getComponentUrl());
        menuDO.setIcon(request.getIcon());
        menuDO.setMenuKey(request.getMenuKey());
        menuDO.setStatus(request.getStatus());
        menuDO.setType(parentLevel + 1);
        menuDO.setSort(request.getSort());
        menuDO.setRemark(request.getRemark());
        menuDO.setCreateBy(request.getCreateBy());
        menuDO.setUpdateBy(request.getUpdateBy());
        return menuDO;
    }

    public static MenuDO buildMenuDO(MenuUpdateRequest request) {
        if (request == null) {
            return null;
        }
        MenuDO menuDO = new MenuDO();
        menuDO.setId(request.getId());
        menuDO.setName(request.getName());
        menuDO.setFrontName(request.getFrontName());
        menuDO.setComponentUrl(request.getComponentUrl());
        menuDO.setIcon(request.getIcon());
        menuDO.setMenuKey(request.getMenuKey());
        menuDO.setStatus(request.getStatus());
        menuDO.setSort(request.getSort());
        menuDO.setRemark(request.getRemark());
        return menuDO;
    }

    public static MenuDO buildMenuDO(MenuPagingRequest request) {
        if (request == null) {
            return null;
        }
        MenuDO menuDO = new MenuDO();
        menuDO.setName(request.getName());
        return menuDO;
    }

    public static PageResponse<MenuPagingResponse> buildMenuPagingResponse(PageResponse<MenuDO> pageResponse) {
        if (pageResponse == null) {
            return null;
        }
        PageResponse<MenuPagingResponse> response = new PageResponse<>();
        response.setTotal(pageResponse.getTotal());
        response.setCurrent(pageResponse.getCurrent());
        response.setSize(pageResponse.getSize());
        response.setRecords(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(pageResponse.getRecords())) {
            response.setRecords(buildMenuPagingResponse(pageResponse.getRecords()));
        }
        return response;
    }

    public static List<MenuPagingResponse> buildMenuPagingResponse(List<MenuDO> menuList) {
        if (CollectionUtils.isEmpty(menuList)) {
            return Collections.emptyList();
        }
        List<MenuPagingResponse> menuPagingResponse = Lists.newLinkedList();
        for (MenuDO menuDO : menuList) {
            menuPagingResponse.add(buildMenuPagingResponse(menuDO));
        }
        return menuPagingResponse;
    }

    public static MenuPagingResponse buildMenuPagingResponse(MenuDO menuDO) {
        if (menuDO == null) {
            return null;
        }
        MenuPagingResponse menuPagingResponse = new MenuPagingResponse();
        menuPagingResponse.setId(menuDO.getId());
        menuPagingResponse.setName(menuDO.getName());
        menuPagingResponse.setFrontName(menuDO.getFrontName());
        menuPagingResponse.setParentId(menuDO.getParentId());
        menuPagingResponse.setComponentUrl(menuDO.getComponentUrl());
        menuPagingResponse.setIcon(menuDO.getIcon());
        menuPagingResponse.setMenuKey(menuDO.getMenuKey());
        menuPagingResponse.setType(menuDO.getType());
        menuPagingResponse.setStatus(menuDO.getStatus());
        menuPagingResponse.setSort(menuDO.getSort());
        menuPagingResponse.setRemark(menuDO.getRemark());
        menuPagingResponse.setCreateBy(menuDO.getCreateBy());
        menuPagingResponse.setUpdateBy(menuDO.getUpdateBy());
        menuPagingResponse.setCreatedAt(menuDO.getCreatedAt());
        menuPagingResponse.setUpdatedAt(menuDO.getUpdatedAt());
        return menuPagingResponse;
    }

    public static MenuDetailResponse buildMenuDetailResponse(MenuDO menuDO) {
        if (menuDO == null) {
            return null;
        }
        MenuDetailResponse menuDetailResponse = new MenuDetailResponse();
        menuDetailResponse.setId(menuDO.getId());
        menuDetailResponse.setName(menuDO.getName());
        menuDetailResponse.setFrontName(menuDO.getFrontName());
        menuDetailResponse.setParentId(menuDO.getParentId());
        menuDetailResponse.setComponentUrl(menuDO.getComponentUrl());
        menuDetailResponse.setIcon(menuDO.getIcon());
        menuDetailResponse.setMenuKey(menuDO.getMenuKey());
        menuDetailResponse.setType(menuDO.getType());
        menuDetailResponse.setStatus(menuDO.getStatus());
        menuDetailResponse.setSort(menuDO.getSort());
        menuDetailResponse.setRemark(menuDO.getRemark());
        menuDetailResponse.setCreateBy(menuDO.getCreateBy());
        menuDetailResponse.setUpdateBy(menuDO.getUpdateBy());
        menuDetailResponse.setCreatedAt(menuDO.getCreatedAt());
        menuDetailResponse.setUpdatedAt(menuDO.getUpdatedAt());
        return menuDetailResponse;
    }

    public static List<MenuTreeResponse> buildMenuTreeResponseList(List<MenuDO> menuList) {
        if (CollectionUtils.isEmpty(menuList)) {
            return Collections.emptyList();
        }
        List<MenuTreeResponse> menuTreeResponseList = Lists.newArrayListWithCapacity(menuList.size());
        menuList.forEach(menu -> menuTreeResponseList.add(buildMenuTreeResponse(menu)));
        return menuTreeResponseList;
    }

    public static MenuTreeResponse buildMenuTreeResponse(MenuDO menuDO) {
        if (menuDO == null) {
            return null;
        }
        MenuTreeResponse menuTreeResponse = new MenuTreeResponse();
        menuTreeResponse.setId(menuDO.getId());
        menuTreeResponse.setName(menuDO.getName());
        menuTreeResponse.setFrontName(menuDO.getFrontName());
        menuTreeResponse.setParentId(menuDO.getParentId());
        menuTreeResponse.setComponentUrl(menuDO.getComponentUrl());
        menuTreeResponse.setIcon(menuDO.getIcon());
        menuTreeResponse.setMenuKey(menuDO.getMenuKey());
        menuTreeResponse.setType(menuDO.getType());
        menuTreeResponse.setStatus(menuDO.getStatus());
        menuTreeResponse.setSort(menuDO.getSort());
        menuTreeResponse.setRemark(menuDO.getRemark());
        menuTreeResponse.setCreateBy(menuDO.getCreateBy());
        menuTreeResponse.setUpdateBy(menuDO.getUpdateBy());
        menuTreeResponse.setCreatedAt(menuDO.getCreatedAt());
        menuTreeResponse.setUpdatedAt(menuDO.getUpdatedAt());
        return menuTreeResponse;
    }

    public static List<MenuTreeResponse> buildMenuTreeChildrenList(List<MenuTreeResponse> menuTreeResponseList) {
        if (CollectionUtils.isEmpty(menuTreeResponseList)) {
            return Collections.emptyList();
        }
        List<MenuTreeResponse> resultMenuTreeResponseList = Lists.newLinkedList();

        for (MenuTreeResponse menuTreeResponse1 : menuTreeResponseList) {
            // 添加顶级节点
            if (menuTreeResponse1.getParentId() == null) {
                resultMenuTreeResponseList.add(menuTreeResponse1);
            }
            for (MenuTreeResponse menuTreeResponse2 : menuTreeResponseList) {
                if (Objects.equals(menuTreeResponse2.getParentId(), menuTreeResponse1.getId())) {
                    if (CollectionUtils.isEmpty(menuTreeResponse1.getChildrenList())) {
                        menuTreeResponse1.setChildrenList(Lists.newLinkedList());
                    }
                    menuTreeResponse1.getChildrenList().add(menuTreeResponse2);
                }
            }
        }
        return resultMenuTreeResponseList;
    }
}
