package com.avatar.hospital.chaperone.web.controller.baccount;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.enums.ErrorCode;
import com.avatar.hospital.chaperone.properties.RoleProperties;
import com.avatar.hospital.chaperone.request.baccount.RoleAddRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleAllocationMenuRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleDeleteRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleMenuRequest;
import com.avatar.hospital.chaperone.request.baccount.RolePagingRequest;
import com.avatar.hospital.chaperone.request.baccount.RoleUpdateRequest;
import com.avatar.hospital.chaperone.response.PageResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleAddResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleAllocationMenuResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleDeleteResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleDetailResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleMenuResponse;
import com.avatar.hospital.chaperone.response.baccount.RolePagingResponse;
import com.avatar.hospital.chaperone.response.baccount.RoleUpdateResponse;
import com.avatar.hospital.chaperone.service.baccount.RoleService;
import com.avatar.hospital.chaperone.template.TemplateProcess;
import com.avatar.hospital.chaperone.template.exception.BusinessException;
import com.avatar.hospital.chaperone.template.util.AssertUtils;
import com.avatar.hospital.chaperone.web.utils.WebAccountUtils;
import com.avatar.hospital.chaperone.web.validator.baccount.RoleValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 角色接口
 *
 * <AUTHOR>
 * @since 2023/10/9
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/web/role")
public class RoleController {

    private final RoleService roleService;
    private final RoleProperties roleProperties;

    /**
     * 添加角色
     *
     * @param request -
     * @return -
     */
    @PostMapping(value = "")
    public SingleResponse<RoleAddResponse> add(@RequestBody RoleAddRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("RoleController add request:{}", JSON.toJSONString(request));
            RoleValidator.addValidate(request);
            return roleService.add(request);
        });
    }

    /**
     * 更新角色
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "")
    public SingleResponse<RoleUpdateResponse> update(@RequestBody RoleUpdateRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("RoleController update request:{}", JSON.toJSONString(request));
            RoleValidator.updateValidate(request);
            return roleService.update(request);
        });
    }

    /**
     * 删除角色
     *
     * @param request -
     * @return -
     */
    @PutMapping(value = "/delete")
    public SingleResponse<RoleDeleteResponse> delete(@RequestBody RoleDeleteRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("RoleController delete request:{}", JSON.toJSONString(request));
            RoleValidator.deleteValidate(request);
            return roleService.delete(request);
        });
    }

    /**
     * 角色分配权限
     *
     * @param request -
     * @return -
     */
    @PostMapping(value = "/allocation-menu")
    public SingleResponse<RoleAllocationMenuResponse> allocationMenu(@RequestBody RoleAllocationMenuRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("RoleController allocationMenu request:{}", JSON.toJSONString(request));
            RoleValidator.allocationMenuValidate(request);
            return roleService.allocationMenu(request);
        });
    }

    /**
     * 分页查询角色
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/paging")
    public SingleResponse<PageResponse<RolePagingResponse>> paging(RolePagingRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("RoleController paging request:{}", JSON.toJSONString(request));
            RoleValidator.pagingValidate(request);
            return roleService.paging(request);
        });
    }

    /**
     * 查询角色详情
     *
     * @param roleId -
     * @return -
     */
    @GetMapping(value = "/{id}")
    public SingleResponse<RoleDetailResponse> detail(@PathVariable("id") Long roleId) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("RoleController detail request:{}", roleId);
            AssertUtils.isNotNull(roleId, ErrorCode.PARAMETER_ERROR);
            return roleService.detail(roleId);
        });
    }

    /**
     * 查询角色关联菜单权限
     *
     * @param request -
     * @return -
     */
    @GetMapping(value = "/menu")
    public SingleResponse<RoleMenuResponse> menu(RoleMenuRequest request) {
        return TemplateProcess.doProcess(log, () -> {
            log.info("RoleController menu request:{}", JSON.toJSONString(request));
            AssertUtils.isNotNull(request, ErrorCode.PARAMETER_ERROR);
            AssertUtils.isNotNull(request.getRoleId(), ErrorCode.PARAMETER_ERROR);
            return roleService.menu(request);
        });
    }

    /**
     * 知识库创建文件夹权限校验
     *
     * @return -
     */
    @GetMapping(value = "/mkdir-permission-check")
    public SingleResponse<Boolean> mkdirPermissionCheck() {
        return TemplateProcess.doProcess(log, () -> {
            Long accountId = WebAccountUtils.getCurrentAccountIdAndThrow();
            log.info("RoleController mkdirPermissionCheck accountId:{}", accountId);
            if (StpUtil.hasRole(roleProperties.getMkdirRoleKey())) {
                return Boolean.TRUE;
            }
            throw BusinessException.of(ErrorCode.WEB_ACCOUNT_NOT_PERMISSION_ERROR);
        });
    }

}
