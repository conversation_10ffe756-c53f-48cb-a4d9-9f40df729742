package com.avatar.hospital.chaperone.utils;

import java.util.function.Supplier;

import com.google.common.base.Throwables;
import org.slf4j.Logger;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-31 11:48
 **/
public class ExceptionUtil {

    public final static void execute(Logger logger,Supplier supplier) {
        try {
            supplier.get();
        }catch (Exception e) {
            logger.error("ExceptionUtil error {}", Throwables.getStackTraceAsString(e));
        }
    }
}
