package com.avatar.hospital.chaperone.database.code.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/26 19:55
 */
@Getter
public enum CodeBizType {
//1-设备，2-备件批次，3-备件入库审批单，4-巡检计划，5-维保计划，6-报修单,7-备件使用申请单

    NONE(-1, "未知"),
    SB(1, "设备"),
    BJ(2, "备件批次"),
    BJRK(3, "备件入库审批单"),
    XJJH(4, "巡检计划"),
    WBJH(5, "维保计划"),
    BXD(6, "报修单"),
    BJSY(7, "备件使用申请单"),
    XJRW(41, "巡检任务"),
    WBRW(51, "维保任务"),
    ;

    private final Integer code;

    private final String describe;


    CodeBizType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static CodeBizType of(Integer code) {
        if (code == null) {
            return NONE;
        }
        for (CodeBizType type : values()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return NONE;
    }
}
