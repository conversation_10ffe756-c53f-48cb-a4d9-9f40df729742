package com.avatar.hospital.chaperone.database.baccount.enums;

import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Getter
public enum OrganizationType {

    /**
     * 医院
     */
    HOSPITAL(1, "医院"),

    /**
     * 部门
     */
    DEPARTMENT(2, "部门"),

    ;

    private final Integer type;

    private final String describe;


    OrganizationType(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }

    public static OrganizationType of(Integer type) {
        if (type == null) {
            return null;
        }
        for (OrganizationType organizationType : values()) {
            if (type.equals(organizationType.getType())) {
                return organizationType;
            }
        }
        return null;
    }
}
