package com.avatar.hospital.chaperone.database.order.repository;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单-套餐关联表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface OrderItemRepositoryService extends IService<OrderItemDO> {

    /**
     * 根据订单查询所有套餐信息
     * @param orderId
     * @return
     */
    List<OrderItemDO> getByOrderId(Long orderId);

    /**
     * 根据订单查询所有套餐信息
     * @param orderIdList
     * @return
     */
    List<OrderItemDO> getByOrderIdList(List<Long> orderIdList);

    /**
     * 根据订单查询所有套餐信息
     *    且把值设置为删除
     * @param orderId
     * @return
     */
    List<OrderItemDO> getSetDelByOrderId(Long orderId,Long delVersion );

    /**
     * 消费涉及的订单商品数据
     * @return
     */
    List<OrderItemDO> listByConsumerLog(List<Long> itemIdList,
                                        Integer dateHour);

    /**
     * 校验是否设置服务套餐
     * @param orderId
     */
    void checkExist(Long orderId);

    /**
     * 货物套餐名称
     * @param orderIds
     * @return
     */
    Map<Long, String> listByOrderIdList(List<Long> orderIds);
}
